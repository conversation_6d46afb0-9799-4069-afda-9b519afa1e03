# Clerk Authentication (Dummy Keys for Development)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuYnJlYWtkb3duLmFkJA
CLERK_SECRET_KEY=**************************************************
CLERK_WEBHOOK_SECRET=whsec_AHHQr7s57HfHoz2LYHbe3ZnLKt7tbEd2
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/studio
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/studio

#Youtube API 
NEXT_PUBLIC_YOUTUBE_API_KEY=AIzaSyB95Z1RkjqgMnWmNFaevQZIAHR-6F_1VoQ

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://elossghirdivbobfycob.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.EuIuMDBRBPx9qIVkL8qodDeS9mkb-iFgA9PphuQH9pI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8

# Gemini AI API (from <EMAIL> GCP account)
GEMINI_API_KEY=AIzaSyDopEjXf6HzDsQ_Q-2sRAGs1rCtOdChXDo
NEXT_PUBLIC_GEMINI_API_KEY=AIzaSyDopEjXf6HzDsQ_Q-2sRAGs1rCtOdChXDo

# Vertex AI (from <EMAIL> GCP account)
VERTEX_AI_PROJECT_ID=gen-lang-client-**********
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_SERVICE_ACCOUNT_EMAIL=<EMAIL>

oldkey=AIzaSyBoI6_LcEKmsmLVJXE7Q_Fe72WmwlqOL9M

# Lemon Squeezy (Dummy Keys for Development)
LEMON_SQUEEZY_API_KEY=dummy_lemon_squeezy_api_key_replace_with_real_api_key
LEMON_SQUEEZY_STORE_ID=12345
LEMON_SQUEEZY_WEBHOOK_SECRET=dummy_webhook_secret_replace_with_real_webhook_secret
NEXT_PUBLIC_LEMON_SQUEEZY_STARTER_VARIANT_ID=123456
NEXT_PUBLIC_LEMON_SQUEEZY_PRO_VARIANT_ID=123457
NEXT_PUBLIC_LEMON_SQUEEZY_ENTERPRISE_VARIANT_ID=123458

NEXT_PUBLIC_APP_URL=https://www.breakdown.ad
NEXT_PUBLIC_API_URL=https://www.breakdown.ad/api

# Redis Completely Disabled - No environment variables needed

