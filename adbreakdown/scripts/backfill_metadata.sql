UPDATE public.ad_analyses
SET 
  brand = marketing_analysis->>'brand',
  product_category = marketing_analysis->>'product_category',
  parent_entity = marketing_analysis->>'parent_entity',
  campaign_category = marketing_analysis->>'campaign_category',
  duration_seconds = (marketing_analysis->>'duration')::integer,
  geography = marketing_analysis->>'geography',
  agency = marketing_analysis->>'agency',
  launch_date = (marketing_analysis->>'launch_date')::date
WHERE 
  marketing_analysis IS NOT NULL 
  AND brand IS NULL; -- only update rows that haven't been processed