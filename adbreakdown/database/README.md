# breakdown.ad Database Directory

This directory contains all database-related files for the breakdown.ad project.

## Directory Structure

```
/database/
├── README.md                   # This file
├── current_schema.sql          # Current database schema (reference)
├── schema.sql                  # Full database dump for initial setup
├── update_schema.sh            # Script to update schema files
├── performance-indexes.sql     # Performance optimization indexes
├── rls-policies.sql            # Row Level Security policies
├── test-data.sql               # Test data for development
├── migrations/                 # Version-controlled database migrations
└── functions/                  # SQL functions (e.g., credit management)
```

## File Descriptions

- **`current_schema.sql`**: A reference file showing the current table structures.
- **`schema.sql`**: The full database schema intended for initial project setup.
- **`migrations/`**: Contains sequential SQL files that modify the database schema over time. This is the source of truth for schema changes after the initial setup.
- **`functions/`**: Contains reusable SQL functions.
- **`performance-indexes.sql`**: Contains additional indexes to improve query performance.
- **`rls-policies.sql`**: Defines the Row Level Security policies to protect user data.

## Database Tables

### V1.0 Tables (Implemented)
- **users**: Core user data, synced with Clerk.
- **profiles**: Manages user credits and subscription status.
- **ad_analyses**: The primary table for storing analysis jobs and their core results.
- **analysis_reports**: Stores detailed content for generated reports (e.g., marketing copy, social posts).
- **report_types**: A lookup table for different report types and their credit costs.
- **credit_usage**: A log of all credit transactions.
- **sentiment_analyses** & **emotion_timeline_events**: Data for detailed emotion analysis.
- **daily_showcases**: Manages the featured content on the public site.
- **admin_submissions**: For internal use to request ad analyses.

### V2.0 Tables (In Development)
- **frameworks**: Definitions for marketing frameworks (AIDA, PAS, etc.).
- **analysis_frameworks**: Links analyses to the frameworks applied, including confidence scores.
- **competitive_analyses**: Manages batch jobs for comparing multiple ads.
- **competitor_results**: Stores the analysis results for each competitor in a comparison.

## Usage

### Initial Setup
1.  Run the `database/schema.sql` file in your Supabase SQL Editor to create the initial database structure.
2.  Run the `database/rls-policies.sql` file to apply security policies.
3.  Run the files in `database/functions/` to create necessary SQL functions.

### Schema Migrations
For subsequent changes, create a new versioned migration file in the `database/migrations/` directory and apply it manually.

### Edge Functions
Note: TypeScript Edge Functions (`run-ad-analysis`, etc.) are located in the root `/supabase/functions/` directory and are deployed via the Supabase CLI.