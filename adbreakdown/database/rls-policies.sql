-- Row Level Security (RLS) Policies for AdBreakdown
-- Run this after the main schema has been created

-- Users table policies
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (clerk_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (clerk_id = auth.jwt() ->> 'sub');

-- Profiles table policies  
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
        )
    );

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
        )
    );

-- Ad analyses table policies
CREATE POLICY "Users can view own analyses" ON ad_analyses
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
        )
    );

CREATE POLICY "Users can create own analyses" ON ad_analyses
    FOR INSERT WITH CHECK (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
        )
    );

CREATE POLICY "Users can update own analyses" ON ad_analyses
    FOR UPDATE USING (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
        )
    );

-- Analysis reports table policies
CREATE POLICY "Users can view own analysis reports" ON analysis_reports
    FOR SELECT USING (
        analysis_id IN (
            SELECT id FROM ad_analyses WHERE user_id IN (
                SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
            )
        )
    );

CREATE POLICY "Users can create own analysis reports" ON analysis_reports
    FOR INSERT WITH CHECK (
        analysis_id IN (
            SELECT id FROM ad_analyses WHERE user_id IN (
                SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
            )
        )
    );

CREATE POLICY "Users can update own analysis reports" ON analysis_reports
    FOR UPDATE USING (
        analysis_id IN (
            SELECT id FROM ad_analyses WHERE user_id IN (
                SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
            )
        )
    );

-- Credit usage table policies
CREATE POLICY "Users can view own credit usage" ON credit_usage
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
        )
    );

CREATE POLICY "Users can create own credit usage records" ON credit_usage
    FOR INSERT WITH CHECK (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
        )
    );

-- Report types table (public read access)
CREATE POLICY "Anyone can read report types" ON report_types
    FOR SELECT USING (true);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
-- V2.0 Frameworks table (public read access)
CREATE POLICY "Anyone can read frameworks" ON frameworks
    FOR SELECT USING (true);

-- V2.0 Analysis Frameworks table policies
CREATE POLICY "Users can view own analysis frameworks" ON analysis_frameworks
    FOR SELECT USING (
        analysis_id IN (
            SELECT id FROM ad_analyses WHERE user_id IN (
                SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
            )
        )
    );

CREATE POLICY "Users can create own analysis frameworks" ON analysis_frameworks
    FOR INSERT WITH CHECK (
        analysis_id IN (
            SELECT id FROM ad_analyses WHERE user_id IN (
                SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
            )
        )
    );

-- V2.0 Competitive Analyses table policies
CREATE POLICY "Users can view own competitive analyses" ON competitive_analyses
    FOR SELECT USING (
        primary_analysis_id IN (
            SELECT id FROM ad_analyses WHERE user_id IN (
                SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
            )
        )
    );

CREATE POLICY "Users can create own competitive analyses" ON competitive_analyses
    FOR INSERT WITH CHECK (
        primary_analysis_id IN (
            SELECT id FROM ad_analyses WHERE user_id IN (
                SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
            )
        )
    );

-- V2.0 Competitor Results table policies
CREATE POLICY "Users can view own competitor results" ON competitor_results
    FOR SELECT USING (
        competitive_analysis_id IN (
            SELECT id FROM competitive_analyses WHERE primary_analysis_id IN (
                SELECT id FROM ad_analyses WHERE user_id IN (
                    SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
                )
            )
        )
    );

CREATE POLICY "Users can create own competitor results" ON competitor_results
    FOR INSERT WITH CHECK (
        competitive_analysis_id IN (
            SELECT id FROM competitive_analyses WHERE primary_analysis_id IN (
                SELECT id FROM ad_analyses WHERE user_id IN (
                    SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'
                )
            )
        )
    );