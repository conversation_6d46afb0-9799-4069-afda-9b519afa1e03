-- Performance indexes for public analysis caching
-- Run these commands one by one in Supabase SQL editor

-- Index for public analysis slug lookups
CREATE INDEX IF NOT EXISTS idx_ad_analyses_public_slug 
ON ad_analyses(slug) 
WHERE is_public = true;

-- Index for public analysis listing with status filter
CREATE INDEX IF NOT EXISTS idx_ad_analyses_public_status 
ON ad_analyses(is_public, status, created_at) 
WHERE is_public = true AND status = 'completed';

-- Index for UUID lookups on public analyses
CREATE INDEX IF NOT EXISTS idx_ad_analyses_public_id 
ON ad_analyses(id) 
WHERE is_public = true;

-- Index for analysis reports lookup
CREATE INDEX IF NOT EXISTS idx_analysis_reports_analysis_id_status 
ON analysis_reports(analysis_id, status);

-- Index for faster user lookups
CREATE INDEX IF NOT EXISTS idx_users_clerk_id 
ON users(clerk_id);

-- Composite index for private analysis ownership checks
CREATE INDEX IF NOT EXISTS idx_ad_analyses_user_slug 
ON ad_analyses(user_id, slug);

-- Index for sentiment analysis lookups
CREATE INDEX IF NOT EXISTS idx_sentiment_analyses_ad_analysis_id 
ON sentiment_analyses(ad_analysis_id);
-- V2.0 Framework Intelligence Indexes
CREATE INDEX IF NOT EXISTS idx_analysis_frameworks_analysis_id ON public.analysis_frameworks(analysis_id);
CREATE INDEX IF NOT EXISTS idx_analysis_frameworks_framework_id ON public.analysis_frameworks(framework_id);

-- V2.0 Competitive Analysis Indexes
CREATE INDEX IF NOT EXISTS idx_competitive_analyses_primary_analysis_id ON public.competitive_analyses(primary_analysis_id);
CREATE INDEX IF NOT EXISTS idx_competitor_results_competitive_analysis_id ON public.competitor_results(competitive_analysis_id);