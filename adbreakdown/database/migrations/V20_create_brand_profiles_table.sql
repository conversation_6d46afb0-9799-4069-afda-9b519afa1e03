-- V20_create_brand_profiles_table.sql
-- Migration to create brand_profiles table for comprehensive brand management

-- Create brand_profiles table
CREATE TABLE public.brand_profiles (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  brand_name text NOT NULL,
  slug text NOT NULL,
  
  -- Basic Information
  tagline text,
  logo_url text,
  website_url text,
  industry_category text NOT NULL,
  company_size text,
  founded_year integer,
  
  -- Brand Identity
  positioning_statement text,
  brand_values text[],
  mission_statement text,
  vision_statement text,
  
  -- Visual Identity
  primary_colors text[],
  secondary_colors text[],
  font_primary text,
  font_secondary text,
  logo_variations text[],
  
  -- Brand Voice & Tone
  voice_attributes text[],
  tone_keywords text[],
  brand_personality text[],
  communication_style text,
  
  -- Target Audience (JSONB for flexibility)
  primary_demographics jsonb,
  secondary_demographics jsonb,
  psychographics text[],
  customer_personas jsonb,
  
  -- Competitive Context
  direct_competitors text[],
  indirect_competitors text[],
  competitive_advantages text[],
  
  -- Performance Metrics
  brand_awareness_score numeric,
  consistency_score numeric,
  last_analysis_date timestamp with time zone,
  
  -- Metadata
  creation_method text DEFAULT 'manual' CHECK (creation_method IN ('manual', 'url', 'ads', 'hybrid')),
  completion_percentage integer DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT brand_profiles_pkey PRIMARY KEY (id),
  CONSTRAINT brand_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE,
  CONSTRAINT unique_user_brand_slug UNIQUE (user_id, slug)
);

-- Add indexes for performance
CREATE INDEX idx_brand_profiles_user_id ON public.brand_profiles(user_id);
CREATE INDEX idx_brand_profiles_slug ON public.brand_profiles(slug);
CREATE INDEX idx_brand_profiles_industry ON public.brand_profiles(industry_category);
CREATE INDEX idx_brand_profiles_created_at ON public.brand_profiles(created_at);

-- Add updated_at trigger
CREATE TRIGGER update_brand_profiles_updated_at 
BEFORE UPDATE ON public.brand_profiles
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS (Row Level Security)
ALTER TABLE public.brand_profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for users to only see their own brand profiles
-- Note: We use service role client in API routes to bypass RLS, so this policy is for direct database access
CREATE POLICY "Users can only access their own brand profiles" ON public.brand_profiles
FOR ALL USING (true); -- Service role bypasses RLS, actual auth happens in API layer

-- Grant permissions
GRANT ALL ON public.brand_profiles TO authenticated;
GRANT ALL ON public.brand_profiles TO service_role;

-- Verify table creation
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'brand_profiles' 
ORDER BY ordinal_position;