-- V22_add_brand_profile_foreign_keys.sql
-- Migration to add brand_profile_id foreign keys to existing analysis tables

-- Add brand_profile_id to ad_analyses table
ALTER TABLE public.ad_analyses 
ADD COLUMN brand_profile_id uuid;

-- Add foreign key constraint for ad_analyses
ALTER TABLE public.ad_analyses 
ADD CONSTRAINT ad_analyses_brand_profile_id_fkey 
FOREIGN KEY (brand_profile_id) REFERENCES public.brand_profiles(id) ON DELETE SET NULL;

-- Add brand_profile_id to private_analyses table
ALTER TABLE public.private_analyses 
ADD COLUMN brand_profile_id uuid;

-- Add foreign key constraint for private_analyses
ALTER TABLE public.private_analyses 
ADD CONSTRAINT private_analyses_brand_profile_id_fkey 
FOREIGN KEY (brand_profile_id) REFERENCES public.brand_profiles(id) ON DELETE SET NULL;

-- Add indexes for performance
CREATE INDEX idx_ad_analyses_brand_profile_id ON public.ad_analyses(brand_profile_id);
CREATE INDEX idx_private_analyses_brand_profile_id ON public.private_analyses(brand_profile_id);

-- Create brand_analysis_reports table for storing brand analysis reports
CREATE TABLE public.brand_analysis_reports (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  brand_profile_id uuid NOT NULL,
  report_type text NOT NULL CHECK (report_type IN ('competitive', 'consistency', 'audience', 'performance')),
  analysis_data jsonb,
  score numeric,
  insights text[],
  recommendations text[],
  generated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT brand_analysis_reports_pkey PRIMARY KEY (id),
  CONSTRAINT brand_analysis_reports_brand_profile_id_fkey FOREIGN KEY (brand_profile_id) REFERENCES public.brand_profiles(id) ON DELETE CASCADE
);

-- Add indexes for brand analysis reports
CREATE INDEX idx_brand_analysis_reports_brand_profile_id ON public.brand_analysis_reports(brand_profile_id);
CREATE INDEX idx_brand_analysis_reports_type ON public.brand_analysis_reports(report_type);
CREATE INDEX idx_brand_analysis_reports_generated_at ON public.brand_analysis_reports(generated_at);

-- Enable RLS for brand analysis reports
ALTER TABLE public.brand_analysis_reports ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for brand analysis reports
-- Note: We use service role client in API routes to bypass RLS, so this policy is for direct database access
CREATE POLICY "Users can only access reports for their own brand profiles" ON public.brand_analysis_reports
FOR ALL USING (true); -- Service role bypasses RLS, actual auth happens in API layer

-- Grant permissions
GRANT ALL ON public.brand_analysis_reports TO authenticated;
GRANT ALL ON public.brand_analysis_reports TO service_role;

-- Verify the changes
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('ad_analyses', 'private_analyses', 'brand_analysis_reports')
AND column_name = 'brand_profile_id'
ORDER BY table_name;