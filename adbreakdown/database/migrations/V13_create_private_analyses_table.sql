-- V13: Create Dedicated Private Analyses Table
-- This migration creates a separate table for private/pre-launch analyses

-- Create private_analyses table with optimized structure for private analysis workflow
CREATE TABLE IF NOT EXISTS private_analyses (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Video Information
    youtube_url text NOT NULL,
    youtube_video_id varchar(20) NOT NULL,
    slug varchar(255) NOT NULL UNIQUE,
    title text,
    inferred_brand text,
    duration_seconds integer,
    thumbnail_url text,
    
    -- Analysis Status and Processing
    status varchar(20) DEFAULT 'pending', -- pending, processing, completed, failed
    analysis_type varchar(20) DEFAULT 'private', -- private, pre-launch, beta
    
    -- Core Analysis Results (focused on pre-launch optimization)
    marketing_analysis text, -- Main pre-launch analysis content
    optimization_recommendations text, -- Specific optimization suggestions
    pre_launch_score integer, -- Overall readiness score (1-100)
    
    -- Structured Analysis Data
    sentiment_analysis jsonb, -- Emotional analysis for optimization
    target_audience jsonb, -- Recommended targeting adjustments
    creative_elements jsonb, -- Visual/audio feedback
    competitive_insights jsonb, -- How it compares to successful ads
    performance_predictions jsonb, -- Expected performance metrics
    
    -- Metadata and Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    analysis_completed_at timestamp with time zone,
    
    -- Privacy and Access
    is_shared boolean DEFAULT false, -- Can be shared with team members
    shared_with jsonb, -- Array of user IDs who have access
    expires_at timestamp with time zone, -- Optional expiration for temporary analyses
    
    -- Constraints
    CONSTRAINT private_analyses_user_video_unique UNIQUE(user_id, youtube_video_id),
    CONSTRAINT private_analyses_status_check CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    CONSTRAINT private_analyses_score_check CHECK (pre_launch_score >= 0 AND pre_launch_score <= 100)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_private_analyses_user_id ON private_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_private_analyses_status ON private_analyses(status);
CREATE INDEX IF NOT EXISTS idx_private_analyses_slug ON private_analyses(slug);
CREATE INDEX IF NOT EXISTS idx_private_analyses_video_id ON private_analyses(youtube_video_id);
CREATE INDEX IF NOT EXISTS idx_private_analyses_created_at ON private_analyses(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_private_analyses_user_status ON private_analyses(user_id, status);

-- Enable Row Level Security
ALTER TABLE private_analyses ENABLE row level security;

-- RLS Policies for private_analyses
-- Users can only access their own private analyses
CREATE POLICY "Users can view own private analyses" ON private_analyses
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert own private analyses" ON private_analyses
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own private analyses" ON private_analyses
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete own private analyses" ON private_analyses
    FOR DELETE USING (user_id = auth.uid());

-- Policy for shared analyses (when is_shared = true and user is in shared_with array)
CREATE POLICY "Users can view shared private analyses" ON private_analyses
    FOR SELECT USING (
        is_shared = true AND 
        shared_with ? auth.uid()::text
    );

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_private_analyses_updated_at()
RETURNS trigger AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language plpgsql;

-- Trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_private_analyses_updated_at_trigger ON private_analyses;
CREATE TRIGGER update_private_analyses_updated_at_trigger
    BEFORE UPDATE ON private_analyses
    FOR EACH ROW
    EXECUTE FUNCTION update_private_analyses_updated_at();

-- Function to clean up expired private analyses
CREATE OR REPLACE FUNCTION cleanup_expired_private_analyses()
RETURNS void AS $$
BEGIN
    DELETE FROM private_analyses 
    WHERE expires_at IS NOT NULL AND expires_at < now();
END;
$$ language plpgsql;

-- Function to generate unique slug for private analyses
CREATE OR REPLACE FUNCTION generate_private_analysis_slug(
    p_brand text,
    p_title text,
    p_video_id text
)
RETURNS text AS $$
DECLARE
    base_slug text;
    final_slug text;
    counter integer := 0;
BEGIN
    -- Create base slug from brand, title, and video ID
    base_slug := lower(
        regexp_replace(
            coalesce(p_brand, 'unknown') || '-' || 
            coalesce(substring(p_title from 1 for 50), 'private-analysis') || '-' ||
            p_video_id,
            '[^a-zA-Z0-9-]', '-', 'g'
        )
    );
    
    -- Remove multiple dashes and trim
    base_slug := regexp_replace(base_slug, '-+', '-', 'g');
    base_slug := trim(both '-' from base_slug);
    
    -- Ensure uniqueness
    final_slug := base_slug;
    WHILE EXISTS (SELECT 1 FROM private_analyses WHERE slug = final_slug) LOOP
        counter := counter + 1;
        final_slug := base_slug || '-' || counter;
    END LOOP;
    
    RETURN final_slug;
END;
$$ language plpgsql;

-- Add comments for documentation
COMMENT ON TABLE private_analyses IS 'Dedicated table for private/pre-launch video ad analyses';
COMMENT ON COLUMN private_analyses.analysis_type IS 'Type of private analysis: private, pre-launch, beta';
COMMENT ON COLUMN private_analyses.marketing_analysis IS 'Main pre-launch analysis content with optimization focus';
COMMENT ON COLUMN private_analyses.optimization_recommendations IS 'Specific actionable recommendations for improving the ad';
COMMENT ON COLUMN private_analyses.pre_launch_score IS 'Overall readiness score from 1-100 for launch preparation';
COMMENT ON COLUMN private_analyses.performance_predictions IS 'Predicted performance metrics based on analysis';
COMMENT ON COLUMN private_analyses.is_shared IS 'Whether this analysis can be shared with team members';
COMMENT ON COLUMN private_analyses.shared_with IS 'Array of user IDs who have access to this shared analysis';
COMMENT ON COLUMN private_analyses.expires_at IS 'Optional expiration timestamp for temporary analyses';