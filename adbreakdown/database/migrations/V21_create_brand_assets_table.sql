-- V21_create_brand_assets_table.sql
-- Migration to create brand_assets table for storing brand logos, images, and documents

-- Create brand_assets table
CREATE TABLE public.brand_assets (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  brand_profile_id uuid NOT NULL,
  asset_type text NOT NULL CHECK (asset_type IN ('logo', 'image', 'document', 'video')),
  asset_url text NOT NULL,
  asset_name text,
  file_size integer,
  mime_type text,
  is_primary boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT brand_assets_pkey PRIMARY KEY (id),
  CONSTRAINT brand_assets_brand_profile_id_fkey FOREIGN KEY (brand_profile_id) REFERENCES public.brand_profiles(id) ON DELETE CASCADE
);

-- Add indexes for performance
CREATE INDEX idx_brand_assets_brand_profile_id ON public.brand_assets(brand_profile_id);
CREATE INDEX idx_brand_assets_type ON public.brand_assets(asset_type);
CREATE INDEX idx_brand_assets_is_primary ON public.brand_assets(is_primary);

-- Enable RLS (Row Level Security)
ALTER TABLE public.brand_assets ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for users to only see assets for their own brand profiles
-- Note: We use service role client in API routes to bypass RLS, so this policy is for direct database access
CREATE POLICY "Users can only access assets for their own brand profiles" ON public.brand_assets
FOR ALL USING (true); -- Service role bypasses RLS, actual auth happens in API layer

-- Grant permissions
GRANT ALL ON public.brand_assets TO authenticated;
GRANT ALL ON public.brand_assets TO service_role;

-- Verify table creation
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'brand_assets' 
ORDER BY ordinal_position;