-- V16: Add new metadata columns to ad_analyses for enhanced filtering.
-- This migration adds columns that will be populated from the marketing_analysis JSON.

ALTER TABLE public.ad_analyses
  ADD COLUMN IF NOT EXISTS geography TEXT,
  ADD COLUMN IF EXISTS agency TEXT,
  ADD COLUMN IF EXISTS launch_date DATE;

-- Add indexes for the new filterable columns to ensure query performance
CREATE INDEX IF NOT EXISTS idx_ad_analyses_geography ON public.ad_analyses(geography);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_agency ON public.ad_analyses(agency);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_launch_date ON public.ad_analyses(launch_date);

COMMENT ON TABLE public.ad_analyses IS 'V16 migration complete. Added geography, agency, and launch_date columns.';
