-- Migration: V12 - Add Framework Intelligence and Competitive Analysis Tables
-- Date: 2025-07-24

-- This migration adds the necessary tables to support the V2.0 features,
-- including framework-driven analysis and competitive intelligence.

-- 1. Frameworks Table
-- Stores definitions for various marketing frameworks.
CREATE TABLE IF NOT EXISTS public.frameworks (
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    name character varying NOT NULL,
    description text,
    complexity_level character varying,
    category character varying,
    signature_patterns jsonb,
    CONSTRAINT frameworks_pkey PRIMARY KEY (id),
    CONSTRAINT frameworks_name_key UNIQUE (name)
);

-- 2. Analysis Frameworks Table
-- Links ad analyses to the frameworks applied to them.
CREATE TABLE IF NOT EXISTS public.analysis_frameworks (
    analysis_id uuid NOT NULL,
    framework_id uuid NOT NULL,
    confidence_score numeric,
    is_primary boolean DEFAULT false,
    detection_reasoning text[],
    CONSTRAINT analysis_frameworks_pkey PRIMARY KEY (analysis_id, framework_id),
    CONSTRAINT analysis_frameworks_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.ad_analyses(id) ON DELETE CASCADE,
    CONSTRAINT analysis_frameworks_framework_id_fkey FOREIGN KEY (framework_id) REFERENCES public.frameworks(id) ON DELETE CASCADE
);

-- 3. Competitive Analyses Table
-- Manages batch jobs for comparing multiple ads.
CREATE TABLE IF NOT EXISTS public.competitive_analyses (
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    primary_analysis_id uuid NOT NULL,
    competitor_urls text[],
    framework_used character varying,
    status character varying DEFAULT 'pending'::character varying,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT competitive_analyses_pkey PRIMARY KEY (id),
    CONSTRAINT competitive_analyses_primary_analysis_id_fkey FOREIGN KEY (primary_analysis_id) REFERENCES public.ad_analyses(id) ON DELETE CASCADE
);

-- 4. Competitor Results Table
-- Stores the analysis results for each competitor in a comparison.
CREATE TABLE IF NOT EXISTS public.competitor_results (
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    competitive_analysis_id uuid NOT NULL,
    competitor_url text,
    analysis_result jsonb,
    framework_scores jsonb,
    positioning_data jsonb,
    CONSTRAINT competitor_results_pkey PRIMARY KEY (id),
    CONSTRAINT competitor_results_competitive_analysis_id_fkey FOREIGN KEY (competitive_analysis_id) REFERENCES public.competitive_analyses(id) ON DELETE CASCADE
);

-- Add comments to the new tables and columns for clarity
COMMENT ON TABLE public.frameworks IS 'Definitions for marketing frameworks like AIDA, PAS, etc.';
COMMENT ON TABLE public.analysis_frameworks IS 'Junction table linking ad analyses to the frameworks used for analysis.';
COMMENT ON TABLE public.competitive_analyses IS 'Manages a batch job for comparing a primary ad against multiple competitors.';
COMMENT ON TABLE public.competitor_results IS 'Stores the individual analysis results for each competitor video in a competitive analysis job.';