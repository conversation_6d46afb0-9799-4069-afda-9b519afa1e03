-- V17_add_private_analysis_fk_to_reports.sql
-- Add a foreign key to analysis_reports for private_analyses

-- 1. Add a new column for private_analysis_id
ALTER TABLE analysis_reports
ADD COLUMN private_analysis_id UUID;

-- 2. Add a foreign key constraint to private_analyses
ALTER TABLE analysis_reports
ADD CONSTRAINT fk_private_analysis
FOREIGN KEY (private_analysis_id)
REFERENCES private_analyses(id)
ON DELETE CASCADE;

-- 3. Make the existing analysis_id nullable, as reports can now belong to either ad_analyses or private_analyses
ALTER TABLE analysis_reports
ALTER COLUMN analysis_id DROP NOT NULL;

-- 4. Add a check constraint to ensure that exactly one of analysis_id or private_analysis_id is set
-- This ensures a report is always linked to one and only one type of analysis
ALTER TABLE analysis_reports
ADD CONSTRAINT chk_analysis_id_xor_private_analysis_id
CHECK (
    (analysis_id IS NOT NULL AND private_analysis_id IS NULL) OR
    (analysis_id IS NULL AND private_analysis_id IS NOT NULL)
);

-- Optional: Migrate existing data if needed (e.g., if some reports were mistakenly linked to private analyses before this migration)
-- For now, we assume new reports will correctly use private_analysis_id
