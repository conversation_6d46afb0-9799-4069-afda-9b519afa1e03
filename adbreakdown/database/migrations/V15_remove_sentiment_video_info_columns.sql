-- V15: This migration removes the sentiment_timeline and video_info columns from ad_analyses
-- and safely rebuilds the dependent showcase_analyses view.

-- Step 1: Drop the dependent view before altering the table.
DROP VIEW IF EXISTS public.showcase_analyses;

-- Step 2: Drop the specified columns from the ad_analyses table.
ALTER TABLE public.ad_analyses
  DROP COLUMN IF EXISTS sentiment_timeline,
  DROP COLUMN IF EXISTS video_info;

-- Step 3: Recreate the showcase_analyses view with the updated column list.
CREATE OR REPLACE VIEW public.showcase_analyses AS
 SELECT a.id,
    a.user_id,
    a.youtube_url,
    a.status,
    a.title,
    a.inferred_brand,
    a.duration_seconds,
    a.duration_formatted,
    a.thumbnail_url,
    a.marketing_analysis,
    a.analysis_completed_at,
    a.created_at,
    a.updated_at,
    a.deciphered_script,
    a.slug,
    a.youtube_video_id,
    a.is_public,
    a.showcase,
    ds.featured_date,
    ds.excerpt,
    ds.key_insights
   FROM (ad_analyses a
     LEFT JOIN daily_showcases ds ON ((a.id = ds.analysis_id)))
  WHERE ((a.is_public = true) AND (a.showcase = true));

-- Log completion of migration
COMMENT ON TABLE public.ad_analyses IS 'V15 cleanup migration complete. Removed sentiment_timeline and video_info columns.';
