-- V14: This migration removes deprecated columns and safely rebuilds dependent views.

-- Step 1: Drop the dependent views before altering the table.
DROP VIEW IF EXISTS public.showcase_analyses;
DROP VIEW IF EXISTS public.public_analyses;

-- Step 2: Drop the deprecated columns from the ad_analyses table.
ALTER TABLE public.ad_analyses
  DROP COLUMN IF EXISTS content_suggestions,
  DROP COLUMN IF EXISTS marketing_copy,
  DROP COLUMN IF EXISTS social_media_posts,
  DROP COLUMN IF EXISTS marketing_scorecard,
  DROP COLUMN IF EXISTS seo_keywords,
  DROP COLUMN IF EXISTS overall_sentiment,
  DROP COLUMN IF EXISTS emotions,
  DROP COLUMN IF EXISTS transcript,
  DROP COLUMN IF EXISTS summary,
  DROP COLUMN IF EXISTS key_themes,
  DROP COLUMN IF EXISTS music_mood,
  DROP COLUMN IF EXISTS voice_tone,
  DROP COLUMN IF EXISTS audio_quality,
  DROP COLUMN IF EXISTS visual_appeal,
  DROP COLUMN IF EXISTS color_palette,
  DROP COLUMN IF EXISTS scenes,
  DROP COLUMN IF EXISTS target_demographics,
  DROP COLUMN IF EXISTS target_interests,
  DROP COLUMN IF EXISTS target_behaviors,
  DROP COLUMN IF EXISTS llm_correction;

-- Step 3: Recreate the views with the updated, correct column lists.

-- Recreate showcase_analyses view (based on its actual definition)
CREATE OR REPLACE VIEW public.showcase_analyses AS
 SELECT a.id,
    a.user_id,
    a.youtube_url,
    a.status,
    a.title,
    a.inferred_brand,
    a.duration_seconds,
    a.duration_formatted,
    a.thumbnail_url,
    a.video_info,
    a.marketing_analysis,
    a.analysis_completed_at,
    a.created_at,
    a.updated_at,
    a.sentiment_timeline,
    a.deciphered_script,
    a.slug,
    a.youtube_video_id,
    a.is_public,
    a.showcase,
    ds.featured_date,
    ds.excerpt,
    ds.key_insights
   FROM (ad_analyses a
     LEFT JOIN daily_showcases ds ON ((a.id = ds.analysis_id)))
  WHERE ((a.is_public = true) AND (a.showcase = true));

-- Recreate public_analyses view (based on its actual definition)
CREATE OR REPLACE VIEW public.public_analyses AS
 SELECT id,
    slug,
    youtube_url,
    youtube_video_id,
    title,
    inferred_brand,
    duration_seconds,
    duration_formatted,
    thumbnail_url,
    created_at,
    analysis_completed_at
   FROM ad_analyses
  WHERE ((is_public = true) AND (status = 'completed'::text));


-- Log completion of migration
COMMENT ON TABLE public.ad_analyses IS 'V14 cleanup migration complete. Removed 20 columns and rebuilt showcase_analyses and public_analyses views.';