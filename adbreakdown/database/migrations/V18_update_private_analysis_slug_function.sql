-- V18_update_private_analysis_slug_function.sql
-- Update the generate_private_analysis_slug function to prepend 'private-'

-- Drop the existing function
DROP FUNCTION IF EXISTS generate_private_analysis_slug(
    p_brand text,
    p_title text,
    p_video_id text
);

-- Create the new function with 'private-' prefix
CREATE OR REPLACE FUNCTION generate_private_analysis_slug(
    p_brand text,
    p_title text,
    p_video_id text
)
RETURNS text AS $$
DECLARE
    base_slug text;
    final_slug text;
    counter integer := 0;
BEGIN
    -- Create base slug from brand, title, and video ID, prepending 'private-'
    base_slug := lower(
        'private-' ||
        regexp_replace(
            coalesce(p_brand, 'unknown') || '-' || 
            coalesce(substring(p_title from 1 for 50), 'analysis') || '-' ||
            p_video_id,
            '[^a-zA-Z0-9-]', '-', 'g'
        )
    );
    
    -- Remove multiple dashes and trim
    base_slug := regexp_replace(base_slug, '-+', '-', 'g');
    base_slug := trim(both '-' from base_slug);
    
    -- Ensure uniqueness
    final_slug := base_slug;
    WHILE EXISTS (SELECT 1 FROM private_analyses WHERE slug = final_slug) LOOP
        counter := counter + 1;
        final_slug := base_slug || '-' || counter;
    END LOOP;
    
    RETURN final_slug;
END;
$$ language plpgsql;
