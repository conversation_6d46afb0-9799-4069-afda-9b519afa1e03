-- V23: Add database trigger for automatic metadata extraction
-- This migration creates a pure SQL trigger that extracts metadata directly

-- Function to extract metadata from marketing_analysis JSON and populate individual columns
CREATE OR REPLACE FUNCTION extract_metadata_from_analysis()
RETURNS TRIGGER AS $$
BEGIN
  -- Only trigger when status changes to 'completed' and marketing_analysis exists
  IF NEW.status = 'completed' 
     AND NEW.marketing_analysis IS NOT NULL 
     AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
    
    -- Extract metadata from marketing_analysis using json_extract_path_text for reliability
    UPDATE public.ad_analyses SET
      brand = COALESCE(
        json_extract_path_text(NEW.marketing_analysis::json, 'brand'),
        json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'brand')
      ),
      product_category = json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'product_category'),
      parent_entity = json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'parent_entity'),
      campaign_category = json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_category'),
      geography = json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'geography'),
      agency = json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'agency'),
      celebrity = CASE 
        WHEN json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'celebrity') = 'None' THEN NULL
        ELSE json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'celebrity')
      END,
      launch_date = CASE 
        WHEN json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_launch_date') IS NOT NULL 
        THEN 
          CASE 
            -- Handle "May 2024" format - convert to first day of month
            WHEN json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_launch_date') ~ '^[A-Za-z]+ \d{4}$' 
            THEN (TO_DATE(json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_launch_date'), 'Month YYYY'))::timestamp with time zone
            -- Handle other date formats if they exist
            ELSE 
              CASE 
                WHEN json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_launch_date') ~ '^\d{4}-\d{2}-\d{2}' 
                THEN (json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_launch_date'))::timestamp with time zone
                ELSE NULL
              END
          END
        ELSE NULL 
      END,
      updated_at = NOW()
    WHERE id = NEW.id;
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS metadata_extraction_trigger ON public.ad_analyses;

CREATE TRIGGER metadata_extraction_trigger
  AFTER UPDATE ON public.ad_analyses
  FOR EACH ROW
  EXECUTE FUNCTION extract_metadata_from_analysis();

-- Add comment for tracking
COMMENT ON FUNCTION extract_metadata_from_analysis() IS 'V23 migration: Extracts metadata from marketing_analysis JSON into individual columns';

COMMENT ON TRIGGER metadata_extraction_trigger ON public.ad_analyses IS 'V23 migration: Automatically extracts metadata when analysis status changes to completed';