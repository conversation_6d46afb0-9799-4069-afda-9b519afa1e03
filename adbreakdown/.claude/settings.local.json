{"permissions": {"allow": ["Bash(npm run build:*)", "Bash(find:*)", "Bash(rm:*)", "Bash(ls:*)", "<PERSON><PERSON>(mv:*)", "Bash(rg:*)", "Bash(grep:*)", "Bash(npm install:*)", "Bash(npm run type-check:*)", "Bash(npm run dev:*)", "Bash(node:*)", "<PERSON><PERSON>(npm show:*)", "Bash(npm view:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "WebFetch(domain:crayo.ai)", "<PERSON><PERSON>(mkdir:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(npx shadcn@latest add:*)", "Bash(psql:*)", "Bash(brew services:*)", "<PERSON>sh(redis-server:*)", "Bash(redis-cli:*)", "WebFetch(domain:www.ideabrowser.com)", "Bash(npm run lint:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(sed:*)", "Bash(git checkout:*)", "Bash(git restore:*)", "Bash(supabase db pull:*)", "Bash(supabase db dump:*)", "Bash(cp:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(diff:*)", "Bash(npx supabase functions deploy:*)", "<PERSON><PERSON>(python3:*)", "WebFetch(domain:cloud.google.com)", "Bash(npx tsc:*)", "Bash(NEXT_PUBLIC_USE_VERTEX_AI=false node -e \"\nconst featureFlags = {\n  useVertexAi: process.env.NEXT_PUBLIC_USE_VERTEX_AI === ''false'',\n};\nconsole.log(''🔧 With env var set to \"\"false\"\":'', featureFlags.useVertexAi);\n\")", "Bash(NEXT_PUBLIC_USE_VERTEX_AI=true node -e \"\nconst featureFlags = {\n  useVertexAi: process.env.NEXT_PUBLIC_USE_VERTEX_AI === ''false'',\n};\nconsole.log(''🔧 With env var set to \"\"true\"\":'', featureFlags.useVertexAi);\n\")", "Bash(kill:*)", "WebFetch(domain:developers.google.com)", "<PERSON>sh(gsutil cors set:*)", "WebFetch(domain:ai-sdk.dev)", "WebFetch(domain:ai.google.dev)", "WebFetch(domain:www.breakdown.ad)", "WebFetch(domain:docs.copilotkit.ai)", "WebFetch(domain:github.com)", "WebFetch(domain:www.livemint.com)", "Bash(npm ls:*)", "Bash(npx next lint:*)"], "deny": []}}