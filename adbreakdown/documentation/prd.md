# breakdown.ad - Product Requirements Document
**AI-Powered Video Ad Analysis SaaS Platform**

**Version:** 2.0 (Framework-Intelligent Release)
**Date:** July 2025
**Status:** 🚀 **V1.0 PRODUCTION-READY**, V2.0 IN DEVELOPMENT

---

## 1. Executive Summary

**Vision:** Transform subjective ad evaluations into objective, data-driven insights through AI-powered analysis, empowering marketers and advertisers to optimize their video campaigns effectively.

**Mission:** Provide the most comprehensive, AI-driven analysis platform for YouTube video ads, enabling objective evaluation of creative elements, competitive intelligence, and delivering actionable recommendations for campaign optimization.

**Market Position:** A framework-intelligent video advertising analysis platform, combining Gemini AI capabilities with marketing expertise to deliver strategic insights that rival human consultants.

---

## 2. Target Market & Users

### Primary Target Personas
- **Marketing Professionals**: Strategists, creatives, and analysts seeking objective ad performance insights.
- **Digital Marketing Agencies**: Teams requiring scalable analysis capabilities for client campaigns.
- **Brand Managers**: Executives needing strategic creative direction and competitive intelligence.
- **Small Business Owners**: Entrepreneurs optimizing video ad campaigns on limited budgets.

### Market Opportunity
- **Total Addressable Market**: $450M subset of AI-powered marketing platforms focused on video advertising analysis.
- **Primary Need**: Objective, data-driven video ad analysis to replace subjective creative evaluations.
- **Key Pain Point**: Lack of structured, AI-powered tools for comprehensive video ad analysis and competitive benchmarking.

---

## 3. System Architecture

### 3.1. Technology Stack
- **Frontend**: Next.js 14 (App Router), React 18, TypeScript (strict mode)
- **UI Framework**: Tailwind CSS, ShadCN/ui components, Radix UI primitives
- **Authentication**: Clerk
- **Database**: Supabase (PostgreSQL)
- **AI/ML**: Google Gemini API (1.5 Flash & Pro)
- **Payments**: Lemon Squeezy
- **Video Processing**: Video.js, YouTube API
- **Deployment**: Vercel (Frontend), Supabase (Database, Edge Functions)
- **Caching**: Three-tier strategy (Static Generation, ISR, API-Level Redis)

### 3.2. Project & Folder Structure
```
/src/app/                 # Next.js App Router pages
├── /(auth)/             # Authentication pages
├── /ad/[id]/            # Dynamic analysis detail pages
├── /api/                # API routes
├── /studio/             # Main user dashboard
└── /billing/            # Subscription management

/src/components/         # React components
├── /ui/                 # Reusable UI components (ShadCN/ui)
└── /studio/             # Dashboard-specific components

/src/lib/                # Utility functions, prompts, configs
/src/hooks/              # Custom React hooks
/src/services/           # API clients & external service integrations
/documentation/          # Project specifications
/database/               # DB migrations, schema, functions
/supabase/               # Supabase CLI files (Edge Functions)
```

### 3.3. Database Schema
- **Core Tables**: `users`, `profiles`, `ad_analyses`, `analysis_reports`, `report_types`, `credit_usage`, `sentiment_analyses`, `emotion_timeline_events`, `daily_showcases`, `admin_submissions`, `frameworks`, `analysis_frameworks`, `competitive_analyses`, `competitor_results`.
- **Management**: Migrations are version-controlled in `/database/migrations/`. Schema is managed via `/database/update_schema.sh`.

---

## 4. User Flows

### 4.1. Onboarding & Authentication
1.  User lands on homepage (`/`).
2.  System checks Clerk authentication.
3.  **If not logged in**: User is prompted to Sign Up/Log In via Clerk UI.
4.  **If logged in**: System checks for an active Lemon Squeezy subscription.
5.  **If no subscription**: User is prompted to subscribe.
6.  User is redirected to the `/studio` dashboard.

### 4.2. Core Analysis Flow
1.  User pastes a YouTube URL in the `/studio` dashboard.
2.  System validates the URL.
3.  An `ad_analyses` record is created.
4.  The `run-ad-analysis` Supabase Edge Function is triggered.
5.  Credits are deducted.
6.  The function calls Gemini API for a multi-faceted analysis (transcript, summary, metadata, sentiment, etc.).
7.  Results are stored in `analysis_reports` and the user is redirected to the analysis page `/ad/[id]`.

### 4.3. Analysis Report & Feature Generation
1.  The `/ad/[id]` page displays the full analysis in a tabbed interface (Overview, Sentiment, Script, Visual, Audio, Targeting).
2.  The page uses a progressive loading architecture for a fast initial load.
3.  User can click action buttons to generate additional AI content (Marketing Copy, Social Posts, etc.) for 1 credit each.
4.  These actions trigger the `run-llm-feature-generation` Supabase Edge Function.
5.  Generated content is displayed dynamically on the page.

---

## 5. Core Features

### 5.1. AI-Powered Video Analysis Engine (V1.0 - Implemented ✅)
- **User Story**: As a marketer, I want to paste a YouTube URL and receive a comprehensive AI-driven analysis of the ad's sentiment, emotions, script, visual, and audio elements.
- **Implementation**: Uses Gemini 1.5 Flash for a single, comprehensive analysis call. Results are structured into multiple sections.
- **Credit Cost**: 2 credits.

### 5.2. AI Content Generation Suite (V1.0 - Implemented ✅)
- **User Story**: As a marketer, I want to generate creative assets and strategic documents based on the core analysis.
- **Features (1 credit each)**:
    1.  **Marketing Copy & Headlines**
    2.  **Social Media Posts** (Platform-specific)
    3.  **Marketing Scorecard** (7-parameter evaluation)
    4.  **SEO Keywords**
    5.  **Content Improvement Suggestions**
- **Implementation**: Triggered from the analysis page, processed by a dedicated Edge Function.

### 5.3. User Dashboard & Analysis Management (V1.0 - Implemented ✅)
- **Features**:
    - Analysis history with pagination and status indicators (`AnalysisCard`).
    - Professional input component (`AnalysisInputCard`).
    - Real-time dashboard stats (total analyses, credits, etc.).
    - Credit balance and subscription status display.

### 5.4. Credit System & Monetization (V1.0 - Implemented ✅)
- **Credit Structure**:
    - Basic Analysis: 2 credits
    - LLM Features: 1 credit each
- **Subscription Tiers**: Starter ($9.99/mo), Pro ($19.99/mo), Enterprise ($49.99/mo).
- **Implementation**: Lemon Squeezy integration for payments and subscription management. Webhooks update user profiles and credit balances in Supabase. Secure SQL functions (`decrement_credits`, etc.) handle atomic credit transactions.

### 5.5. Public Analyses & Discovery (V1.0 - Implemented ✅)
- **User Story**: As a user, I want to make my analyses public to share them, and I want to explore analyses shared by others.
- **Features**:
    - `is_public` toggle on analyses for owners.
    - Public analysis list page (`/ad-library`) with grid layout and pagination.
    - Public analyses (`/ad/[slug]`) are viewable without authentication.
    - API endpoints (`/api/analyses/public`, `/api/analyses/[id]/toggle-public`) to support the feature.

### 5.6. Metadata Validation (V1.0 - Implemented ✅)
- **User Story**: As a user, I want to validate the AI-inferred metadata against public sources for accuracy.
- **Implementation**: Uses a dedicated hook (`useMetadataValidation`) and component (`MetadataValidationCard`). The `/api/analyses/[id]/validate-metadata` endpoint uses Gemini's Google Search integration to verify data, provide confidence scores, and suggest enhancements.

### 5.7. Framework Intelligence (V2.0 - In Development 🔄)
- **User Story**: As a strategist, I want the AI to analyze ads through the lens of established marketing frameworks to get deeper strategic insights.
- **Features**:
    - **Framework Detection**: AI automatically classifies which framework (AIDA, PAS, ADPLAN, etc.) best fits the ad.
    - **Framework-Specific Analysis**: The AI provides a detailed breakdown and scoring based on the detected framework's components.
    - **UI**: The analysis page will feature new visualizations like radar charts (for ADPLAN) and interactive timelines.

### 5.8. Competitive Analysis (V1.0 - Partially Implemented 🔄, V2.0 - Planned)
- **User Story**: As a brand manager, I want to compare my ad directly against my competitors' ads.
- **Current State**: UI shell with mock data.
- **Planned (V2.0)**: Multi-URL input to run side-by-side framework comparisons, identify market positioning, and find strategic gaps.

---

## 6. API Architecture & Critical Patterns

### 6.1. Core API Endpoints
- **Analyses**: `POST /api/analyses`, `GET /api/analyses`, `GET /api/analyses/[id]`
- **Content Generation**: `POST /api/analyses/[id]/generate-llm-feature`
- **User & Billing**: `GET /api/user/profile`, `POST /api/billing/create-checkout`
- **Webhooks**: `POST /api/webhooks/clerk`, `POST /api/webhooks/lemon-squeezy`

### 6.2. Critical Pattern: UUID/Slug Resolution
**MANDATORY**: All API routes in `/api/analyses/[id]/` **MUST** handle both UUIDs and SEO-friendly slugs to prevent database errors.
```typescript
// ✅ CORRECT - Handles both UUIDs and slugs
const isUUID = /^[0-9a-f]{8}-.../.test(id);
const { data } = await supabase
  .from('ad_analyses')
  .select('*')
  .eq(isUUID ? 'id' : 'slug', id) // Critical: Use conditional field
  .single();
```

### 6.3. Critical Pattern: Credit-First Architecture
All AI operations that cost credits **MUST** follow this pattern:
1.  Pre-validate if sufficient credits exist.
2.  Deduct credits **BEFORE** initiating the operation.
3.  Perform the operation.
4.  Handle success or failure, refunding credits on failure if necessary.

---

## 7. SEO & GEO Strategy

- **Traditional SEO**: Implemented via dynamic sitemaps, structured data (JSON-LD), meta tags, and canonical URLs. Public analyses and a future blog will serve as content for link building.
- **GPT Engine Optimization (GEO)**: A `public/llm.txt` file provides a comprehensive overview for AI crawlers. Content is structured semantically to be easily discoverable and citable by LLMs.

---

## 8. Future Roadmap (Post-V2.0)

- **Advanced Analytics**: Performance prediction (virality, conversion), A/B testing variant suggestions.
- **Team Collaboration**: Shared workspaces, collaborative annotation, role-based access.
- **Multi-Platform Support**: Expand analysis to TikTok, Instagram, and Facebook video ads.
- **Community Features**: User profiles, comments/likes, leaderboards, and content curation.

---

## 9. Out of Scope (Current Version)

- Direct integration with ad platforms (Google Ads, Meta Ads).
- Custom AI model fine-tuning.
- Real-time collaboration features.