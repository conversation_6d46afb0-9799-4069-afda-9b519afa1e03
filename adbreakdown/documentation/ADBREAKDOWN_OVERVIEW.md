# breakdown.ad: V2.0 Overview & Core Systems

## 1. Core Purpose & Vision

**Vision:** To transform subjective ad evaluations into objective, data-driven insights through AI-powered analysis, empowering marketers and advertisers to optimize their video campaigns with strategic intelligence.

**Core Function:** breakdown.ad is a framework-intelligent SaaS platform that analyzes YouTube video ads. It uses the Google Gemini API to provide deep strategic analysis, generate creative assets, and offer competitive intelligence.

## 2. System Architecture Highlights

- **Tech Stack**: Next.js 14, React 18, TypeScript, Tailwind CSS, ShadCN/ui.
- **Backend**: Supabase (PostgreSQL Database, Edge Functions), Clerk (Authentication), Lemon Squeezy (Payments).
- **AI Engine**: Google Gemini 1.5 (Flash & Pro) for all analysis and content generation.
- **Caching**: A three-tier strategy (Static, ISR, Redis) is implemented for optimal performance on public-facing pages like the Ad Library and Featured Analyses.

## 3. Core User Flow

1.  **Authentication**: User signs up or logs in via Clerk. A valid Lemon Squeezy subscription is required for full access.
2.  **Analysis Submission**: In the `/studio` dashboard, a user submits a public YouTube URL.
3.  **AI Processing**: A Supabase Edge Function (`run-ad-analysis`) is triggered. It uses the Gemini API to perform a comprehensive analysis, deducting credits from the user's account.
4.  **Report Viewing**: The user is directed to the analysis page (`/ad/[id]`), which presents the full report in a progressively loaded, tabbed interface.
5.  **Feature Generation**: From the report page, the user can spend additional credits to generate AI-powered assets like marketing copy, social media posts, or a marketing scorecard.

## 4. Key Features

### V1.0 - Fully Implemented ✅
*   **AI-Powered Video Analysis**: Comprehensive analysis of a video's sentiment, script, visuals, audio, and target audience.
*   **AI Content Generation Suite**: Tools to create marketing copy, social media posts, SEO keywords, content suggestions, and a marketing scorecard.
*   **User Dashboard**: A central hub for managing analyses, credits, and subscription status.
*   **Credit & Subscription System**: Integrated with Lemon Squeezy for tiered access and feature usage.
*   **Public Analysis & Discovery**: Users can make their analyses public, which are then discoverable in the `/ad-library`.
*   **Metadata Validation**: An AI-powered feature to verify and enhance ad metadata using Google Search grounding.

### V2.0 - In Development 🔄
*   **Framework Intelligence**: The platform's core evolution. The AI will automatically detect and analyze ads through the lens of established marketing frameworks (e.g., AIDA, PAS, ADPLAN), providing a deeper layer of strategic insight.
*   **Competitive Analysis**: A dedicated feature to analyze multiple competitor ads side-by-side, identifying strategic gaps and market positioning opportunities.

## 5. Data & Third-Party Integrations

- **Authentication**: **Clerk** handles all user management.
- **Payments**: **Lemon Squeezy** manages all subscription and billing logic.
- **AI/ML**: **Google Gemini API** is the core engine for all analysis.
- **Video Data**: The **YouTube API** is used for fetching initial video metadata.
- **Database & Backend**: **Supabase** serves as the primary database and hosts serverless Edge Functions for backend processing.

## 6. Data Privacy & Compliance

- **Data Collection**: The platform primarily processes public YouTube video URLs. User data (email, subscription status) is managed by Clerk and Lemon Squeezy.
- **Data Usage**: Data is used to provide the core analysis service, personalize the user experience, and for internal analytics. Public analyses are displayed only with explicit user consent.
- **Compliance**: The platform operates in compliance with the YouTube API Services Terms of Service and Google Privacy Policy.
