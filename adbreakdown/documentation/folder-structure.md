# breakdown.ad - Folder Structure

This document outlines the official folder structure for the breakdown.ad Next.js application.

```
adbreakdown/
├── /database/                  # Database migrations, schema, and SQL functions
│   ├── /migrations/            # Version-controlled DB migrations
│   ├── /functions/             # SQL functions (e.g., credit management)
│   └── schema.sql              # The complete database schema
│
├── /documentation/             # All project documentation (PRD, plans, etc.)
│
├── /public/                    # Static assets (images, fonts, llm.txt)
│
├── /src/                       # Main application source code
│   ├── /app/                   # Next.js App Router
│   │   ├── /(app)/             # Protected routes requiring authentication
│   │   │   ├── /studio/        # Main user dashboard
│   │   │   └── /ad/[id]/       # Dynamic analysis detail pages
│   │   ├── /(auth)/            # Authentication pages (handled by Clerk)
│   │   └── /api/               # API routes for all backend logic
│   │
│   ├── /components/            # Reusable React components
│   │   ├── /ui/                # Core UI elements from ShadCN
│   │   ├── /studio/            # Components specific to the user dashboard
│   │   └── /analysis/          # Components for the analysis report page
│   │
│   ├── /hooks/                 # Custom React hooks (e.g., useCredits)
│   │
│   ├── /lib/                   # Core libraries, utilities, and configurations
│   │   ├── /prompts/           # Centralized Gemini API prompts
│   │   ├── supabase.ts         # Supabase client instances
│   │   └── utils.ts            # General utility functions
│   │
│   └── /services/              # Third-party service integrations
│
├── /supabase/                  # Supabase CLI configuration and Edge Functions
│   ├── /functions/             # TypeScript Edge Functions
│   │   ├── /run-ad-analysis/   # Core AI analysis pipeline
│   │   └── /run-llm-feature-generation/ # Handles secondary content generation
│   └── config.toml             # Supabase project configuration
│
├── .env.local                  # Local environment variables (DO NOT COMMIT)
├── next.config.js              # Next.js configuration
├── tailwind.config.js          # Tailwind CSS configuration
└── tsconfig.json               # TypeScript configuration
```

### Key Principles:

*   **App Router**: All frontend routes and backend API endpoints are located within `/src/app/`.
*   **Backend Logic**: Complex, long-running backend tasks are handled by **Supabase Edge Functions** in the `/supabase/functions/` directory. API routes in `/src/app/api/` serve as the interface to these functions and for direct database interactions.
*   **Database Management**: The `/database/` directory is the source of truth for the PostgreSQL schema and migrations.
*   **Component Organization**: Components are organized by feature or page, with globally reusable UI elements in `/src/components/ui/`.
*   **Configuration**: All major configurations (Next.js, TypeScript, Tailwind) are in the root directory.