## Task Plan - breakdown.ad v2.0 Implementation Roadmap

This task plan breaks down the PRD into actionable phases and steps. It assumes the existing foundational setup (Auth, basic Supabase integration) is stable.

### Phase 1: Core Framework Integration (Backend & AI)

**Goal:** Enable AI to analyze ads through the lens of marketing frameworks and store structured data.

1.  **DB Schema Updates (Framework Data):**
    *   Modify `ad_analyses` table to include fields for overall framework scores (e.g., `adplan_scores` JSONB, `aida_scores` JSONB).
    *   Extend `analysis_reports` table to store detailed framework breakdowns (e.g., `report_type_id` for 'adplan_breakdown', 'aida_breakdown', 'pas_breakdown', 'emotional_arc_timeline').
    *   Add `psychology` field to `frameworks` table/data in `src/lib/frameworks.ts`.
2.  **AI Prompt Engineering & Model Training:**
    *   Develop/refine Gemini API prompts for each core framework (ADPLAN, AIDA, PAS, Emotional Arcs, etc.) to extract specific insights and scores from video transcripts/visuals.
    *   Ensure AI output is structured (JSON) for easy database storage and frontend rendering.
3.  **Supabase Function: `run-ad-analysis` Enhancement:**
    *   Update `run-ad-analysis` to call Gemini API with framework-specific prompts.
    *   Parse AI responses and populate new `ad_analyses` and `analysis_reports` fields.
    *   Implement error handling for AI generation failures.
4.  **API: `POST /api/analyses` & `GET /api/analyses/{id}` Updates:**
    *   Modify `POST /api/analyses` to trigger framework-driven analysis.
    *   Enhance `GET /api/analyses/{id}` to fetch all new framework-related data from `ad_analyses` and `analysis_reports`.

### Phase 2: Enhanced Analysis UI/UX (`/ad/[id]/page.tsx`)

**Goal:** Redesign the ad analysis page to be interactive, visually rich, and framework-centric.

1.  **Modular Component Development:**
    *   Create reusable React components for each analysis module (e.g., `AdplanScorecard`, `AidaFunnel`, `PasBreakdown`, `EmotionalArcTimeline`, `SceneThumbnails`).
    *   Implement toggle/expand functionality for these modules.
2.  **Visual Aids Implementation:**
    *   **ADPLAN Radar Chart:** Integrate a charting library (e.g., Recharts, Chart.js) to display ADPLAN scores visually.
    *   **Emotional Arc Timeline:** Develop an interactive timeline component to visualize sentiment progression.
    *   **Scene-by-Scene Thumbnails:** Implement a component to display key frames with associated AI insights.
3.  **Interactive Video Player Integration:**
    *   Enhance `VideoPlayer` component to support timed commentary overlays.
    *   Connect scene thumbnails to video player for direct navigation.
4.  **Framework-Specific UI Elements:**
    *   Update `frameworks/[slug]/page.tsx` to display the new `psychology` field.
    *   Integrate framework-specific visualizations (e.g., PAS timeline bar).
5.  **Overall Page Layout Refinement:**
    *   Implement a clear, logical flow for analysis sections (Overview, Story/Message, Creative Elements, Impact).
    *   Ensure responsive design for all new UI elements.

### Phase 3: Interactive AI "Ad Agent"

**Goal:** Integrate a conversational AI assistant into the analysis experience.

1.  **Backend API for Ad Agent:**
    *   Create new API endpoint (e.g., `POST /api/ad-agent/chat`) to handle user queries.
    *   This endpoint will take the `analysis_id` and user query.
    *   Leverage Gemini API for conversational AI, providing it with the context of the current ad analysis data.
2.  **Frontend Chat Widget:**
    *   Develop a persistent chat widget on the `/ad/[id]` page.
    *   Implement real-time messaging and display AI responses.
    *   Pre-populate context for the AI based on the currently viewed ad.
3.  **Idea Generation Feature:**
    *   Extend Ad Agent functionality to generate ad ideas or improvements based on user prompts and analysis data.

### Phase 4: Community & Engagement Features

**Goal:** Build features that encourage user interaction and content contribution.

1.  **Comments & Ratings System:**
    *   DB Schema: New tables for `comments` (analysis_id, user_id, content, timestamp) and `ratings` (analysis_id, user_id, score).
    *   API Endpoints: `POST /api/analyses/{id}/comments`, `GET /api/analyses/{id}/comments`, `POST /api/analyses/{id}/ratings`.
    *   Frontend UI: Components for displaying comments, submitting new comments, and rating analyses.
2.  **Leaderboards & Badges:**
    *   DB Schema: Fields for user `contributor_score` or `badges`.
    *   API Endpoints: `GET /api/users/leaderboard`.
    *   Frontend UI: Display leaderboards on dashboard or a dedicated community page.
3.  **Challenges & Contests (MVP):**
    *   Define a basic mechanism for creating and managing contests (e.g., manual setup initially).
    *   Frontend UI: Display active contests and submission guidelines.
4.  **Personalized Feeds (MVP):**
    *   DB Schema: User preferences for `followed_brands`, `followed_industries`, `followed_frameworks`.
    *   API Endpoints: `GET /api/users/{id}/feed` to fetch new analyses based on preferences.
    *   Frontend UI: A "My Feed" section on the dashboard.

### Phase 5: Performance & Mobile Optimization

**Goal:** Ensure a fast, responsive, and accessible experience across devices.

1.  **Lazy Loading Implementation:**
    *   Apply lazy loading to images, video embeds, and complex charting components on analysis pages.
2.  **Mobile Responsiveness Audit & Refinement:**
    *   Thoroughly test all new and existing UI components on various mobile devices and screen sizes.
    *   Adjust layouts, font sizes, and interactive elements for touch-friendly use.
3.  **Login/Signup Flow Optimization:**
    *   Investigate and implement social login options (e.g., Google, Facebook via Clerk).
    *   Review and simplify the overall authentication journey.
4.  **Public Read-Only Access (Configuration):**
    *   Configure Clerk middleware to allow unauthenticated access to `/ad/[id]` pages for viewing, while restricting interactive features.

### Phase 6: Content & Educational Hub Expansion

**Goal:** Develop rich educational content to complement the analysis features.

1.  **Framework Comparison Tool:**
    *   Frontend UI: Develop an interactive component allowing users to select and compare multiple frameworks side-by-side.
2.  **Narrative Articles/Case Studies:**
    *   Content Creation: Write 3-5 initial articles/case studies based on existing analyses or mock data.
    *   Frontend UI: Create a dedicated section (e.g., `/insights` or `/case-studies`) to host these articles.
3.  **Educational Tooltips/Glossary:**
    *   Implement a system for displaying tooltips with definitions for marketing jargon and framework terms throughout the platform.
