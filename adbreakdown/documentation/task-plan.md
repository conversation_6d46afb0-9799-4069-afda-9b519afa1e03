# breakdown.ad - Consolidated Task Plan (V1.0 & V2.0)

This document outlines the complete implementation roadmap for the breakdown.ad platform, covering the foundational V1.0 features and the advanced V2.0 "Framework Intelligence" evolution.

---

## ✅ Phase 1: Foundational Setup & Authentication (V1.0 - COMPLETE)

**Objective**: Establish the core application, user authentication, and database structure.

1.  **Project & Tech Stack Setup**: Initialize Next.js, Tailwind CSS, ShadCN/ui.
2.  **Cloud Services Setup**: Configure Supabase (DB) and Clerk (Auth).
3.  **Environment Config**: Set up all required environment variables.
4.  **Clerk Integration**: Implement sign-in, sign-up, and middleware for protected routes.
5.  **Database Schema (Users & Profiles)**: Create `users` and `profiles` tables.
6.  **Clerk Webhook**: Implement a webhook to sync new users to the Supabase DB.
7.  **Lemon Squeezy Placeholder**: Create a placeholder webhook for payments.
8.  **Milestone**: Authentication layer is fully tested and integrated.

---

## ✅ Phase 2: Core AI Analysis Pipeline (V1.0 - COMPLETE)

**Objective**: Build the primary feature of analyzing a YouTube URL and displaying a basic report.

1.  **DB Schema (Analyses & Reports)**: Create `ad_analyses`, `analysis_reports`, and `report_types` tables.
2.  **API Endpoints**:
    *   `POST /api/analyses`: To create a new analysis job.
    *   `POST /api/analyses/{id}/trigger-initial-analysis`: To start the AI processing.
3.  **Supabase Function (`run-ad-analysis`)**:
    *   Receives a YouTube URL.
    *   Calls Gemini API to get transcript, summary, and inferred metadata.
    *   Calls Gemini API again for a detailed 10-point marketing analysis.
    *   Populates the database with the results.
4.  **Frontend UI (`/ad/[id]/page.tsx`)**:
    *   Build the basic layout for the analysis report page with a video player, metadata display, and a tabbed interface.
5.  **API Endpoint (`GET /api/analyses/{id}`)**: To fetch the analysis data for the report page.
6.  **End-to-End Test**: Verify that submitting a URL results in a complete, viewable report.
7.  **Milestone**: Core AI-driven ad analysis pipeline is functional.

---

## ✅ Phase 3: Dashboard & LLM Feature Suite (V1.0 - COMPLETE)

**Objective**: Create the user dashboard for managing analyses and implement the suite of AI-powered content generation tools.

1.  **API Endpoint (`GET /api/analyses`)**: To list all analyses for a user.
2.  **Dashboard UI (`/studio/page.tsx`)**:
    *   Implement `AnalysisInputCard` for submitting new URLs.
    *   Create a grid of `AnalysisCard` components to display recent analyses.
3.  **DB Schema (`report_types`)**: Add new types for each LLM feature (marketing copy, social posts, etc.).
4.  **Generic LLM API (`POST /api/analyses/{id}/generate-llm-feature`)**:
    *   A single endpoint to handle all secondary AI feature generation.
    *   Performs a credit check before proceeding.
    *   Triggers the `run-llm-feature-generation` Supabase function.
5.  **Supabase Function (`run-llm-feature-generation`)**:
    *   Uses the context of the initial analysis to generate the requested content (e.g., marketing copy).
    *   Deducts credits upon successful generation.
6.  **Frontend Integration**: Wire up the action buttons on the analysis page to the new API endpoint and display the generated content dynamically.
7.  **Milestone**: Dashboard is operational and all LLM features are integrated with the credit system.

---

## ✅ Phase 4: Payments & Production Polish (V1.0 - COMPLETE)

**Objective**: Integrate the payment system and prepare the application for production launch.

1.  **Lemon Squeezy Webhook**: Finalize the webhook handler to manage subscription events and grant credits.
2.  **Billing UI (`/billing/page.tsx`)**: Create a page for users to manage their subscription and view credit balance.
3.  **Credit System Polish**: Implement UI elements to show credit costs and handle insufficient credit errors gracefully.
4.  **E2E Testing**: Conduct thorough end-to-end testing of all user flows.
5.  **UI & Accessibility Review**: Final polish of all UI components, ensuring responsiveness and accessibility.
6.  **Milestone**: V1.0 application is production-ready.

---

## 🔄 Phase 5: Framework Intelligence Foundation (V2.0 - IN PROGRESS)

**Objective**: Evolve the platform to understand and analyze ads through the lens of established marketing frameworks.

1.  **DB Schema (Frameworks)**: Add `frameworks` and `analysis_frameworks` tables to store framework definitions and link them to analyses.
2.  **Framework Detection Algorithm**: Implement an AI model (`POST /api/analyses/[id]/detect-framework`) that uses Gemini Pro to automatically classify which marketing framework (AIDA, PAS, etc.) an ad uses.
3.  **Framework-Specific Analysis Engines**: Develop specialized Gemini prompts and logic within the `run-ad-analysis` function to perform detailed analysis based on the detected framework.
4.  **Credit System Update**: Add a 1-credit cost for each framework analysis applied.
5.  **Framework UI (`/ad/[id]`)**: Design and build new UI components to visualize framework analysis, such as radar charts for ADPLAN, funnels for AIDA, and interactive timelines.
6.  **Milestone**: The system can successfully detect and apply at least one marketing framework to an ad analysis, displaying the results in a new, specialized UI.

---

## 📋 Phase 6: Competitive Intelligence & Strategic Insights (V2.0 - PLANNED)

**Objective**: Build features for comparing ads and deriving high-level strategic insights.

1.  **DB Schema (Competition)**: Add `competitive_analyses` and `competitor_results` tables.
2.  **Multi-Video Analysis Pipeline**: Enhance the backend to process a primary ad and up to 5 competitor ads in a single batch job.
3.  **Competitive Analysis API (`POST /api/analyses/[id]/competitive-analysis`)**: Create an endpoint to initiate a competitive analysis.
4.  **Comparison UI**: Develop a side-by-side interface to compare framework scores, messaging, and positioning between the primary ad and its competitors.
5.  **Market Opportunity Engine**: Implement AI logic to analyze the competitive data and automatically identify market gaps, underutilized strategies, and differentiation opportunities.
6.  **Strategic Recommendation Engine**: Generate a high-level summary of strategic recommendations based on the competitive analysis.
7.  **Milestone**: Users can run a full competitive analysis and receive actionable strategic insights.

---

## 📋 Phase 7: Advanced Intelligence & Learning System (V2.0 - PLANNED)

**Objective**: Introduce predictive capabilities and a continuous learning loop.

1.  **Performance Prediction Engine**: Develop ML models to predict an ad's potential for engagement, conversion, and virality based on its analysis data.
2.  **Prediction UI**: Integrate prediction scores and confidence intervals into the analysis report.
3.  **Industry Benchmarking**: Implement a system to classify ads by industry and provide benchmark comparisons for framework scores and performance metrics.
4.  **AI Framework Assistant**: Create a chatbot/assistant to provide contextual help and education about the different marketing frameworks.
5.  **User Feedback Loop**: Implement a system for users to rate the quality of an analysis, with this feedback being used to continuously fine-tune the AI prompts and models.
6.  **Milestone**: The platform provides predictive insights and actively learns from user feedback to improve its analysis quality.