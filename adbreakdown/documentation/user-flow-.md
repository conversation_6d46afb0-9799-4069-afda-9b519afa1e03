# User Flow – breakdown.ad V2.0

## 1. Persona & Objective

*   **Persona**: Advertising professionals, digital marketing agencies, and brand managers.
*   **Objective**: To obtain a comprehensive, framework-driven analysis of a YouTube video ad, generate creative assets, and perform competitive benchmarking.

## 2. Core Flows

### 2.1. Onboarding & Authentication
*   This flow remains unchanged from V1.0.
*   User lands on the homepage, authenticates via <PERSON>, and the system verifies their Lemon Squeezy subscription status before granting access to the `/studio` dashboard.

### 2.2. V2.0 Analysis & Competitive Intelligence Flow

This flowchart outlines the enhanced user journey, incorporating the new Framework Intelligence and Competitive Analysis features.

```mermaid
flowchart TD
    subgraph "Phase 1: Setup & Initial Analysis"
        A[Start: /studio Dashboard] --> B{User provides YouTube URL};
        B --> C[API creates analysis job & deducts 2 credits];
        C --> D[run-ad-analysis function triggered];
        D --> E[AI performs core analysis: transcript, sentiment, etc.];
        E --> F[User redirected to /ad/[id]];
    end

    subgraph "Phase 2: Framework Intelligence"
        F --> G{Framework Analysis};
        G --> H[AI detects primary framework (e.g., AIDA)];
        H --> I[User confirms or manually selects a framework];
        I --> J[API triggers framework-specific analysis for +1 credit];
        J --> K[UI displays new Framework tab with visualizations];
    end

    subgraph "Phase 3: Content Generation & Competitive Analysis"
        K --> L{User Action?};
        L -->|Generate Content| M[User clicks 'Generate Copy', etc.];
        M --> N[API triggers LLM feature for +1 credit];
        N --> O[Generated content is displayed];
        O --> L;

        L -->|Compare Competitors| P[User clicks 'Compare' & adds competitor URLs];
        P --> Q[API triggers batch analysis for +2 credits/competitor];
        Q --> R[UI displays side-by-side Competitive Analysis tab];
        R --> L;
    end

    subgraph "Phase 4: Completion"
        L -->|End Task| S[User logs out or starts new analysis];
    end
```

### 2.3. Detailed Flow Steps

1.  **Initial Analysis (V1.0 Core)**:
    *   The user submits a YouTube URL in the `/studio` dashboard.
    *   The system performs the initial AI analysis (transcript, sentiment, etc.) and lands the user on the `/ad/[id]` report page. This costs 2 credits.

2.  **Framework Intelligence (V2.0 Enhancement)**:
    *   Upon loading the report, the system automatically triggers an AI process to detect the most likely marketing framework used in the ad.
    *   A new "Framework" tab or section appears, suggesting the primary framework (e.g., "AIDA") with a confidence score.
    *   The user can accept the suggestion or manually choose a different framework to apply.
    *   Confirming the framework analysis costs 1 additional credit and unlocks a detailed breakdown with framework-specific scores and visualizations (e.g., an AIDA funnel chart).

3.  **Content Generation (V1.0 Core)**:
    *   The user can still access the suite of LLM-powered content generation tools (Marketing Copy, Social Posts, etc.) at any time, each costing 1 credit.

4.  **Competitive Analysis (V2.0 Enhancement)**:
    *   From the report page, the user can initiate a competitive comparison.
    *   They can input up to 5 competitor YouTube URLs.
    *   The system runs the same framework analysis on all competitor videos.
    *   This costs 2 credits per competitor video.
    *   A new "Competition" tab appears, showing a side-by-side comparison of framework scores, messaging, and strategic positioning, along with AI-generated insights on market gaps and opportunities.

## 3. Exit Paths

*   **New Analysis**: The user can start a new analysis from the dashboard at any time.
*   **Billing**: The user can navigate to the `/billing` page to manage their subscription.
*   **Logout**: The user can log out via the Clerk UI, ending their session.