# breakdown.ad Deployment Guide

## Phase 2 Deployment - Core Analysis Pipeline

This guide covers deploying the core ad analysis functionality (Phase 2 of the task plan).

## Prerequisites

1. **Clerk Account** - Set up authentication
2. **Supabase Project** - Database and edge functions
3. **Gemini API Key** - Google AI API access
4. **Vercel Account** - Frontend deployment

## Step 1: Database Setup

### Create Supabase Project
1. Go to [supabase.com](https://supabase.com) and create a new project
2. Note your project URL and anon key
3. Go to Settings > API to get your service role key

### Run Database Migrations
1. Install Supabase CLI: `npm install -g supabase`
2. Login: `supabase login`
3. Link project: `supabase link --project-ref YOUR_PROJECT_ID`
4. Run the schema:
   ```sql
   -- Copy and paste contents from database/schema.sql
   -- into the SQL Editor in Supabase Dashboard
   ```

## Step 2: Supabase Edge Function

### Deploy the Analysis Function
1. In your Supabase project, go to Edge Functions
2. Create a new function called `run-ad-analysis`
3. Copy the code from `database/functions/run-ad-analysis.ts`
4. Set environment variables in the function:
   - `GEMINI_API_KEY`: Your Google AI API key
   - `SUPABASE_URL`: Your Supabase URL
   - `SUPABASE_SERVICE_ROLE_KEY`: Your service role key

## Step 3: Clerk Authentication

### Set up Clerk
1. Create account at [clerk.com](https://clerk.com)
2. Create new application
3. Configure sign-in/sign-up options
4. In Dashboard > Webhooks, add webhook endpoint:
   - URL: `https://your-domain.vercel.app/api/webhooks/clerk`
   - Events: `user.created`, `user.updated`, `user.deleted`
   - Note the webhook secret

## Step 4: Environment Variables

Create `.env.local` with:

```env
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
CLERK_WEBHOOK_SECRET=whsec_...
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/studio
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/studio

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ...
SUPABASE_SERVICE_ROLE_KEY=eyJ...

# Gemini AI API
GEMINI_API_KEY=AIza...

# Application
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
NEXT_PUBLIC_API_URL=https://your-domain.vercel.app/api
```

## Step 5: Deploy to Vercel

### Deploy Frontend
1. Install Vercel CLI: `npm install -g vercel`
2. In the project root: `vercel`
3. Follow prompts to deploy
4. Add environment variables in Vercel dashboard

### Configure Production Settings
1. Set up custom domain (optional)
2. Configure Clerk production settings
3. Update webhook URLs to production domain

## Step 6: Test the Pipeline

### Test Flow
1. Sign up for a new account
2. Verify user is created in Supabase
3. Go to dashboard and paste a YouTube URL
4. Verify analysis is created and triggered
5. Check Supabase logs for function execution
6. Verify analysis results appear on `/ad/[id]` page

### Troubleshooting
- Check Vercel function logs
- Check Supabase Edge Function logs
- Verify all environment variables are set
- Test API endpoints manually

## Phase 2 Features Ready

✅ User authentication and database sync  
✅ Video analysis creation and triggering  
✅ Gemini AI integration for transcript and marketing analysis  
✅ Analysis results display with tabbed interface  
✅ Basic dashboard with analysis management  

## Next Steps (Phase 3+)

- Implement LLM-powered feature generation (marketing copy, social posts, etc.)
- Add Lemon Squeezy payment integration
- Set up credit system and usage tracking
- Deploy production monitoring and error tracking

## Support

For deployment issues, check:
1. Vercel deployment logs
2. Supabase function logs  
3. Network tab in browser dev tools
4. Console errors in browser
