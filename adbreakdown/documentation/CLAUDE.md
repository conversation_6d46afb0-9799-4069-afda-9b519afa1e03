# Kilo Code Development Guide for breakdown.ad

This file provides guidance for any AI assistant when working with code in this repository.

## 1. Project Overview

breakdown.ad is a **framework-intelligent video ad analysis SaaS platform**. It transforms subjective ad evaluations into objective, data-driven insights. Built with Next.js 14, it uses the Google Gemini API to analyze YouTube video ads, apply marketing frameworks, and generate comprehensive reports with competitive intelligence.

## 2. Architecture

### Tech Stack
- **Frontend**: Next.js 14 (App Router), React 18, TypeScript (strict mode)
- **Styling**: Tailwind CSS, ShadCN/ui components, Radix UI primitives
- **Authentication**: Clerk
- **Database**: Supabase (PostgreSQL)
- **AI/ML**: Google Gemini API (1.5 Flash & Pro)
- **Payments**: Lemon Squeezy
- **Deployment**: Vercel & Supabase

### Project Structure
```
/src/app/                 # Next.js App Router pages
├── /(auth)/             # Authentication pages
├── /ad/[id]/            # Dynamic analysis detail pages
├── /api/                # API routes
├── /studio/             # Main user dashboard
└── /billing/            # Subscription management

/src/components/         # React components
├── /ui/                 # Reusable UI components (ShadCN/ui)
└── /studio/             # Dashboard-specific components

/src/lib/                # Utility functions, prompts, configs
/src/hooks/              # Custom React hooks
/src/services/           # API clients & external service integrations
/documentation/          # Project specifications
/database/               # DB migrations, schema, functions
/supabase/               # Supabase CLI files (Edge Functions)
```

## 3. Development Commands

```bash
# Development
npm run dev              # Start development server (http://localhost:3000)
npm run build            # Build for production
npm run lint             # Run ESLint

# Database & Schema Management
./database/update_schema.sh    # Update local schema files
./scripts/schema-diff.sh       # Compare local vs remote schema

# Supabase Functions Deployment
./scripts/deploy-functions.sh  # Deploy all Edge Functions
```

## 4. Key Development Guidelines

### Documentation-Driven Development
**The single source of truth is `documentation/prd.md`**. All development must align with this document.

1.  **ALWAYS reference `documentation/prd.md`** before implementing any feature.
2.  **Follow the folder structure** outlined in this guide and the PRD.
3.  **Implement user flows** as specified in the PRD.
4.  **Adhere to the V2.0 task plan** for development phases.

### Current Development Phase: V2.0
- ✅ **V1.0 Complete**: Core analysis, auth, payments, and dashboard are functional.
- 🔄 **V2.0 In Progress**: Focus is on implementing **Framework Intelligence** and **Competitive Analysis**.

## 5. Core V2.0 Features to Implement

1.  **Framework Intelligence**: AI-driven detection and analysis of ads using marketing frameworks (AIDA, PAS, ADPLAN, etc.).
2.  **Competitive Analysis**: Side-by-side comparison of multiple ads using the same framework.
3.  **Enhanced Visualizations**: Radar charts, interactive timelines, and other UI elements to display framework analysis.
4.  **Market Opportunity Detection**: AI-powered gap analysis based on competitive data.

## 6. Critical Architectural Patterns

### 🚨 MANDATORY: UUID/Slug Resolution Pattern
This is the most critical and recurring issue. **ALL API routes in `/api/analyses/[id]/` MUST handle both UUIDs and slugs.**

```typescript
// ✅ CORRECT - Handles both UUIDs and slugs
const isUUID = /^[0-9a-f]{8}-.../.test(id);
const { data } = await supabase
  .from('ad_analyses')
  .select('*')
  .eq(isUUID ? 'id' : 'slug', id) // Use conditional field
  .single();
```
- **Checklist**: Before modifying any analysis API route, ensure you have added UUID detection, used conditional field selection, and tested with both URL types.

### 2. Credit-First Architecture
ALL AI operations that cost credits **MUST** pre-validate the user's balance and deduct credits **BEFORE** initiating the operation. Use the secure SQL functions provided.
- **Credit Costs**:
    - Basic Analysis: 2 credits
    - LLM Features: 1 credit each
    - Framework Analysis: +1 credit per framework

### 3. Progressive Loading Architecture
The analysis page (`/ad/[id]`) uses a progressive loading pattern. It makes a fast initial call to a `/basic` endpoint for above-the-fold content, then lazy-loads the full data and tab-specific content.

### 4. Modular AI Prompt System
All Gemini prompts are centralized in `/src/lib/prompts/`. When adding new analysis features (like for a new framework), create a new, dedicated prompt file that enforces a JSON response format.

## 7. Database & Schema

- The schema is managed through version-controlled migrations in `/database/migrations/`.
- Key V2.0 tables include `frameworks`, `analysis_frameworks`, `competitive_analyses`, and `competitor_results`.
- Always use the provided scripts (`update_schema.sh`, `schema-diff.sh`) for managing the local schema.

## 8. Supabase Edge Functions

- Core backend logic for AI processing resides in Supabase Edge Functions located in `/supabase/functions/`.
- Key functions: `run-ad-analysis`, `run-llm-feature-generation`.
- New V2.0 logic for framework or competitive analysis should be added to these functions or new, dedicated functions as appropriate.
- Use the `./scripts/deploy-functions.sh` script for deployment.

## 9. Environment Setup

Refer to `.env.example` for all required environment variables. Ensure `GEMINI_API_KEY`, Clerk, Supabase, and Lemon Squeezy keys are correctly configured in your `.env.local` file.

By following these guidelines, you will ensure that all development aligns with the project's architecture, quality standards, and strategic goals as outlined in the V2.0 Product Requirements Document.