# Campaign Agent System

## Overview

The Campaign Agent System is a multi-agent AI system designed to guide users through the campaign creation process. It consists of two main agents:

1. **Idea Briefing Agent** - Guides users through campaign idea development
2. **Script Generation Agent** - Creates video scripts based on campaign ideas

## Features

- **Intelligent Guidance**: Asks targeted questions to build comprehensive campaign briefs
- **Structured Data Extraction**: Automatically extracts campaign elements from conversations
- **Context Awareness**: Uses brand and format context to provide relevant suggestions
- **Seamless Integration**: Works with existing chat system and campaign canvas
- **Progress Tracking**: Shows campaign completion progress
- **Auto Script Generation**: Automatically generates scripts when ideas are complete

## How It Works

### 1. Agent Mode Toggle

Users can switch between regular chat mode and agent mode using the toggle button in the chat interface:
- **Agent Mode (⚡)**: Uses intelligent agents for guided campaign creation
- **Chat Mode (💬)**: Regular chat functionality

### 2. Campaign Briefing Process

The Idea Briefing Agent guides users through these key areas:
- Campaign title and summary
- Hook and problem identification
- Solution and key benefits
- Call to action
- Visual and audio cues
- Target audience and USP

### 3. Automatic Script Generation

When the campaign idea is complete (all required fields filled), the system automatically:
1. Triggers the Script Generation Agent
2. Creates a structured video script
3. Updates the script tab in the Campaign Canvas

## Usage

### Basic Usage

```typescript
import { createCampaignAgentService } from '@/lib/agents/campaign-agent-service';

// Create agent service
const agentService = createCampaignAgentService();

// Process user message
const response = await agentService.processMessage(
  "I want to create a campaign for a new fitness app"
);

console.log(response.content); // AI response
console.log(response.campaignUpdate); // Extracted campaign data
console.log(response.isComplete); // Whether campaign is complete
```

### With Brand and Format Context

```typescript
const brandContext = {
  brand_name: "TechFit",
  tagline: "Fitness meets technology",
  // ... other brand properties
};

const formatContext = {
  name: "Instagram Reel",
  duration: "30 seconds",
  // ... other format properties
};

const response = await agentService.processMessage(
  "Create a campaign for our app launch",
  brandContext,
  formatContext
);
```

### Progress Tracking

```typescript
// Get campaign progress
const progress = agentService.getProgressSummary();
console.log(progress.step); // Current step
console.log(progress.progress); // Completion percentage
console.log(progress.completedFields); // Completed fields
console.log(progress.missingFields); // Missing fields
```

## Integration with UI Components

### CampaignChat Component

The chat component automatically:
- Detects agent mode toggle
- Processes messages through agents when enabled
- Updates campaign canvas with extracted data
- Shows progress indicators

### CampaignCanvas Component

The canvas component:
- Receives agent updates via `campaignUpdate` prop
- Updates idea tab with extracted campaign data
- Updates script tab with generated scripts
- Maintains backward compatibility with existing data

## Data Structures

### Campaign Idea

```typescript
interface CampaignIdea {
  title: string;
  summary: string;
  hook: string;
  problem: string;
  solution: string;
  keyBenefits: string;
  cta: string;
  visualCues: string;
  audioCues: string;
  brandProduct: string;
  targetAudience: string;
  usp: string;
}
```

### Video Script

```typescript
interface VideoScript {
  title: string;
  content: string;
  structure: {
    hook: string;
    problem: string;
    solution: string;
    benefits: string;
    cta: string;
  };
}
```

## API Integration

The agents use the existing `/api/chat/campaign` endpoint, ensuring:
- Consistent API usage
- Proper streaming responses
- Error handling
- Authentication

## Testing

Test the agent system using the provided test functions:

```typescript
import { runAllTests } from '@/lib/agents/test-agents';

// Run all tests
await runAllTests();
```

Or test individual components:

```typescript
import { testCampaignAgents, testWithContext } from '@/lib/agents/test-agents';

await testCampaignAgents();
await testWithContext();
```

## Error Handling

The system includes comprehensive error handling:
- Graceful fallback to regular chat mode
- Stream error recovery
- API timeout handling
- User-friendly error messages

## Future Enhancements

Potential improvements:
- More sophisticated data extraction using structured outputs
- Additional agent types (e.g., creative review agent)
- Integration with external creative tools
- Advanced progress visualization
- Campaign templates and presets

## Troubleshooting

### Common Issues

1. **Agent not responding**: Check if agent mode is enabled and API is accessible
2. **Data not updating**: Verify campaign update handlers are properly connected
3. **Script not generating**: Ensure campaign idea has all required fields

### Debug Mode

Enable debug logging by setting:
```typescript
console.log('Agent state:', agentService.getCurrentState());
console.log('Progress:', agentService.getProgressSummary());
```

## Architecture

```
User Input
    ↓
CampaignChat (UI)
    ↓
CampaignAgentService
    ↓
CampaignAgentWorkflow
    ↓
IdeaBriefingAgent / ScriptGenerationAgent
    ↓
/api/chat/campaign (Gemini API)
    ↓
Response Processing
    ↓
CampaignCanvas (UI Update)
```

The system is designed to be modular, maintainable, and easily extensible for future enhancements.
