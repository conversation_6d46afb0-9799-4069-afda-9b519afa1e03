/** @type {import('next').NextConfig} */
const nextConfig = {
    experimental: {
        reactCompiler: true,
    },
    serverExternalPackages: ['@google-cloud/storage'],
    // Exclude supabase functions from build
    webpack: (config, { isServer }) => {
        config.externals = config.externals || [];
        config.externals.push({
            'https://deno.land/std@0.177.0/http/server.ts': 'commonjs https://deno.land/std@0.177.0/http/server.ts',
        });
        
        // Optimize chunk splitting for better performance
        if (!isServer) {
            config.optimization.splitChunks = {
                ...config.optimization.splitChunks,
                chunks: 'all',
                cacheGroups: {
                    ...config.optimization.splitChunks.cacheGroups,
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                        priority: 10,
                    },
                    ui: {
                        test: /[\\/]src[\\/]components[\\/]ui[\\/]/,
                        name: 'ui',
                        chunks: 'all',
                        priority: 5,
                    },
                    analysis: {
                        test: /[\\/]src[\\/]components[\\/]analysis[\\/]/,
                        name: 'analysis',
                        chunks: 'all',
                        priority: 5,
                    },
                },
            };
        }
        
        return config;
    },
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'i.ytimg.com',
            },
            {
                protocol: 'https',
                hostname: 'img.youtube.com',
            },
            {
                protocol: 'https',
                hostname: 'placehold.co',
            },
            {
                protocol: 'https',
                hostname: 'www.uni.cards',
            },
            {
                protocol: 'https',
                hostname: '**.amazonaws.com',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'images.unsplash.com',
            },
            {
                protocol: 'https',
                hostname: 'via.placeholder.com',
            }
        ],
        formats: ['image/webp', 'image/avif'],
        minimumCacheTTL: 3600,
        dangerouslyAllowSVG: true,
        contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
        deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    },
    // Enable static exports for truly static pages
    output: process.env.NEXT_OUTPUT === 'export' ? 'export' : undefined,
    // Improve caching with static assets
    async headers() {
        const isDev = process.env.NODE_ENV === 'development'
        
        return [
            {
                source: '/(.*)',
                headers: [
                    {
                        key: 'X-Content-Type-Options',
                        value: 'nosniff',
                    },
                    {
                        key: 'X-Frame-Options',
                        value: 'DENY',
                    },
                    {
                        key: 'X-XSS-Protection',
                        value: '1; mode=block',
                    },
                ],
            },
            // Static assets caching - reduced timing for production
            {
                source: '/(.*)\.(ico|png|jpg|jpeg|gif|webp|svg)$',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: isDev 
                            ? 'no-cache, no-store, must-revalidate' 
                            : 'public, max-age=3600, must-revalidate', // 1 hour instead of 1 year
                    },
                ],
            },
            // Font caching - reduced timing for production
            {
                source: '/(.*)\.(woff|woff2|eot|ttf|otf)$',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: isDev 
                            ? 'no-cache, no-store, must-revalidate' 
                            : 'public, max-age=86400, must-revalidate', // 1 day instead of 1 year
                    },
                ],
            },
            // CSS and JS caching - reduced timing for production
            {
                source: '/(.*)\.(css|js)$',
                headers: [
                    {
                        key: 'Cache-control',
                        value: isDev 
                            ? 'no-cache, no-store, must-revalidate' 
                            : 'public, max-age=1800, must-revalidate', // 30 minutes instead of 1 year
                    },
                    {
                        key: 'ETag',
                        value: `"${Date.now()}"`, // Force cache invalidation
                    },
                ],
            },
            // API routes - no caching
            {
                source: '/api/(.*)',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'no-cache, no-store, must-revalidate',
                    },
                ],
            },
            // Dynamic pages - short cache
            {
                source: '/((?!_next|api).)*',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: isDev 
                            ? 'no-cache, no-store, must-revalidate' 
                            : 'public, max-age=300, must-revalidate', // 5 minutes for pages
                    },
                ],
            },
        ]
    },
};

module.exports = nextConfig;