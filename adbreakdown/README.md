# breakdown.ad

AI-Powered Video Ad Analysis SaaS Platform

## Overview

breakdown.ad transforms subjective ad evaluations into objective, data-driven insights through AI-powered analysis, empowering marketers and advertisers to optimize their video campaigns effectively.

## Tech Stack

- **Frontend**: Next.js 14, <PERSON>act, <PERSON><PERSON>, Tailwind CSS
- **UI Components**: ShadCN/ui, Radix UI
- **Authentication**: Clerk
- **Database**: Supabase (PostgreSQL)
- **AI**: Google Gemini API
- **Payments**: Lemon Squeezy
- **Video**: Video.js

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Copy environment variables:
   ```bash
   cp .env.example .env.local
   ```

3. Update `.env.local` with your API keys

4. Run the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000)

## Project Structure

- `/src/app` - Next.js App Router pages
- `/src/components` - React components
- `/src/lib` - Utility functions
- `/src/hooks` - Custom React hooks
- `/src/services` - API clients and services
- `/server` - Backend API (Node.js/Express)
- `/ai-service` - Python AI/ML service
- `/database` - Database migrations and seeds

## Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler

## Documentation

See `/documentation` folder for:
- Product Requirements Document (PRD)
- User Flow Documentation
- Task Implementation Plan
- Architecture Overview
