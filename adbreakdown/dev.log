
> adbreakdown@0.1.0 dev
> next dev

 ⚠ Port 3000 is in use, using available port 3001 instead.
   ▲ Next.js 15.3.4
   - Local:        http://localhost:3001
   - Network:      http://**************:3001
   - Environments: .env.local
   - Experiments (use with caution):
     ✓ reactCompiler

 ✓ Starting...
 ✓ Ready in 4.5s
   Reload env: .env.local
   Reload env: .env.local
   Reload env: .env.local
 ⚠ Failed to reload dynamic routes: Error: You cannot use different slug names for the same dynamic path ('slug' !== 'id').
    at Array.forEach (<anonymous>)
Error: You cannot use different slug names for the same dynamic path ('slug' !== 'id').
    at Array.forEach (<anonymous>)
 ⨯ unhandledRejection: Error: You cannot use different slug names for the same dynamic path ('slug' !== 'id').
    at Array.forEach (<anonymous>)
 ⨯ unhandledRejection:  Error: You cannot use different slug names for the same dynamic path ('slug' !== 'id').
    at Array.forEach (<anonymous>)
 ⚠ Failed to reload dynamic routes: Error: You cannot use different slug names for the same dynamic path ('slug' !== 'id').
    at Array.forEach (<anonymous>)
 ⚠ Failed to reload dynamic routes: Error: You cannot use different slug names for the same dynamic path ('slug' !== 'id').
    at Array.forEach (<anonymous>)
 ⚠ Failed to reload dynamic routes: Error: You cannot use different slug names for the same dynamic path ('slug' !== 'id').
    at Array.forEach (<anonymous>)
 ⚠ Failed to reload dynamic routes: Error: You cannot use different slug names for the same dynamic path ('slug' !== 'id').
    at Array.forEach (<anonymous>)
 ⚠ Failed to reload dynamic routes: Error: You cannot use different slug names for the same dynamic path ('slug' !== 'id').
    at Array.forEach (<anonymous>)
 ○ Compiling /middleware ...
 ✓ Compiled /middleware in 2.4s (224 modules)
<w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (149kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
 ✓ Compiled in 503ms (224 modules)
