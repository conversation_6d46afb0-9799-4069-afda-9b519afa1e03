// Campaign data extraction utilities
export interface CampaignIdeaData {
  title?: string;
  summary?: string;
  hook?: string;
  problem?: string;
  solution?: string;
  keyBenefits?: string;
  cta?: string;
  visualCues?: string;
  audioCues?: string;
  brandProduct?: string;
  targetAudience?: string;
  usp?: string;
}

export interface ScriptData {
  title?: string;
  content?: string;
}

export interface ExtractedCampaignData {
  idea?: Partial<CampaignIdeaData>;
  script?: Partial<ScriptData>;
  shouldGenerateScript?: boolean;
}

// Extract structured data from AI response
export function extractCampaignData(content: string): ExtractedCampaignData {
  const result: ExtractedCampaignData = {};
  
  // Convert to lowercase for pattern matching
  const lowerContent = content.toLowerCase();
  
  // Check if this is a structured campaign response
  const isStructuredResponse = 
    lowerContent.includes('campaign') || 
    lowerContent.includes('brief') ||
    lowerContent.includes('hook') ||
    lowerContent.includes('target audience') ||
    lowerContent.includes('problem') ||
    lowerContent.includes('solution');

  if (!isStructuredResponse) {
    return result;
  }

  // Initialize idea object
  result.idea = {};

  // Extract campaign title
  const titleMatch = content.match(/(?:campaign|title|name):?\s*([^\n]+)/i);
  if (titleMatch) {
    result.idea.title = titleMatch[1].trim().replace(/['"]/g, '');
  }

  // Extract summary/overview
  const summaryPatterns = [
    /(?:summary|overview|concept|brief):?\s*([^\n]+(?:\n(?!\w+:)[^\n]+)*)/i,
    /(?:this campaign|the campaign|this ad)\s+([^.]+\.)/i
  ];
  
  for (const pattern of summaryPatterns) {
    const match = content.match(pattern);
    if (match) {
      result.idea.summary = cleanExtractedText(match[1]);
      break;
    }
  }

  // Extract hook
  const hookPatterns = [
    /(?:hook|opening|attention-grabbing|first.*seconds?):?\s*([^\n]+(?:\n(?!\w+:)[^\n]+)*)/i,
    /(?:starts? with|opens? with|begins? with):?\s*([^\n]+)/i
  ];
  
  for (const pattern of hookPatterns) {
    const match = content.match(pattern);
    if (match) {
      result.idea.hook = cleanExtractedText(match[1]);
      break;
    }
  }

  // Extract problem/need
  const problemPatterns = [
    /(?:problem|pain point|challenge|issue|need):?\s*([^\n]+(?:\n(?!\w+:)[^\n]+)*)/i,
    /(?:addresses?|solves?|tackles?)\s+(?:the\s+)?([^.]+\.)/i
  ];
  
  for (const pattern of problemPatterns) {
    const match = content.match(pattern);
    if (match) {
      result.idea.problem = cleanExtractedText(match[1]);
      break;
    }
  }

  // Extract solution
  const solutionPatterns = [
    /(?:solution|product|service|offer|presents?):?\s*([^\n]+(?:\n(?!\w+:)[^\n]+)*)/i,
    /(?:how.*solves?|introduces?)\s+([^.]+\.)/i
  ];
  
  for (const pattern of solutionPatterns) {
    const match = content.match(pattern);
    if (match) {
      result.idea.solution = cleanExtractedText(match[1]);
      break;
    }
  }

  // Extract key benefits
  const benefitsPatterns = [
    /(?:benefits?|advantages?|value|why choose):?\s*([^\n]+(?:\n(?!\w+:)[^\n]+)*)/i,
    /(?:key benefits?|main benefits?):?\s*((?:[^\n]*\n?)*?)(?=\n\w+:|$)/i
  ];
  
  for (const pattern of benefitsPatterns) {
    const match = content.match(pattern);
    if (match) {
      result.idea.keyBenefits = cleanExtractedText(match[1]);
      break;
    }
  }

  // Extract call to action
  const ctaPatterns = [
    /(?:call to action|cta|action|next step):?\s*([^\n]+(?:\n(?!\w+:)[^\n]+)*)/i,
    /(?:visit|download|sign up|buy|get|try|learn more|contact|subscribe)\s+([^.]+\.)/i
  ];
  
  for (const pattern of ctaPatterns) {
    const match = content.match(pattern);
    if (match) {
      result.idea.cta = cleanExtractedText(match[1]);
      break;
    }
  }

  // Extract visual cues
  const visualPatterns = [
    /(?:visual|visually|scenes?|imagery|graphics?):?\s*([^\n]+(?:\n(?!\w+:)[^\n]+)*)/i,
    /(?:shows?|displays?|features?)\s+([^.]+\.)/i
  ];
  
  for (const pattern of visualPatterns) {
    const match = content.match(pattern);
    if (match) {
      result.idea.visualCues = cleanExtractedText(match[1]);
      break;
    }
  }

  // Extract audio cues
  const audioPatterns = [
    /(?:audio|music|sound|voice|narration):?\s*([^\n]+(?:\n(?!\w+:)[^\n]+)*)/i,
    /(?:soundtrack|background music|voice-?over)\s+([^.]+\.)/i
  ];
  
  for (const pattern of audioPatterns) {
    const match = content.match(pattern);
    if (match) {
      result.idea.audioCues = cleanExtractedText(match[1]);
      break;
    }
  }

  // Extract target audience
  const audiencePatterns = [
    /(?:target audience|audience|demographics?|customers?):?\s*([^\n]+(?:\n(?!\w+:)[^\n]+)*)/i,
    /(?:targeting|aimed at|designed for)\s+([^.]+\.)/i
  ];
  
  for (const pattern of audiencePatterns) {
    const match = content.match(pattern);
    if (match) {
      result.idea.targetAudience = cleanExtractedText(match[1]);
      break;
    }
  }

  // Extract USP
  const uspPatterns = [
    /(?:usp|unique selling proposition|what makes.*different|competitive advantage):?\s*([^\n]+(?:\n(?!\w+:)[^\n]+)*)/i,
    /(?:uniquely|stands out|differentiates)\s+([^.]+\.)/i
  ];
  
  for (const pattern of uspPatterns) {
    const match = content.match(pattern);
    if (match) {
      result.idea.usp = cleanExtractedText(match[1]);
      break;
    }
  }

  // Check if this response contains a script
  const scriptPatterns = [
    /(?:script|screenplay|dialogue):?\s*((?:[^\n]*\n?)*?)(?=\n\w+:|$)/i,
    /\[.*?\]\s*((?:[^\n]*\n?)*?)(?=\n\[|\n\w+:|$)/i // Matches [SCENE] or [ACTION] format
  ];

  for (const pattern of scriptPatterns) {
    const match = content.match(pattern);
    if (match && match[1].trim().length > 50) { // Only if substantial content
      result.script = {
        content: cleanExtractedText(match[1])
      };
      break;
    }
  }

  // Determine if we should generate a script
  const ideaFields = Object.keys(result.idea || {}).length;
  if (ideaFields >= 3 && !result.script) { // If we have enough idea data but no script
    result.shouldGenerateScript = true;
  }

  return result;
}

// Clean extracted text
function cleanExtractedText(text: string): string {
  return text
    .trim()
    .replace(/^['"]+|['"]+$/g, '') // Remove quotes
    .replace(/^\*+|\*+$/g, '') // Remove asterisks
    .replace(/^-+|-+$/g, '') // Remove dashes
    .replace(/\n+/g, ' ') // Replace multiple newlines with space
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim();
}

// Check if idea is complete enough to generate script
export function isIdeaComplete(idea: CampaignIdeaData): boolean {
  const requiredFields = ['summary', 'hook', 'problem', 'solution', 'keyBenefits', 'cta'];
  const completedFields = requiredFields.filter(field => idea[field as keyof CampaignIdeaData]);
  return completedFields.length >= 4; // At least 4 core fields
}

// Generate script prompt based on idea
export function generateScriptPrompt(idea: CampaignIdeaData, format?: any): string {
  let prompt = `Based on the following campaign idea, create a detailed video script:\n\n`;
  
  if (idea.title) prompt += `Campaign: ${idea.title}\n`;
  if (idea.summary) prompt += `Summary: ${idea.summary}\n`;
  if (idea.hook) prompt += `Hook: ${idea.hook}\n`;
  if (idea.problem) prompt += `Problem: ${idea.problem}\n`;
  if (idea.solution) prompt += `Solution: ${idea.solution}\n`;
  if (idea.keyBenefits) prompt += `Key Benefits: ${idea.keyBenefits}\n`;
  if (idea.cta) prompt += `Call to Action: ${idea.cta}\n`;
  if (idea.targetAudience) prompt += `Target Audience: ${idea.targetAudience}\n`;
  
  if (format) {
    prompt += `\nFormat Requirements:\n`;
    if (format.duration) prompt += `Duration: ${format.duration}\n`;
    if (format.dimensions) prompt += `Dimensions: ${format.dimensions}\n`;
    if (format.platform) prompt += `Platform: ${format.platform}\n`;
  }

  prompt += `\nPlease create a comprehensive video script with:
1. Scene descriptions and visual cues
2. Dialogue or voice-over text
3. Timing notes
4. Audio/music cues
5. Graphics and text overlay suggestions

Format the script clearly with scene headers and detailed instructions.`;

  return prompt;
}