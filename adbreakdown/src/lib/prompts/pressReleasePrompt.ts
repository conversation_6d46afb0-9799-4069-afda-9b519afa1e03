
export const getPressReleasePrompt = (videoInfo: string, marketingAnalysis: string) => `
Given the following video information and marketing analysis:

---
Video Content Info:
${videoInfo}

---
Marketing Analysis:
${marketingAnalysis}
---

As a PR expert, generate compelling press release content including:
1. 3 attention-grabbing press release headlines suitable for media distribution
2. 2 professional press release paragraphs (2-4 sentences each) that would appeal to journalists and media outlets
3. 1 executive quote that could be attributed to a company spokesperson

Focus on newsworthiness, credibility, and media appeal. Use a professional, authoritative tone appropriate for press distribution.
`;
