/**
 * Test file for campaign agents
 * This file contains test functions to verify the agent system works correctly
 */

import { createCampaignAgentService } from './campaign-agent-service';

/**
 * Test the campaign agent workflow
 */
export async function testCampaignAgents() {
  console.log('🧪 Testing Campaign Agent System...');
  
  try {
    // Create agent service
    const agentService = createCampaignAgentService();
    console.log('✅ Agent service created successfully');

    // Test 1: Initial greeting
    console.log('\n📝 Test 1: Initial campaign briefing');
    const response1 = await agentService.processMessage(
      "I want to create a campaign for a new fitness app"
    );
    console.log('Response:', response1.content.substring(0, 200) + '...');
    console.log('Next step:', response1.nextStep);
    console.log('Is complete:', response1.isComplete);

    // Test 2: Provide more details
    console.log('\n📝 Test 2: Providing campaign details');
    const response2 = await agentService.processMessage(
      "Title: FitTrack Pro\nSummary: A comprehensive fitness tracking app that helps users achieve their health goals\nHook: Transform your body in 30 days\nProblem: People struggle to stay consistent with their fitness routines\nSolution: Our AI-powered app provides personalized workouts and tracks progress\nKey Benefits: Personalized workouts, progress tracking, community support\nCTA: Download now and start your transformation"
    );
    console.log('Response:', response2.content.substring(0, 200) + '...');
    console.log('Campaign Update:', response2.campaignUpdate);
    console.log('Next step:', response2.nextStep);
    console.log('Is complete:', response2.isComplete);

    // Test 3: Check progress
    console.log('\n📊 Test 3: Progress summary');
    const progress = agentService.getProgressSummary();
    console.log('Progress:', progress);

    // Test 4: Get current state
    console.log('\n📋 Test 4: Current state');
    const state = agentService.getCurrentState();
    console.log('Current step:', state.currentStep);
    console.log('Campaign idea:', state.campaignIdea);

    console.log('\n✅ All tests completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

/**
 * Test with brand and format context
 */
export async function testWithContext() {
  console.log('🧪 Testing with Brand and Format Context...');
  
  try {
    const agentService = createCampaignAgentService();

    // Mock brand context
    const brandContext = {
      brand_name: "TechFit",
      tagline: "Fitness meets technology",
      industry_category: "Health & Fitness",
      positioning_statement: "The smart way to get fit",
      brand_values: ["Innovation", "Health", "Community"],
      voice_attributes: ["Motivational", "Friendly", "Expert"],
      target_demographics: {
        age_range: "25-45",
        interests: ["Fitness", "Technology", "Health"]
      }
    };

    // Mock format context
    const formatContext = {
      name: "Instagram Reel",
      dimensions: "1080x1920",
      duration: "30 seconds",
      platform: "Instagram",
      category: "Social Media Video"
    };

    const response = await agentService.processMessage(
      "Create a campaign for our new fitness app launch",
      brandContext,
      formatContext
    );

    console.log('Response with context:', response.content.substring(0, 300) + '...');
    console.log('✅ Context test completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Context test failed:', error);
    return false;
  }
}

/**
 * Run all tests
 */
export async function runAllTests() {
  console.log('🚀 Running all Campaign Agent tests...\n');
  
  const test1 = await testCampaignAgents();
  const test2 = await testWithContext();
  
  if (test1 && test2) {
    console.log('\n🎉 All tests passed! The agent system is working correctly.');
  } else {
    console.log('\n❌ Some tests failed. Please check the implementation.');
  }
  
  return test1 && test2;
}

// Export for use in browser console or Node.js
if (typeof window !== 'undefined') {
  // Browser environment
  (window as any).testCampaignAgents = {
    testCampaignAgents,
    testWithContext,
    runAllTests
  };
}
