/**
 * Campaign Agent Service
 * 
 * This service provides a simplified interface for integrating the campaign agents
 * with the existing chat system. It handles state management and provides
 * streaming responses compatible with the current UI.
 */

import { CampaignAgentWorkflow, CampaignState, CampaignIdea, VideoScript } from './campaign-agents';

export interface AgentResponse {
  content: string;
  campaignUpdate?: {
    ideaUpdate?: Partial<CampaignIdea>;
    scriptUpdate?: Partial<VideoScript>;
  };
  isComplete: boolean;
  nextStep: 'briefing' | 'script_generation' | 'complete';
}

export interface AgentServiceConfig {
  // No API key needed - using existing Gemini API
  temperature?: number;
  model?: string;
}

/**
 * Service class that manages campaign agent interactions
 */
export class CampaignAgentService {
  private workflow: CampaignAgentWorkflow;

  constructor(config: AgentServiceConfig = {}) {
    this.workflow = new CampaignAgentWorkflow();
  }

  /**
   * Process a user message through the agent workflow
   */
  async processMessage(
    userInput: string,
    brandContext?: any,
    formatContext?: any
  ): Promise<AgentResponse> {
    try {
      // Process through workflow
      const result = await this.workflow.processUserMessage(userInput, brandContext, formatContext);

      return {
        content: result.response,
        campaignUpdate: result.campaignUpdate,
        isComplete: result.isComplete,
        nextStep: result.nextStep
      };

    } catch (error) {
      console.error('Agent service error:', error);
      return {
        content: 'I apologize, but I encountered an error while processing your request. Please try again.',
        isComplete: false,
        nextStep: 'briefing'
      };
    }
  }

  /**
   * Get the current campaign state
   */
  getCurrentState(): CampaignState {
    return this.workflow.getCurrentState();
  }

  /**
   * Reset the agent state (start a new campaign)
   */
  resetState(): void {
    this.workflow.resetState();
  }

  /**
   * Check if the current idea is complete enough for script generation
   */
  isIdeaComplete(): boolean {
    const state = this.workflow.getCurrentState();
    const idea = state.campaignIdea;
    if (!idea) return false;

    const requiredFields = ['title', 'summary', 'hook', 'problem', 'solution', 'keyBenefits', 'cta'];
    return requiredFields.every(field => {
      const value = idea[field as keyof CampaignIdea];
      return typeof value === 'string' && value.trim().length > 0;
    });
  }

  /**
   * Force script generation (if idea is complete)
   */
  async generateScript(): Promise<AgentResponse> {
    if (!this.isIdeaComplete()) {
      return {
        content: 'The campaign idea needs to be more complete before I can generate a script. Please provide more details about the campaign.',
        isComplete: false,
        nextStep: 'briefing'
      };
    }

    return await this.processMessage('Please generate the video script based on the campaign idea we\'ve developed.');
  }

  /**
   * Get campaign progress summary
   */
  getProgressSummary(): {
    step: string;
    completedFields: string[];
    missingFields: string[];
    progress: number;
  } {
    const state = this.workflow.getCurrentState();
    const idea = state.campaignIdea;
    const requiredFields = ['title', 'summary', 'hook', 'problem', 'solution', 'keyBenefits', 'cta'];

    if (!idea) {
      return {
        step: 'Getting Started',
        completedFields: [],
        missingFields: requiredFields,
        progress: 0
      };
    }

    const completedFields = requiredFields.filter(field => {
      const value = idea[field as keyof CampaignIdea];
      return typeof value === 'string' && value.trim().length > 0;
    });

    const missingFields = requiredFields.filter(field => !completedFields.includes(field));
    const progress = Math.round((completedFields.length / requiredFields.length) * 100);

    let step = 'Campaign Briefing';
    if (progress === 100) {
      step = state.script ? 'Complete' : 'Ready for Script Generation';
    } else if (progress > 50) {
      step = 'Refining Details';
    } else if (progress > 0) {
      step = 'Building Foundation';
    }

    return {
      step,
      completedFields,
      missingFields,
      progress
    };
  }
}

/**
 * Factory function to create a campaign agent service
 */
export function createCampaignAgentService(config: AgentServiceConfig = {}): CampaignAgentService {
  return new CampaignAgentService(config);
}

/**
 * Utility function to extract campaign data from AI responses
 * This is a simplified version - in production, you'd want more sophisticated parsing
 */
export function extractCampaignDataFromResponse(response: string, currentIdea?: Partial<CampaignIdea>): Partial<CampaignIdea> {
  const updates: Partial<CampaignIdea> = {};
  
  // Simple regex patterns to extract structured data
  const patterns = {
    title: /(?:title|campaign name):\s*([^\n]+)/i,
    summary: /(?:summary|overview):\s*([^\n]+)/i,
    hook: /(?:hook|opening):\s*([^\n]+)/i,
    problem: /(?:problem|pain point):\s*([^\n]+)/i,
    solution: /(?:solution|how we solve):\s*([^\n]+)/i,
    keyBenefits: /(?:benefits|key benefits):\s*([^\n]+)/i,
    cta: /(?:call to action|cta):\s*([^\n]+)/i,
    targetAudience: /(?:target audience|audience):\s*([^\n]+)/i,
    usp: /(?:usp|unique selling proposition):\s*([^\n]+)/i,
  };

  for (const [field, pattern] of Object.entries(patterns)) {
    const match = response.match(pattern);
    if (match && match[1]) {
      updates[field as keyof CampaignIdea] = match[1].trim();
    }
  }

  return { ...currentIdea, ...updates };
}

/**
 * Utility function to format campaign data for display
 */
export function formatCampaignForDisplay(idea: CampaignIdea): string {
  return `
**Campaign: ${idea.title}**

**Summary:** ${idea.summary}

**Hook:** ${idea.hook}

**Problem:** ${idea.problem}

**Solution:** ${idea.solution}

**Key Benefits:** ${idea.keyBenefits}

**Call to Action:** ${idea.cta}

**Target Audience:** ${idea.targetAudience}

**USP:** ${idea.usp}

**Visual Cues:** ${idea.visualCues}

**Audio Cues:** ${idea.audioCues}
  `.trim();
}
