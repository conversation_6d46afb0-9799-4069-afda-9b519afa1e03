/**
 * Campaign Creation Agent System
 * 
 * This module implements a multi-agent system for campaign creation using LangGraph.
 * It consists of two main agents:
 * 1. Idea Briefing Agent - Guides users through campaign idea development
 * 2. Script Generation Agent - Creates video scripts based on campaign ideas
 */

import { StateGraph, END, START } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { z } from "zod";

// State interface for the agent workflow
export interface CampaignState {
  messages: BaseMessage[];
  currentStep: 'briefing' | 'script_generation' | 'complete';
  campaignIdea: CampaignIdea | null;
  script: VideoScript | null;
  brandContext?: any;
  formatContext?: any;
  userInput: string;
  nextAction: string;
}

// Campaign idea structure
export interface CampaignIdea {
  title: string;
  summary: string;
  hook: string;
  problem: string;
  solution: string;
  keyBenefits: string;
  cta: string;
  visualCues: string;
  audioCues: string;
  brandProduct: string;
  targetAudience: string;
  usp: string;
}

// Video script structure
export interface VideoScript {
  title: string;
  content: string;
  structure: {
    hook: string;
    problem: string;
    solution: string;
    benefits: string;
    cta: string;
  };
}

// Validation schemas
const CampaignIdeaSchema = z.object({
  title: z.string(),
  summary: z.string(),
  hook: z.string(),
  problem: z.string(),
  solution: z.string(),
  keyBenefits: z.string(),
  cta: z.string(),
  visualCues: z.string(),
  audioCues: z.string(),
  brandProduct: z.string(),
  targetAudience: z.string(),
  usp: z.string(),
});

/**
 * Idea Briefing Agent
 * Responsible for guiding users through campaign idea development
 */
export class IdeaBriefingAgent {
  private llm: ChatOpenAI;

  constructor(apiKey: string) {
    this.llm = new ChatOpenAI({
      modelName: "gpt-4",
      temperature: 0.7,
      openAIApiKey: apiKey,
    });
  }

  async processUserInput(state: CampaignState): Promise<Partial<CampaignState>> {
    const systemPrompt = `You are a campaign briefing specialist. Your role is to guide users through creating comprehensive campaign ideas.

Current campaign idea progress:
${state.campaignIdea ? JSON.stringify(state.campaignIdea, null, 2) : 'No idea started yet'}

Brand Context: ${state.brandContext ? JSON.stringify(state.brandContext, null, 2) : 'None provided'}
Format Context: ${state.formatContext ? JSON.stringify(state.formatContext, null, 2) : 'None provided'}

Your tasks:
1. Ask targeted questions to fill in missing campaign idea components
2. Provide suggestions and examples based on brand and format context
3. Extract structured data from user responses
4. Determine when the idea is complete enough to move to script generation

Focus on these key areas in order:
- Campaign title and summary
- Hook and problem identification
- Solution and key benefits
- Call to action
- Visual and audio cues
- Target audience and USP

Respond in a conversational, helpful manner. Ask one focused question at a time.`;

    const messages = [
      new HumanMessage(systemPrompt),
      ...state.messages,
      new HumanMessage(state.userInput)
    ];

    const response = await this.llm.invoke(messages);
    
    // Extract campaign data from response
    const extractedIdea = this.extractCampaignData(response.content as string, state.campaignIdea);
    
    // Determine if idea is complete
    const isComplete = this.isIdeaComplete(extractedIdea);
    
    return {
      messages: [...state.messages, new HumanMessage(state.userInput), response],
      campaignIdea: extractedIdea,
      nextAction: isComplete ? 'generate_script' : 'continue_briefing'
    };
  }

  private extractCampaignData(response: string, currentIdea: CampaignIdea | null): CampaignIdea {
    // Implementation to extract structured data from AI response
    // This would use regex patterns or structured output parsing
    // For now, return current idea or create new one
    return currentIdea || {
      title: '',
      summary: '',
      hook: '',
      problem: '',
      solution: '',
      keyBenefits: '',
      cta: '',
      visualCues: '',
      audioCues: '',
      brandProduct: '',
      targetAudience: '',
      usp: '',
    };
  }

  private isIdeaComplete(idea: CampaignIdea | null): boolean {
    if (!idea) return false;
    
    const requiredFields = ['title', 'summary', 'hook', 'problem', 'solution', 'keyBenefits', 'cta'];
    return requiredFields.every(field => idea[field as keyof CampaignIdea]?.trim().length > 0);
  }
}

/**
 * Script Generation Agent
 * Responsible for creating video scripts based on campaign ideas
 */
export class ScriptGenerationAgent {
  private llm: ChatOpenAI;

  constructor(apiKey: string) {
    this.llm = new ChatOpenAI({
      modelName: "gpt-4",
      temperature: 0.8,
      openAIApiKey: apiKey,
    });
  }

  async generateScript(state: CampaignState): Promise<Partial<CampaignState>> {
    if (!state.campaignIdea) {
      throw new Error('Campaign idea is required for script generation');
    }

    const systemPrompt = `You are a video script writing specialist. Create compelling video ad scripts based on campaign briefs.

Campaign Idea:
${JSON.stringify(state.campaignIdea, null, 2)}

Brand Context: ${state.brandContext ? JSON.stringify(state.brandContext, null, 2) : 'None provided'}
Format Context: ${state.formatContext ? JSON.stringify(state.formatContext, null, 2) : 'None provided'}

Create a structured video script with:
1. Attention-grabbing hook (0-5 seconds)
2. Problem identification (5-10 seconds)
3. Solution presentation (10-20 seconds)
4. Key benefits (20-25 seconds)
5. Strong call to action (25-30 seconds)

Include:
- Scene descriptions
- Visual cues and transitions
- Audio/music suggestions
- Voice-over tone guidance
- Text overlay suggestions

Format the script professionally with clear timing and production notes.`;

    const messages = [
      new HumanMessage(systemPrompt),
      new HumanMessage(`Generate a video script for: ${state.campaignIdea.title}`)
    ];

    const response = await this.llm.invoke(messages);
    
    const script: VideoScript = {
      title: state.campaignIdea.title + ' - Video Script',
      content: response.content as string,
      structure: {
        hook: state.campaignIdea.hook,
        problem: state.campaignIdea.problem,
        solution: state.campaignIdea.solution,
        benefits: state.campaignIdea.keyBenefits,
        cta: state.campaignIdea.cta,
      }
    };

    return {
      messages: [...state.messages, response],
      script,
      currentStep: 'complete',
      nextAction: 'workflow_complete'
    };
  }
}

/**
 * Campaign Agent Workflow
 * Orchestrates the multi-agent campaign creation process
 */
export class CampaignAgentWorkflow {
  private ideaAgent: IdeaBriefingAgent;
  private scriptAgent: ScriptGenerationAgent;
  private workflow: StateGraph<CampaignState>;

  constructor(openAIApiKey: string) {
    this.ideaAgent = new IdeaBriefingAgent(openAIApiKey);
    this.scriptAgent = new ScriptGenerationAgent(openAIApiKey);
    this.workflow = this.buildWorkflow();
  }

  private buildWorkflow(): StateGraph<CampaignState> {
    const workflow = new StateGraph<CampaignState>({
      channels: {
        messages: [],
        currentStep: 'briefing',
        campaignIdea: null,
        script: null,
        userInput: '',
        nextAction: 'continue_briefing'
      }
    });

    // Add nodes
    workflow.addNode("idea_briefing", this.ideaBriefingNode.bind(this));
    workflow.addNode("script_generation", this.scriptGenerationNode.bind(this));

    // Add edges
    workflow.addEdge(START, "idea_briefing");
    workflow.addConditionalEdges(
      "idea_briefing",
      this.shouldGenerateScript.bind(this),
      {
        continue_briefing: "idea_briefing",
        generate_script: "script_generation"
      }
    );
    workflow.addEdge("script_generation", END);

    return workflow;
  }

  private async ideaBriefingNode(state: CampaignState): Promise<Partial<CampaignState>> {
    return await this.ideaAgent.processUserInput(state);
  }

  private async scriptGenerationNode(state: CampaignState): Promise<Partial<CampaignState>> {
    return await this.scriptAgent.generateScript(state);
  }

  private shouldGenerateScript(state: CampaignState): string {
    return state.nextAction === 'generate_script' ? 'generate_script' : 'continue_briefing';
  }

  async processUserMessage(
    userInput: string,
    currentState?: Partial<CampaignState>
  ): Promise<CampaignState> {
    const initialState: CampaignState = {
      messages: [],
      currentStep: 'briefing',
      campaignIdea: null,
      script: null,
      userInput,
      nextAction: 'continue_briefing',
      ...currentState
    };

    const compiledWorkflow = this.workflow.compile();
    const result = await compiledWorkflow.invoke(initialState);
    
    return result;
  }
}
