/**
 * Campaign Creation Agent System
 *
 * This module implements a simplified multi-agent system for campaign creation.
 * It consists of two main agents:
 * 1. Idea Briefing Agent - Guides users through campaign idea development
 * 2. Script Generation Agent - Creates video scripts based on campaign ideas
 *
 * Note: Using a simplified approach that works with existing Gemini API
 */

import { z } from "zod";

// State interface for the agent workflow
export interface CampaignState {
  currentStep: 'briefing' | 'script_generation' | 'complete';
  campaignIdea: CampaignIdea | null;
  script: VideoScript | null;
  brandContext?: any;
  formatContext?: any;
  conversationHistory: Array<{role: 'user' | 'assistant', content: string}>;
  nextAction: string;
}

// Campaign idea structure
export interface CampaignIdea {
  title: string;
  summary: string;
  hook: string;
  problem: string;
  solution: string;
  keyBenefits: string;
  cta: string;
  visualCues: string;
  audioCues: string;
  brandProduct: string;
  targetAudience: string;
  usp: string;
}

// Video script structure
export interface VideoScript {
  title: string;
  content: string;
  structure: {
    hook: string;
    problem: string;
    solution: string;
    benefits: string;
    cta: string;
  };
}

// Validation schemas
const CampaignIdeaSchema = z.object({
  title: z.string(),
  summary: z.string(),
  hook: z.string(),
  problem: z.string(),
  solution: z.string(),
  keyBenefits: z.string(),
  cta: z.string(),
  visualCues: z.string(),
  audioCues: z.string(),
  brandProduct: z.string(),
  targetAudience: z.string(),
  usp: z.string(),
});

/**
 * Idea Briefing Agent
 * Responsible for guiding users through campaign idea development
 */
export class IdeaBriefingAgent {

  constructor() {
    // Simplified constructor - will use existing API
  }

  async processUserInput(
    userInput: string,
    state: CampaignState,
    apiEndpoint: string = '/api/chat/campaign'
  ): Promise<{
    response: string;
    extractedIdea: Partial<CampaignIdea>;
    isComplete: boolean;
  }> {
    const systemPrompt = this.buildSystemPrompt(state);

    // Prepare context for API call
    const contextInfo = this.buildContextInfo(state);

    // Make API call to existing endpoint
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          ...state.conversationHistory.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          { role: 'user', content: userInput }
        ],
        context: [systemPrompt, ...contextInfo],
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to get agent response');
    }

    // Handle streaming response
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response stream');
    }

    const decoder = new TextDecoder();
    let fullContent = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              break;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.content) {
                fullContent += parsed.content;
              }
            } catch (error) {
              console.error('Error parsing agent response:', error);
            }
          }
        }
      }
    } finally {
      reader.cancel();
    }

    // Extract campaign data from response
    const extractedIdea = this.extractCampaignData(fullContent, state.campaignIdea);

    // Determine if idea is complete
    const isComplete = this.isIdeaComplete(extractedIdea);

    return {
      response: fullContent,
      extractedIdea,
      isComplete
    };
  }

  private buildSystemPrompt(state: CampaignState): string {
    return `You are a campaign briefing specialist. Your role is to guide users through creating comprehensive campaign ideas.

CURRENT CAMPAIGN PROGRESS:
${state.campaignIdea ? JSON.stringify(state.campaignIdea, null, 2) : 'No idea started yet'}

INSTRUCTIONS:
1. Ask targeted questions to fill in missing campaign idea components
2. Provide suggestions and examples based on brand and format context
3. Extract structured data from user responses and format it clearly
4. When providing campaign details, use clear labels like "Title:", "Summary:", etc.

FOCUS AREAS (in order of priority):
- Campaign title and summary
- Hook and problem identification
- Solution and key benefits
- Call to action
- Visual and audio cues
- Target audience and USP

RESPONSE FORMAT:
When you identify campaign elements, format them clearly:
Title: [campaign title]
Summary: [brief overview]
Hook: [attention grabber]
Problem: [pain point addressed]
Solution: [how product/service solves it]
Key Benefits: [main value propositions]
CTA: [call to action]
Target Audience: [who this is for]
USP: [unique selling proposition]

Respond conversationally and ask one focused question at a time.`;
  }

  private buildContextInfo(state: CampaignState): string[] {
    const contextInfo: string[] = [];

    if (state.brandContext) {
      contextInfo.push(`BRAND CONTEXT: ${JSON.stringify(state.brandContext, null, 2)}`);
    }

    if (state.formatContext) {
      contextInfo.push(`FORMAT CONTEXT: ${JSON.stringify(state.formatContext, null, 2)}`);
    }

    return contextInfo;
  }

  private extractCampaignData(response: string, currentIdea: CampaignIdea | null): Partial<CampaignIdea> {
    const updates: Partial<CampaignIdea> = {};

    // Extract structured data using regex patterns
    const patterns = {
      title: /(?:^|\n)Title:\s*([^\n]+)/i,
      summary: /(?:^|\n)Summary:\s*([^\n]+)/i,
      hook: /(?:^|\n)Hook:\s*([^\n]+)/i,
      problem: /(?:^|\n)Problem:\s*([^\n]+)/i,
      solution: /(?:^|\n)Solution:\s*([^\n]+)/i,
      keyBenefits: /(?:^|\n)(?:Key Benefits|Benefits):\s*([^\n]+)/i,
      cta: /(?:^|\n)(?:CTA|Call to Action):\s*([^\n]+)/i,
      targetAudience: /(?:^|\n)Target Audience:\s*([^\n]+)/i,
      usp: /(?:^|\n)USP:\s*([^\n]+)/i,
      visualCues: /(?:^|\n)Visual Cues:\s*([^\n]+)/i,
      audioCues: /(?:^|\n)Audio Cues:\s*([^\n]+)/i,
      brandProduct: /(?:^|\n)Brand\/Product:\s*([^\n]+)/i,
    };

    for (const [field, pattern] of Object.entries(patterns)) {
      const match = response.match(pattern);
      if (match && match[1]) {
        updates[field as keyof CampaignIdea] = match[1].trim();
      }
    }

    return { ...currentIdea, ...updates };
  }

  private isIdeaComplete(idea: Partial<CampaignIdea> | null): boolean {
    if (!idea) return false;

    const requiredFields = ['title', 'summary', 'hook', 'problem', 'solution', 'keyBenefits', 'cta'];
    return requiredFields.every(field => {
      const value = idea[field as keyof CampaignIdea];
      return typeof value === 'string' && value.trim().length > 0;
    });
  }
}

/**
 * Script Generation Agent
 * Responsible for creating video scripts based on campaign ideas
 */
export class ScriptGenerationAgent {

  constructor() {
    // Simplified constructor - will use existing API
  }

  async generateScript(
    campaignIdea: CampaignIdea,
    brandContext?: any,
    formatContext?: any,
    apiEndpoint: string = '/api/chat/campaign'
  ): Promise<{
    response: string;
    script: VideoScript;
  }> {
    if (!campaignIdea) {
      throw new Error('Campaign idea is required for script generation');
    }

    const systemPrompt = this.buildScriptPrompt(campaignIdea, brandContext, formatContext);

    // Make API call to existing endpoint
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: `Generate a video script for: ${campaignIdea.title}` }
        ],
        context: [systemPrompt],
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate script');
    }

    // Handle streaming response
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response stream');
    }

    const decoder = new TextDecoder();
    let fullContent = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              break;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.content) {
                fullContent += parsed.content;
              }
            } catch (error) {
              console.error('Error parsing script response:', error);
            }
          }
        }
      }
    } finally {
      reader.cancel();
    }

    const script: VideoScript = {
      title: campaignIdea.title + ' - Video Script',
      content: fullContent,
      structure: {
        hook: campaignIdea.hook,
        problem: campaignIdea.problem,
        solution: campaignIdea.solution,
        benefits: campaignIdea.keyBenefits,
        cta: campaignIdea.cta,
      }
    };

    return {
      response: fullContent,
      script
    };
  }

  private buildScriptPrompt(campaignIdea: CampaignIdea, brandContext?: any, formatContext?: any): string {
    return `You are a video script writing specialist. Create compelling video ad scripts based on campaign briefs.

CAMPAIGN IDEA:
${JSON.stringify(campaignIdea, null, 2)}

BRAND CONTEXT: ${brandContext ? JSON.stringify(brandContext, null, 2) : 'None provided'}
FORMAT CONTEXT: ${formatContext ? JSON.stringify(formatContext, null, 2) : 'None provided'}

SCRIPT STRUCTURE:
Create a structured video script with clear timing:

1. HOOK (0-5 seconds)
   - Attention-grabbing opening
   - Use: ${campaignIdea.hook}

2. PROBLEM (5-10 seconds)
   - Identify the pain point
   - Use: ${campaignIdea.problem}

3. SOLUTION (10-20 seconds)
   - Present your product/service
   - Use: ${campaignIdea.solution}

4. BENEFITS (20-25 seconds)
   - Key value propositions
   - Use: ${campaignIdea.keyBenefits}

5. CALL TO ACTION (25-30 seconds)
   - Strong, clear CTA
   - Use: ${campaignIdea.cta}

INCLUDE IN SCRIPT:
- Scene descriptions in [brackets]
- Visual cues and transitions
- Audio/music suggestions
- Voice-over tone guidance
- Text overlay suggestions
- Camera angles and shots

FORMAT:
Use professional script formatting with clear timing markers and production notes.
Make it engaging, concise, and aligned with the brand voice.`;
  }
}

/**
 * Campaign Agent Workflow
 * Orchestrates the multi-agent campaign creation process
 */
export class CampaignAgentWorkflow {
  private ideaAgent: IdeaBriefingAgent;
  private scriptAgent: ScriptGenerationAgent;
  private state: CampaignState;

  constructor() {
    this.ideaAgent = new IdeaBriefingAgent();
    this.scriptAgent = new ScriptGenerationAgent();
    this.state = {
      currentStep: 'briefing',
      campaignIdea: null,
      script: null,
      conversationHistory: [],
      nextAction: 'continue_briefing'
    };
  }

  async processUserMessage(
    userInput: string,
    brandContext?: any,
    formatContext?: any
  ): Promise<{
    response: string;
    campaignUpdate?: {
      ideaUpdate?: Partial<CampaignIdea>;
      scriptUpdate?: VideoScript;
    };
    isComplete: boolean;
    nextStep: 'briefing' | 'script_generation' | 'complete';
  }> {
    // Update state with context
    this.state.brandContext = brandContext;
    this.state.formatContext = formatContext;

    try {
      if (this.state.currentStep === 'briefing') {
        // Process with idea briefing agent
        const result = await this.ideaAgent.processUserInput(userInput, this.state);

        // Update state
        this.state.conversationHistory.push(
          { role: 'user', content: userInput },
          { role: 'assistant', content: result.response }
        );

        if (result.extractedIdea) {
          this.state.campaignIdea = { ...this.state.campaignIdea, ...result.extractedIdea } as CampaignIdea;
        }

        const campaignUpdate: any = {};
        if (result.extractedIdea && Object.keys(result.extractedIdea).length > 0) {
          campaignUpdate.ideaUpdate = result.extractedIdea;
        }

        // Check if ready for script generation
        if (result.isComplete) {
          this.state.currentStep = 'script_generation';
          this.state.nextAction = 'generate_script';

          // Auto-generate script
          try {
            const scriptResult = await this.scriptAgent.generateScript(
              this.state.campaignIdea!,
              this.state.brandContext,
              this.state.formatContext
            );

            this.state.script = scriptResult.script;
            this.state.currentStep = 'complete';
            campaignUpdate.scriptUpdate = scriptResult.script;

            return {
              response: result.response + '\n\n✅ Campaign idea is complete! I\'ve generated a video script for you.',
              campaignUpdate,
              isComplete: true,
              nextStep: 'complete'
            };
          } catch (scriptError) {
            console.error('Script generation error:', scriptError);
            return {
              response: result.response + '\n\n✅ Campaign idea is complete! You can now generate a script.',
              campaignUpdate,
              isComplete: false,
              nextStep: 'script_generation'
            };
          }
        }

        return {
          response: result.response,
          campaignUpdate: Object.keys(campaignUpdate).length > 0 ? campaignUpdate : undefined,
          isComplete: false,
          nextStep: 'briefing'
        };

      } else if (this.state.currentStep === 'script_generation') {
        // Generate script if not already done
        if (!this.state.script && this.state.campaignIdea) {
          const scriptResult = await this.scriptAgent.generateScript(
            this.state.campaignIdea,
            this.state.brandContext,
            this.state.formatContext
          );

          this.state.script = scriptResult.script;
          this.state.currentStep = 'complete';

          return {
            response: scriptResult.response,
            campaignUpdate: { scriptUpdate: scriptResult.script },
            isComplete: true,
            nextStep: 'complete'
          };
        }
      }

      return {
        response: 'Campaign workflow is complete!',
        isComplete: true,
        nextStep: 'complete'
      };

    } catch (error) {
      console.error('Workflow error:', error);
      return {
        response: 'I apologize, but I encountered an error processing your request. Please try again.',
        isComplete: false,
        nextStep: this.state.currentStep
      };
    }
  }

  getCurrentState(): CampaignState {
    return { ...this.state };
  }

  resetState(): void {
    this.state = {
      currentStep: 'briefing',
      campaignIdea: null,
      script: null,
      conversationHistory: [],
      nextAction: 'continue_briefing'
    };
  }
}
