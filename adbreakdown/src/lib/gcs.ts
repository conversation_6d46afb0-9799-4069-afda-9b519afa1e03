import { Storage } from '@google-cloud/storage'

let storage: Storage

// Initialize Google Cloud Storage
try {
  const gcsCredentials = JSON.parse(process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY || '{}')
  
  storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
    credentials: gcsCredentials,
  })
  
  console.log('✅ GCS Storage utility initialized successfully')
} catch (error) {
  console.error('❌ Failed to initialize GCS storage utility:', error)
  // Fallback initialization
  storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
  })
}

const bucketName = process.env.NEXT_PUBLIC_GCS_BUCKET_NAME

export class GCSService {
  static async deleteFile(gcsUri: string): Promise<boolean> {
    try {
      if (!bucketName) {
        throw new Error('NEXT_PUBLIC_GCS_BUCKET_NAME is not configured')
      }

      // Extract filename from GCS URI (gs://bucket-name/filename)
      const uriPattern = /^gs:\/\/([^\/]+)\/(.+)$/
      const match = gcsUri.match(uriPattern)
      
      if (!match) {
        throw new Error(`Invalid GCS URI format: ${gcsUri}`)
      }

      const [, bucket, fileName] = match
      
      if (bucket !== bucketName) {
        throw new Error(`URI bucket ${bucket} doesn't match configured bucket ${bucketName}`)
      }

      console.log(`🗑️ Deleting file from GCS: ${fileName}`)
      
      const gcsFile = storage.bucket(bucketName).file(fileName)
      
      // Check if file exists first
      const [exists] = await gcsFile.exists()
      if (!exists) {
        console.log(`⚠️ File ${fileName} doesn't exist in GCS, skipping deletion`)
        return true // Consider non-existent files as successfully "deleted"
      }

      // Delete the file
      await gcsFile.delete()
      
      console.log(`✅ Successfully deleted file from GCS: ${fileName}`)
      return true
      
    } catch (error: any) {
      console.error('❌ Failed to delete file from GCS:', {
        gcsUri,
        error: error.message,
        code: error.code
      })
      
      // Don't throw error - log it but don't fail the analysis deletion
      // if GCS cleanup fails
      return false
    }
  }

  static async fileExists(gcsUri: string): Promise<boolean> {
    try {
      if (!bucketName) {
        return false
      }

      const uriPattern = /^gs:\/\/([^\/]+)\/(.+)$/
      const match = gcsUri.match(uriPattern)
      
      if (!match) {
        return false
      }

      const [, bucket, fileName] = match
      
      if (bucket !== bucketName) {
        return false
      }

      const gcsFile = storage.bucket(bucketName).file(fileName)
      const [exists] = await gcsFile.exists()
      
      return exists
      
    } catch (error) {
      console.error('❌ Error checking if GCS file exists:', error)
      return false
    }
  }

  static extractFileNameFromUri(gcsUri: string): string | null {
    try {
      const uriPattern = /^gs:\/\/([^\/]+)\/(.+)$/
      const match = gcsUri.match(uriPattern)
      return match ? match[2] : null
    } catch (error) {
      return null
    }
  }
}