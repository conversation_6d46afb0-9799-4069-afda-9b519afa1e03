'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ArrowRight, BookOpen, Target, Palette, BarChart3, Search } from 'lucide-react'
import Link from 'next/link'
import { Framework } from '@/lib/frameworks'

const categoryIcons: { [key: string]: React.ElementType } = {
  persuasion: Target,
  storytelling: BookOpen,
  analysis: BarChart3,
  branding: Palette,
}

const categoryColors: { [key: string]: string } = {
  persuasion: 'bg-blue-100 text-blue-800 border-blue-200',
  storytelling: 'bg-purple-100 text-purple-800 border-purple-200',
  analysis: 'bg-green-100 text-green-800 border-green-200',
  branding: 'bg-orange-100 text-orange-800 border-orange-200',
}

interface FrameworksClientProps {
  frameworks: Framework[]
}

export default function FrameworksClient({ frameworks }: FrameworksClientProps) {
  const [activeCategory, setActiveCategory] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('popularity')

  const filteredFrameworks = useMemo(() => {
    let filtered = frameworks

    if (activeCategory !== 'all') {
      filtered = filtered.filter(f => f.category === activeCategory)
    }

    if (searchTerm) {
      filtered = filtered.filter(f => 
        f.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        f.summary.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    switch (sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'complexity':
        const complexityOrder = { beginner: 1, intermediate: 2, advanced: 3 }
        filtered.sort((a, b) => complexityOrder[a.complexity] - complexityOrder[b.complexity])
        break;
      // Default to popularity
      default:
        // Assuming the initial order is by popularity
        break
    }

    return filtered
  }, [frameworks, activeCategory, searchTerm, sortBy])

  return (
    <>
      {/* Filtering and Sorting Controls */}
      <div className="mb-12 sticky top-0 z-10 bg-gray-50 py-6">
        <div className="flex flex-col md:flex-row gap-4 items-center">
          <div className="relative w-full md:w-1/3">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input 
              placeholder="Search frameworks..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex-grow flex items-center justify-center gap-2">
            <Button variant={activeCategory === 'all' ? 'default' : 'outline'} onClick={() => setActiveCategory('all')}>All</Button>
            <Button variant={activeCategory === 'persuasion' ? 'default' : 'outline'} onClick={() => setActiveCategory('persuasion')}>Persuasion</Button>
            <Button variant={activeCategory === 'storytelling' ? 'default' : 'outline'} onClick={() => setActiveCategory('storytelling')}>Storytelling</Button>
            <Button variant={activeCategory === 'analysis' ? 'default' : 'outline'} onClick={() => setActiveCategory('analysis')}>Analysis</Button>
            <Button variant={activeCategory === 'branding' ? 'default' : 'outline'} onClick={() => setActiveCategory('branding')}>Branding</Button>
          </div>
          <div className="w-full md:w-auto">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="popularity">Popularity</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="complexity">Complexity</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Frameworks Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredFrameworks.map((framework: Framework) => {
          const CategoryIcon = categoryIcons[framework.category]
          
          return (
            <Card key={framework.slug} className="group flex flex-col bg-white shadow-md hover:shadow-xl transition-all duration-300 hover:-translate-y-1 rounded-lg overflow-hidden">
              <CardHeader className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="text-4xl">{framework.icon}</div>
                    <div>
                      <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                        {framework.name}
                      </CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary" className={`text-xs font-medium ${categoryColors[framework.category]}`}>
                          <CategoryIcon className="w-3 h-3 mr-1.5" />
                          {framework.category}
                        </Badge>
                        {framework.badge && (
                          <Badge variant="outline" className="text-xs font-medium">
                            {framework.badge}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <CardDescription className="text-gray-600 text-sm leading-relaxed h-20">
                  {framework.summary}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="p-6 flex-grow flex flex-col justify-between">
                <div>
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-800 mb-2">Best for:</h4>
                    <div className="flex flex-wrap gap-1.5">
                      {framework.useCase.slice(0, 3).map((useCase, index) => (
                        <Badge key={index} variant="outline" className="font-normal">{useCase}</Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span className="font-medium">Complexity</span>
                    <div className="flex items-center gap-1.5">
                      <div className={`w-4 h-4 rounded-full ${framework.complexity === 'beginner' || framework.complexity === 'intermediate' || framework.complexity === 'advanced' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                      <div className={`w-4 h-4 rounded-full ${framework.complexity === 'intermediate' || framework.complexity === 'advanced' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                      <div className={`w-4 h-4 rounded-full ${framework.complexity === 'advanced' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                      <span className="capitalize ml-1">{framework.complexity}</span>
                    </div>
                  </div>
                </div>

                <div className="mt-6 space-y-2">
                  <Link href={`/frameworks/${framework.slug}`} className="block">
                    <Button variant="outline" className="w-full">
                      Learn Framework
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                  <Link href="/studio" className="block">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                      Try with AI Analysis
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </>
  )
}