'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import Image from 'next/image'

interface ExecutiveBriefingCardProps {
  parsedData: any
}

export default function ExecutiveBriefingCard({ parsedData }: ExecutiveBriefingCardProps) {
  if (!parsedData?.executive_briefing) {
    return null
  }

  const { gut_reaction } = parsedData.executive_briefing

  return (
    <Card className="shadow-sm border border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600">
      <CardContent className="p-6">
        {/* Header with AI Expert */}
        <div className="flex items-center space-x-3 mb-6">
          <Image
            src="/Gemini_Generated_Image_bzln87bzln87bzln.png"
            alt="AI Expert Avatar"
            width={48}
            height={48}
            className="w-12 h-12 rounded-full object-cover"
          />
          <div>
            <h2 className="text-base md:text-lg font-medium text-white mb-0">AI Editor notes</h2>
            <p className="text-white/80 text-sm">Straight from the gut!!</p>
          </div>
        </div>
        
        {/* Gut Reaction Quote */}
        {gut_reaction && (
          <div>
              <blockquote className="text-white text-md italic leading-relaxed">
                &ldquo;{gut_reaction}&rdquo;
              </blockquote>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
