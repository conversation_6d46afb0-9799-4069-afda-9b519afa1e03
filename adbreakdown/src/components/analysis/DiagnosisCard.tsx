'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { AlertTriangle, CheckCircle, Lightbulb } from 'lucide-react'

interface DiagnosisCardProps {
  parsedData: any
}

export default function DiagnosisCard({ parsedData }: DiagnosisCardProps) {
  const diagnosis = parsedData?.diagnosis

  if (!diagnosis) {
    return null
  }

  // Check if there are any pitfalls
  const hasPitfalls = diagnosis.primary_pitfall?.description || 
                     diagnosis.secondary_pitfall?.description

  if (!hasPitfalls) {
    // Show positive message when no issues detected
    return (
      <Card className="shadow-sm border border-gray-200 bg-white">
        <CardHeader className="pb-3">
          <CardTitle className="text-base md:text-lg font-medium text-gray-900 flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 text-gray-500" />
            Strong Strategic Cohesion
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <p className="text-gray-700 leading-relaxed text-sm">
              No major issues detected. This ad demonstrates effective emotional engagement, 
              clear strategic direction, and appropriate next steps for viewers.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardHeader className="pb-3">
        <CardTitle className="text-base md:text-lg font-medium text-gray-900 flex items-center">
          <AlertTriangle className="w-5 h-5 mr-2 text-gray-500" />
          Diagnosis: Issues & Solutions
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Primary Pitfall */}
          {diagnosis.primary_pitfall?.description && (
            <div className="border border-gray-200 rounded-lg p-4 bg-white">
              <h3 className="text-sm md:text-base font-medium text-gray-900 mb-3 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-2" />
                Primary Issue: {diagnosis.primary_pitfall.category}
              </h3>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-gray-800 mb-2 text-sm">The Problem:</h4>
                  <p className="text-gray-700 leading-relaxed text-sm">{diagnosis.primary_pitfall.description}</p>
                </div>
                {diagnosis.primary_pitfall.improvement_signal && (
                  <div className="border-t border-gray-200 pt-3">
                    <h4 className="font-medium text-gray-800 mb-2 flex items-center text-sm">
                      <Lightbulb className="w-4 h-4 mr-1" />
                      The Solution:
                    </h4>
                    <p className="text-gray-700 leading-relaxed text-sm">{diagnosis.primary_pitfall.improvement_signal}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Secondary Pitfall */}
          {diagnosis.secondary_pitfall?.description && (
            <div className="border border-gray-200 rounded-lg p-4 bg-white">
              <h3 className="text-sm md:text-base font-medium text-gray-900 mb-3 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-2" />
                Secondary Issue: {diagnosis.secondary_pitfall.category}
              </h3>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-gray-800 mb-2 text-sm">The Problem:</h4>
                  <p className="text-gray-700 leading-relaxed text-sm">{diagnosis.secondary_pitfall.description}</p>
                </div>
                {diagnosis.secondary_pitfall.improvement_signal && (
                  <div className="border-t border-gray-200 pt-3">
                    <h4 className="font-medium text-gray-800 mb-2 flex items-center text-sm">
                      <Lightbulb className="w-4 h-4 mr-1" />
                      The Solution:
                    </h4>
                    <p className="text-gray-700 leading-relaxed text-sm">{diagnosis.secondary_pitfall.improvement_signal}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}