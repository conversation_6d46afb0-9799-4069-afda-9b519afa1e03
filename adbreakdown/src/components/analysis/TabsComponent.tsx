'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  TrendingUp, Users, Target, Lightbulb
} from 'lucide-react'

interface TabsComponentProps {
  parsedData: any
}

export default function TabsComponent({ parsedData }: TabsComponentProps) {
  const [activeTab, setActiveTab] = useState("audience-market")

  if (!parsedData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Analysis data not available</p>
      </div>
    )
  }

  const tabs = [
    {
      value: "audience-market",
      label: "Audience & Market",
      icon: Users
    },
    {
      value: "brand-strategy",
      label: "Brand & Strategy",
      icon: Target
    },
    {
      value: "creative-culture",
      label: "Creative & Culture",
      icon: Lightbulb
    },
    {
      value: "patterns-predictions",
      label: "Patterns & Predictions",
      icon: TrendingUp
    }
  ]

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      {/* Desktop Tabs - Hidden on mobile */}
      <TabsList className="hidden md:grid h-14 w-full grid-cols-4 bg-blue-50">
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.value}
            value={tab.value}
            className="flex items-center text-sm text-slate-700 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-purple-600 data-[state=active]:text-white hover:bg-orange-75 hover:text-slate-800 transition-all duration-200 data-[state=active]:m-1 data-[state=active]:rounded-lg"
          >
            <tab.icon className="w-6 h-6" />
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>

      {/* Mobile Dropdown - Hidden on desktop */}
      <div className="md:hidden mb-4">
        <Select value={activeTab} onValueChange={setActiveTab}>
          <SelectTrigger className="w-full bg-orange-50 border-orange-200 focus:ring-orange-300 focus:border-orange-300 text-slate-700">
            <SelectValue>
              {tabs.find(tab => tab.value === activeTab) && (
                <div className="flex items-center gap-2">
                  {React.createElement(tabs.find(tab => tab.value === activeTab)!.icon, { className: "w-4 h-4 text-slate-600" })}
                  <span className="text-slate-700">{tabs.find(tab => tab.value === activeTab)!.label}</span>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="bg-orange-50 border-orange-200">
            {tabs.map((tab) => (
              <SelectItem
                key={tab.value}
                value={tab.value}
                className="hover:bg-orange-100 focus:bg-orange-100 data-[highlighted]:bg-orange-100 text-slate-700"
              >
                <div className="flex items-center gap-2">
                  <tab.icon className="w-4 h-4 text-slate-600" />
                  <span>{tab.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      {/* Tab 1: Audience & Market */}
      <TabsContent value="audience-market" className="space-y-6">
        <div className="space-y-4">
          {/* Target Audience */}
          {parsedData.strategic_deep_dive?.brand_and_market_context?.target_audience_evidence && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Users className="w-4 h-4 mr-2" />
                  Target Audience
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.strategic_deep_dive.brand_and_market_context.target_audience_evidence}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Competitive Landscape */}
          {parsedData.strategic_deep_dive?.brand_and_market_context?.competitive_landscape_evidence && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Target className="w-4 h-4 mr-2" />
                  Competitive Landscape
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.strategic_deep_dive.brand_and_market_context.competitive_landscape_evidence}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      {/* Tab 2: Brand & Strategy */}
      <TabsContent value="brand-strategy" className="space-y-6">
        <div className="space-y-4">
          {/* Brand Identity */}
          {parsedData.strategic_deep_dive?.brand_strategy && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Target className="w-4 h-4 mr-2" />
                  Brand Identity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {parsedData.strategic_deep_dive.brand_strategy.brand_position && (
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                      <h4 className="text-xs md:text-sm font-medium text-gray-800 mb-2">Brand Position</h4>
                      <p className="text-gray-700 leading-relaxed">
                        {parsedData.strategic_deep_dive.brand_strategy.brand_position}
                      </p>
                    </div>
                  )}
                  {parsedData.strategic_deep_dive.brand_strategy.brand_archetype && (
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                      <h4 className="text-xs md:text-sm font-medium text-gray-800 mb-2">Brand Archetype</h4>
                      <p className="text-gray-700 leading-relaxed">
                        {parsedData.strategic_deep_dive.brand_strategy.brand_archetype}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* The Hypothetical Brief */}
          {parsedData.strategic_deep_dive?.brand_strategy?.hypothetical_brief && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Lightbulb className="w-4 h-4 mr-2" />
                  Hypothetical Brief
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.strategic_deep_dive.brand_strategy.hypothetical_brief}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      {/* Tab 3: Creative & Culture */}
      <TabsContent value="creative-culture" className="space-y-6">
        <div className="space-y-4">
          {/* The Creative Game Plan */}
          {parsedData.strategic_deep_dive?.creative_and_cultural_context?.creative_game_plan && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Lightbulb className="w-4 h-4 mr-2" />
                  Creative Game Plan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.strategic_deep_dive.creative_and_cultural_context.creative_game_plan}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* The Cultural Hook */}
          {parsedData.strategic_deep_dive?.creative_and_cultural_context?.cultural_hook && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Cultural Hook
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.strategic_deep_dive.creative_and_cultural_context.cultural_hook}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      {/* Tab 4: Patterns & Predictions */}
      <TabsContent value="patterns-predictions" className="space-y-6">
        <div className="space-y-4">
          {/* Industry Patterns */}
          {parsedData.internal_signals?.pattern_recognition && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Industry Patterns
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.internal_signals.pattern_recognition}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Performance Predictions */}
          {parsedData.internal_signals?.prediction_factors && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Target className="w-4 h-4 mr-2" />
                  Performance Predictions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.internal_signals.prediction_factors}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>
    </Tabs>
  )
}