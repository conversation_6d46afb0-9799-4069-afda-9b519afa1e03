'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import MarkdownRenderer from '@/components/ui/markdown-renderer'

interface GeneratedContentSectionsProps {
  marketingScorecard?: string
  seoKeywords?: string
  contentSuggestions?: string
}

export default function GeneratedContentSections({
  marketingScorecard,
  seoKeywords,
  contentSuggestions
}: GeneratedContentSectionsProps) {
  // Helper function to render HTML content (for legacy content)
  const renderHtmlContent = (content: string) => (
    <div className="generated-content" dangerouslySetInnerHTML={{ __html: content.replace(/\n/g, '<br />') }} />
  )

  // Helper function to render markdown content
  const renderMarkdownContent = (content: string | null | undefined) => {
    if (!content) return null;
    let parsedContent = content;
    try {
      // Attempt to parse the content as JSON.
      const json = JSON.parse(content);
      // If it has a raw_content property, use that.
      if (typeof json === 'object' && json !== null && json.raw_content) {
        parsedContent = json.raw_content;
      }
    } catch (e) {
      // If parsing fails, it's likely already a raw string.
      // We'll use the original content.
    }
    return (
      <MarkdownRenderer
        content={parsedContent}
        variant="default"
        className="generated-content table-container"
      />
    );
  }

  return (
    <div className="mt-8 space-y-8">
      {seoKeywords && (
        <Card>
          <CardHeader>
            <CardTitle>Extracted SEO Keywords</CardTitle>
          </CardHeader>
          <CardContent>
            {renderMarkdownContent(seoKeywords)}
          </CardContent>
        </Card>
      )}
      
      {contentSuggestions && (
        <Card>
          <CardHeader>
            <CardTitle>Content Improvement Suggestions</CardTitle>
          </CardHeader>
          <CardContent>
            {renderMarkdownContent(contentSuggestions)}
          </CardContent>
        </Card>
      )}
    </div>
  )
}