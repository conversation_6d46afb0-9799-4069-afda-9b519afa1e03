'use client'

import React, { useState, useEffect, useCallback, lazy, Suspense } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Globe, Share2, Lightbulb, BarChart3
} from 'lucide-react'
import Link from 'next/link'
import ProcessingStatusModal from '@/components/analysis/ProcessingStatusModal'
import VideoPlayer from '@/components/analysis/VideoPlayer'
import { getMarketingAnalysisPrompt } from '@/lib/prompts/marketingAnalysisPrompt'
import LoginOverlay from '@/components/ui/LoginOverlay'
import Navigation from '@/components/Navigation'
// Lazy load heavy chat components
const FloatingChatButton = lazy(() => import('@/components/chat/FloatingChatButton'))
const ChatO<PERSON><PERSON> = lazy(() => import('@/components/chat/ChatOverlay'))
// Lazy load modal components
const DeleteAnalysisModal = lazy(() => import('@/components/analysis/DeleteAnalysisModal'))
const ShareAnalysisModal = lazy(() => import('@/components/analysis/ShareAnalysisModal'))
import LoadingSkeleton from '@/components/analysis/LoadingSkeleton'
import ErrorDisplay from '@/components/analysis/ErrorDisplay'
import TabsComponent from '@/components/analysis/TabsComponent'
import { useAnalysisStream } from '@/hooks/useAnalysisStream'
import ExecutiveBriefingCard from '@/components/analysis/ExecutiveBriefingCard'
import BottomLineCard from '@/components/analysis/BottomLineCard'
import CoreAnalysisCard from '@/components/analysis/CoreAnalysisCard'
import ScorecardSidebar from '@/components/analysis/ScorecardSidebar'
import MetadataSidebar from '@/components/analysis/MetadataSidebar'
import DiagnosisCard from '@/components/analysis/DiagnosisCard'
import CitationsCard from '@/components/analysis/CitationsCard'

// Interfaces
interface VideoMetadata {
  title: string
  thumbnail: string
  duration: string
  inferredBrandName: string
}

interface YouTubeMetadata {
  title: string
  description: string
  channelTitle: string
  publishedAt: string
  viewCount: string
  likeCount: string
  commentCount: string
  tags: string[]
  categoryId: string
  defaultLanguage?: string
  duration: string
  definition: string
}

interface SharedAnalysisPageProps {
  analysisId: string
  isFeaturedMode?: boolean
  preloadedAnalysis?: any
}

// Main Component
export default function SharedAnalysisPage({ 
  analysisId, 
  isFeaturedMode = false, 
  preloadedAnalysis = null 
}: SharedAnalysisPageProps) {
  const { isAuthenticated, loading: authLoading } = useAuth()
  const { checkCredits, deductCredits, loading: creditsLoading } = useCredits()
  const { isStreaming, thinkingText, error: streamError, finalSlug, startStream, resetStream } = useAnalysisStream();
  
  // Log feature flag status on component mount
  console.log('🔧 SharedAnalysisPage initialized with feature flags:', {
    useVertexAi: process.env.NEXT_PUBLIC_USE_VERTEX_AI === 'true',
    environment: process.env.NEXT_PUBLIC_USE_VERTEX_AI || 'undefined',
    analysisId
  })
  const [analysis, setAnalysis] = useState<any>(preloadedAnalysis)
  const [loading, setLoading] = useState(!preloadedAnalysis) // This is for feature generation, not initial analysis
  const [error, setError] = useState('')
  const [youtubeVideoId, setYoutubeVideoId] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [youtubeMetadata, setYoutubeMetadata] = useState<YouTubeMetadata | null>(null)
  const [showLoginOverlay, setShowLoginOverlay] = useState(false)
  const [isInitialLoading, setIsInitialLoading] = useState(!preloadedAnalysis)

  // Core analysis data states (from prototype)
  const [videoInfo, setVideoInfo] = useState('') // Raw transcript + summary text
  const [marketingAnalysis, setMarketingAnalysis] = useState('') // Detailed 10-point analysis
  const [parsedData, setParsedData] = useState<any>(null) // Centralized parsed marketing analysis

  // Generated content states
  const [enhancedScript, setEnhancedScript] = useState('')
  
  
  // Loading states for individual features
  const [enhancedScriptLoading, setEnhancedScriptLoading] = useState(false)
  const [togglePublicLoading, setTogglePublicLoading] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)
  const [isRefreshingAfterCompletion, setIsRefreshingAfterCompletion] = useState(false)
  const [isChatOpen, setIsChatOpen] = useState(false)

  // Centralized data parsing function
  const parseMarketingAnalysis = useCallback((data: any) => {
    if (!data) return null
    try {
      if (typeof data === 'string') {
        let cleanData = data
          .replace(/```json\s*/g, '')
          .replace(/```\s*/g, '')
          .replace(/^\s*```.*$/gm, '')
          .trim()
        
        // Find the first { and last } to extract the JSON object
        const firstBrace = cleanData.indexOf('{')
        const lastBrace = cleanData.lastIndexOf('}')
        
        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
          cleanData = cleanData.substring(firstBrace, lastBrace + 1)
        }
        
        // Try to parse the cleaned data
        const parsed = JSON.parse(cleanData)
        return parsed
      }
      return data
    } catch (error) {
      console.error('Failed to parse marketing analysis:', error)
      console.error('Raw data:', data)
      return null
    }
  }, [])

  // Update parsed data whenever marketing analysis changes
  useEffect(() => {
    const newParsedData = parseMarketingAnalysis(marketingAnalysis)
    setParsedData(newParsedData)
  }, [marketingAnalysis, parseMarketingAnalysis])

  // Extract badges data from analysis
  const getBadges = () => {
    const badges = []
    
    // Brand name - prefer from parsed metadata, fallback to current analysis
    const brandName = parsedData?.metadata?.brand || analysis?.inferred_brand
    if (brandName) {
      badges.push({ label: brandName, variant: 'secondary' as const })
    }
    
    // Product category - prefer from parsed metadata, fallback to current analysis
    const productCategory = parsedData?.metadata?.product_category || analysis?.product_category
    if (productCategory) {
      badges.push({ label: productCategory, variant: 'secondary' as const })
    }
    
    // Campaign category - prefer from parsed metadata, fallback to current analysis
    const campaignCategory = parsedData?.metadata?.campaign_category || analysis?.campaign_category
    if (campaignCategory) {
      badges.push({ label: campaignCategory, variant: 'secondary' as const })
    }
    
    // Celebrity - from parsed metadata
    const celebrity = parsedData?.metadata?.celebrity
    if (celebrity && celebrity.trim() !== '' && celebrity.toLowerCase() !== 'none' && celebrity.toLowerCase() !== 'n/a') {
      // Handle multiple celebrities separated by commas or "and"
      const celebrityList = celebrity.split(/,|\band\b/i).map((name: string) => name.trim()).filter((name: string) => name.length > 0)
      
      // Only add first celebrity to keep badges manageable
      if (celebrityList.length > 0) {
        badges.push({ label: celebrityList[0], variant: 'secondary' as const })
      }
    }
    
    return badges
  }

  // Data states
  const [videoMetadata, setVideoMetadata] = useState<VideoMetadata>({
    title: 'Loading analysis...',
    thumbnail: 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Loading...',
    duration: '0:00',
    inferredBrandName: 'Loading...'
  })
  
  // Removed unused detailedAnalysisData state

  const formatDuration = (seconds: number | null) => {
    if (seconds === null || seconds === undefined) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.round(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const extractYouTubeVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'hqdefault' | 'mqdefault' | 'sddefault' | 'maxresdefault' = 'hqdefault'): string => {
    return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`
  }

  const fetchYouTubeMetadata = async (videoId: string) => {
    try {
      const API_KEY = process.env.NEXT_PUBLIC_YOUTUBE_API_KEY || process.env.NEXT_PUBLIC_GEMINI_API_KEY
      if (!API_KEY) {
        console.warn('No YouTube API key available')
        return
      }

      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${API_KEY}&part=snippet,statistics,contentDetails`
      )
      
      if (!response.ok) {
        throw new Error('Failed to fetch YouTube metadata')
      }
      
      const data = await response.json()
      
      if (data.items && data.items.length > 0) {
        const video = data.items[0]
        const snippet = video.snippet
        const statistics = video.statistics
        const contentDetails = video.contentDetails
        
        // Parse ISO 8601 duration (PT4M13S -> 4:13)
        const parseDuration = (duration: string) => {
          const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
          if (!match) return '0:00'
          
          const hours = parseInt(match[1] || '0')
          const minutes = parseInt(match[2] || '0')
          const seconds = parseInt(match[3] || '0')
          
          if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
          }
          return `${minutes}:${seconds.toString().padStart(2, '0')}`
        }
        
        const metadata: YouTubeMetadata = {
          title: snippet.title || 'Untitled Video',
          description: snippet.description || 'No description available',
          channelTitle: snippet.channelTitle || 'Unknown Channel',
          publishedAt: snippet.publishedAt,
          viewCount: statistics.viewCount || '0',
          likeCount: statistics.likeCount || '0',
          commentCount: statistics.commentCount || '0',
          tags: snippet.tags || [],
          categoryId: snippet.categoryId || '',
          defaultLanguage: snippet.defaultLanguage,
          duration: parseDuration(contentDetails.duration),
          definition: contentDetails.definition || 'sd'
        }
        
        setYoutubeMetadata(metadata)
      }
    } catch (error) {
      console.error('Error fetching YouTube metadata:', error)
    }
  }

  const parseAndSetData = useCallback((analysisData: any) => {
    console.log('parseAndSetData: Received analysis data', analysisData)
    setAnalysis(analysisData)
    
    // Extract YouTube video ID from URL first
    let videoId: string | null = null
    if (analysisData.video_url) {
      videoId = extractYouTubeVideoId(analysisData.video_url)
      setYoutubeVideoId(videoId)
      
      // Fetch YouTube metadata
      if (videoId) {
        fetchYouTubeMetadata(videoId)
      }
    }
    
    // Also check if we have a direct youtube_video_id field
    if (analysisData.youtube_video_id) {
      setYoutubeVideoId(analysisData.youtube_video_id)
      
      // Fetch YouTube metadata if we don't already have it
      if (!videoId) {
        fetchYouTubeMetadata(analysisData.youtube_video_id)
      }
    }
    
    // Use YouTube thumbnail if we have a video ID, otherwise fallback to stored thumbnail
    // Use hqdefault (480x360) instead of maxresdefault (1920x1080) for better performance
    const thumbnailUrl = videoId 
      ? getYouTubeThumbnail(videoId, 'hqdefault')
      : analysisData.video_thumbnail_url || analysisData.thumbnail_url || 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail'
    
    setVideoMetadata({
      title: analysisData.video_title || analysisData.title || 'No Title',
      thumbnail: thumbnailUrl,
      duration: formatDuration(analysisData.video_duration || analysisData.duration_seconds),
      inferredBrandName: analysisData.inferred_brand || 'N/A'
    })

    // Set video info and marketing analysis from database (matching prototype structure)
    if (analysisData.video_info) {
      setVideoInfo(analysisData.video_info)
    } else if (analysisData.transcript && analysisData.summary) {
      setVideoInfo(analysisData.transcript + "\n\n**Summary:**\n" + analysisData.summary)
    }
    
    if (analysisData.marketing_analysis) {
      setMarketingAnalysis(analysisData.marketing_analysis)
    }

    // Set generated content from database (removed unused content types)
    
    // Enhanced script handling
    if (analysisData.deciphered_script?.enhanced_analysis) {
      setEnhancedScript(analysisData.deciphered_script.enhanced_analysis)
    }

    // Simplified data handling - detailed analysis data removed for cleaner code

    // Populate other generated reports (removed unused report types)
  }, [])

  const fetchBasicAnalysis = useCallback(async () => {
    if (!analysisId) return
    setIsInitialLoading(true)
    setLoading(true)
    try {
      // First, fetch basic data quickly for above-the-fold content with cache-busting
      const timestamp = Date.now()
      const basicRes = await fetch(`/api/analyses/${analysisId}/basic?_t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      if (!basicRes.ok) {
        let errorMessage = 'Failed to fetch analysis data.'
        try {
          const errData = await basicRes.json()
          errorMessage = errData.error || errorMessage
        } catch (parseError) {
          // If JSON parsing fails, the server likely returned HTML (like a 500 error page)
          console.error('❌ Server returned non-JSON response:', parseError)
          errorMessage = `Server error (${basicRes.status}): Unable to load analysis`
        }
        
        // If it's an authentication issue, show login overlay instead of error
        if (basicRes.status === 401 || errorMessage.includes('sign in') || errorMessage.includes('private')) {
          console.log('🔒 Analysis requires authentication - showing login overlay')
          setShowLoginOverlay(true)
          setIsInitialLoading(false)
          setLoading(false)
          return
        }
        
        console.error(`❌ Basic analysis fetch failed (${basicRes.status}):`, errorMessage)
        throw new Error(errorMessage)
      }
      const basicData = await basicRes.json()
      console.log('fetchBasicAnalysis: Basic data fetched (cache-busted)', basicData)
      
      // Set basic data immediately for fast rendering
      setAnalysis(basicData)
      setIsInitialLoading(false)
      setLoading(false)
      
      // Then fetch full data in background with a small delay to improve perceived performance
      setTimeout(async () => {
        try {
          const fullTimestamp = Date.now()
          const fullRes = await fetch(`/api/analyses/${analysisId}?_t=${fullTimestamp}`, {
            cache: 'no-store',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          })
          if (fullRes.ok) {
            const fullData = await fullRes.json()
            parseAndSetData(fullData)
          }
        } catch (err) {
          console.warn('Failed to load full analysis data:', err)
        }
      }, 100)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
      setIsInitialLoading(false)
      setLoading(false)
    }
  }, [analysisId, parseAndSetData])

  const fetchAnalysis = useCallback(async () => {
    if (!analysisId) return
    setLoading(true)
    try {
      const timestamp = Date.now()
      const res = await fetch(`/api/analyses/${analysisId}?_t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      if (!res.ok) {
        let errorMessage = 'Failed to fetch analysis data.'
        try {
          const errData = await res.json()
          errorMessage = errData.error || errorMessage
        } catch (parseError) {
          // If JSON parsing fails, the server likely returned HTML (like a 500 error page)
          console.error('❌ Server returned non-JSON response:', parseError)
          errorMessage = `Server error (${res.status}): Unable to load analysis`
        }
        
        // If it's an authentication issue, show login overlay instead of error
        if (res.status === 401 || errorMessage.includes('sign in') || errorMessage.includes('private')) {
          console.log('🔒 Analysis requires authentication - showing login overlay')
          setShowLoginOverlay(true)
          setLoading(false)
          return
        }
        
        console.error(`❌ Full analysis fetch failed (${res.status}):`, errorMessage)
        throw new Error(errorMessage)
      }
      const data = await res.json()
      
      console.log('fetchAnalysis: Fresh data fetched (cache-busted)', data)
      
      parseAndSetData(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
    } finally {
      setLoading(false)
    }
  }, [analysisId, parseAndSetData])

  useEffect(() => {
    // Skip data fetching if we have preloaded analysis
    if (preloadedAnalysis) {
      parseAndSetData(preloadedAnalysis)
      return
    }

    // Use progressive loading when authenticated OR try loading public analysis
    if ((isAuthenticated && !authLoading) || (!authLoading && !isAuthenticated)) {
      fetchBasicAnalysis()
    }
  }, [isAuthenticated, authLoading, fetchBasicAnalysis, preloadedAnalysis, parseAndSetData])

  // Scroll detection for login overlay (only for non-featured mode)
  useEffect(() => {
    // Skip scroll listener for featured mode or authenticated users
    if (isFeaturedMode || isAuthenticated || !analysis?.is_public) {
      return
    }

    const handleScroll = () => {
      const scrollPosition = window.scrollY
      const windowHeight = window.innerHeight
      
      // Show overlay when user scrolls past the second "fold" 
      // This is approximately after the video player and campaign analysis sections
      // Use a more conservative trigger point - 2 viewport heights or when user scrolls significantly
      const triggerPoint = Math.max(windowHeight * 1.2, 800) // Either 1.2 viewport heights or 800px minimum
      
      if (scrollPosition >= triggerPoint && !showLoginOverlay) {
        setShowLoginOverlay(true)
      }
    }

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true })
    
    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [isFeaturedMode, isAuthenticated, analysis?.is_public, showLoginOverlay])

  // Tab handling removed for simplified component

  const handleDeleteAnalysis = async () => {
    setIsDeleting(true)
    setError('')
    
    try {
      const response = await fetch(`/api/analyses/${analysisId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete analysis')
      }
      
      // Redirect to dashboard after successful deletion
      window.location.href = '/studio'
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete analysis')
      setIsDeleting(false)
      setShowDeleteConfirm(false)
    }
  }

  const callGeminiApi = async (_prompt: string, modelName: string, parts: any[], isJsonResponse = false) => {
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY not configured')
    }
    
    // Define fallback models in order of preference
    const fallbackModels = [
      modelName, // Original requested model
      'gemini-1.5-pro', // First fallback
      'gemini-1.5-flash' // Second fallback
    ]
    
    let chatHistory = []
    chatHistory.push({ role: "user", parts: parts })

    // Check if the request is multimodal
    const isMultimodal = parts.some(part => part.fileData);

    const payload: any = { 
      contents: chatHistory,
    }

    // Only add search grounding tool for text-only requests
    if (!isMultimodal) {
      payload.tools = [
        {
          googleSearchRetrieval: {
            dynamicRetrievalConfig: {
              mode: 'MODE_DYNAMIC',
              dynamicThreshold: 0.7
            }
          }
        }
      ]
    }

    if (isJsonResponse) {
      payload.generationConfig = {
        responseMimeType: "application/json",
        temperature: 0.8,
        topP: 0.95,
      }
    } else {
      payload.generationConfig = {
        temperature: 0.8,
        topP: 0.95,
      }
    }

    // Try each model in sequence if previous ones fail
    for (let i = 0; i < fallbackModels.length; i++) {
      const currentModel = fallbackModels[i]
      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${currentModel}:generateContent?key=${apiKey}`

      try {
        console.log(`🤖 Trying model: ${currentModel}`)
        console.log(`🔗 API URL: ${apiUrl}`)
        console.log(`📦 Payload size: ${JSON.stringify(payload).length} bytes`)
        
        // Add timeout and additional headers for better reliability
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 240000) // 60 second timeout (increased from 30)
        
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(payload),
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        console.log(`📡 Response status: ${response.status} ${response.statusText}`)

        if (!response.ok) {
          let errorData;
          try {
            errorData = await response.json()
          } catch (jsonError) {
            console.error('Failed to parse error response as JSON:', jsonError)
            errorData = { error: { message: `HTTP ${response.status}: ${response.statusText}` } }
          }
          
          // If it's a 503 Service Unavailable, try the next model
          if (response.status === 503 && i < fallbackModels.length - 1) {
            console.warn(`⚠️ Model ${currentModel} unavailable (503), trying fallback...`)
            continue
          }
          
          throw new Error(`API Error (${response.status}): ${errorData.error?.message || response.statusText}`)
        }

        const result = await response.json()
        console.log(`📄 Response structure:`, Object.keys(result))
        
        if (result.candidates && result.candidates.length > 0 &&
            result.candidates[0].content && result.candidates[0].content.parts &&
            result.candidates[0].content.parts.length > 0) {
          const textContent = result.candidates[0].content.parts[0].text
          console.log(`✅ Successfully used model: ${currentModel}`)
          console.log(`📝 Response length: ${textContent.length} characters`)
          if (isJsonResponse) {
            // Clean the response to ensure it's valid JSON
            let cleanData = textContent
              .replace(/```json\s*/g, '')
              .replace(/```\s*/g, '')
              .trim()
            
            const firstBrace = cleanData.indexOf('{')
            const lastBrace = cleanData.lastIndexOf('}')
            
            if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
              cleanData = cleanData.substring(firstBrace, lastBrace + 1)
            }
            
            try {
              return JSON.parse(cleanData)
            } catch (e) {
              console.error("Failed to parse cleaned JSON:", e)
              console.error("Cleaned data:", cleanData)
              throw new Error("Received malformed JSON from AI.")
            }
          }
          return textContent
        } else {
          console.error('❌ Invalid response structure:', result)
          throw new Error("Could not get valid response from Gemini.")
        }
      } catch (error) {
        // Enhanced error logging for better debugging
        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            console.error(`⏱️ Request timeout for model ${currentModel} (60 seconds)`)
          } else if (error.message.includes('Failed to fetch')) {
            console.error(`🌐 Network error for model ${currentModel}:`, error)
            console.error('💡 Possible causes: CORS, network connectivity, or API endpoint issues')
          } else {
            console.error(`❌ Error with model ${currentModel}:`, error)
          }
        } else {
          console.error(`❌ Unknown error with model ${currentModel}:`, error)
        }
        
        // If this is the last model, re-throw the error with more context
        if (i === fallbackModels.length - 1) {
          if (error instanceof Error) {
            if (error.message.includes('Failed to fetch')) {
              throw new Error(`Network error: Unable to connect to Gemini API. Please check your internet connection and try again.`)
            }
            if (error.name === 'AbortError') {
              throw new Error(`Request timeout: The AI analysis is taking longer than expected. Please try again.`)
            }
            throw error
          } else {
            throw new Error(`Unknown error occurred: ${String(error)}`)
          }
        }
        
        // Otherwise, continue to next model
        console.log(`🔄 Trying next fallback model...`)
      }
    }
  }

  const handleTriggerInitialAnalysis = useCallback(async () => {
    setError('')
    
    // Check authentication and credits first
    if (!isAuthenticated) {
      setError('Please sign in to run analysis.')
      setLoading(false)
      return
    }
    
    if (!checkCredits(1)) {
      setError('Insufficient credits. Please upgrade your plan to continue.')
      setLoading(false)
      return
    }
    
    try {
      // Deduct credit first
      const creditDeducted = await deductCredits(1)
      if (!creditDeducted) {
        setError('Failed to process credit. Please try again.')
        setLoading(false)
        return
      }
      
      // First get the analysis record with cache-busting
      const timestamp = Date.now()
      const analysisRes = await fetch(`/api/analyses/${analysisId}?_t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      if (!analysisRes.ok) {
        throw new Error('Failed to fetch analysis data.')
      }
      const analysisData = await analysisRes.json()
      
      if (!analysisData.video_url) {
        throw new Error('No video URL found for analysis.')
      }

      let marketingAnalysis;

      console.log('🔧 Feature Flag Status:', {
        useVertexAi: process.env.NEXT_PUBLIC_USE_VERTEX_AI === 'true',
        environment: process.env.NEXT_PUBLIC_USE_VERTEX_AI || 'undefined'
      })

      if (process.env.NEXT_PUBLIC_USE_VERTEX_AI === 'true') {
        // Use Vertex AI API endpoint
        console.log('🚀 ANALYSIS METHOD: Using Vertex AI endpoint')
        console.log('🌊 Starting stream with params:', { analysisId, videoUrl: analysisData.video_url });
        
        await startStream(analysisId, analysisData.video_url);
        
        console.log('✅ startStream call completed');
        // The rest of the logic (saving, etc.) is now handled by the streaming API route and the hook.
        // The `loading` state is now managed by `isStreaming` from the hook.
        return; // Exit here as the stream handles the rest.
      } else {
        // Use direct Gemini API call
        console.log('🚀 ANALYSIS METHOD: Using direct Gemini API')
        console.log('📊 Video URL:', analysisData.video_url)
        console.log('📝 Analysis ID:', analysisId)
        
        // Use local prompt directly for better performance
        const marketingAnalysisPrompt = getMarketingAnalysisPrompt(analysisData.video_url);
        console.log('📋 Using local prompt file for maximum speed')
        
        marketingAnalysis = await callGeminiApi(
          marketingAnalysisPrompt,
          'gemini-2.5-pro', // Use a capable model for the main analysis
          [
            { text: marketingAnalysisPrompt },
            { fileData: { mimeType: "video/mp4", fileUri: analysisData.video_url } }
          ],
          true // Expecting JSON response
        );

        console.log('📄 Raw Gemini API Response:', typeof marketingAnalysis === 'object' ? JSON.stringify(marketingAnalysis, null, 2) : marketingAnalysis)
        console.log('🔍 Marketing Analysis Type:', typeof marketingAnalysis)

        // Update the analysis with marketing analysis only (skip initial analysis)
        console.log('💾 Saving analysis to database...')
        const updateRes = await fetch(`/api/analyses/${analysisId}/update-ai-analysis`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            initialAnalysis: null, // Skip step 1
            marketingAnalysis: marketingAnalysis
          })
        })

        if (!updateRes.ok) {
          const updateError = await updateRes.json()
          console.error('❌ Database Update Error:', updateError)
          throw new Error('Failed to save AI analysis.')
        }
        
        console.log('✅ Analysis saved to database successfully')
      }

      console.log('🔄 Refreshing analysis data...')
      // Refresh the data
      await fetchAnalysis()
      
      console.log('✅ Analysis complete! Method used:', process.env.NEXT_PUBLIC_USE_VERTEX_AI === 'true' ? 'Vertex AI' : 'Direct Gemini API')

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred.'
      console.error('❌ Analysis failed:', errorMessage)
      console.error('❌ Method attempted:', process.env.NEXT_PUBLIC_USE_VERTEX_AI === 'true' ? 'Vertex AI' : 'Direct Gemini API')
      setError(errorMessage)
    }
  }, [isAuthenticated, checkCredits, deductCredits, analysisId, fetchAnalysis, startStream])

  // Auto-trigger marketing analysis if status is pending
  useEffect(() => {
    console.log('🔄 Auto-trigger effect check:', {
      analysisStatus: analysis?.status,
      authLoading,
      isStreaming,
      creditsLoading,
      isFeaturedMode,
      isAuthenticated,
      hasCredits: isAuthenticated ? checkCredits(1) : 'N/A'
    })
    
    // Use isStreaming to prevent re-triggering while an analysis is in progress
    // Only trigger for 'pending' status, never for 'completed', 'generated', or 'processing'
    // Also prevent triggering while refreshing after completion
    if (analysis?.status === 'pending' && !authLoading && !isStreaming && !creditsLoading && !isFeaturedMode && !isRefreshingAfterCompletion) {
      console.log('✅ All conditions met for auto-trigger')
      
      if (isAuthenticated && checkCredits(1)) {
        console.log('🚀 Triggering initial analysis automatically after 1s delay')
        // Add small delay to ensure page is fully loaded
        const timer = setTimeout(() => {
          handleTriggerInitialAnalysis()
        }, 1000)
        return () => clearTimeout(timer)
      } else {
        console.log('❌ Auto-trigger blocked: insufficient credits or not authenticated')
      }
    } else {
      console.log('⏸️ Auto-trigger skipped due to conditions not met')
    }
  }, [analysis?.status, isAuthenticated, authLoading, isStreaming, creditsLoading, checkCredits, handleTriggerInitialAnalysis, isFeaturedMode, isRefreshingAfterCompletion])

  // Effect to handle stream completion
  useEffect(() => {
    if (finalSlug) {
      console.log(`✅ Stream finished. Final slug: ${finalSlug}. Refreshing analysis data.`);
      console.log('🔄 Current state:', { isStreaming, thinkingText: thinkingText.length, error });
      
      // Clear any existing errors
      setError('');
      
      // Set flag to prevent auto-trigger during refresh
      setIsRefreshingAfterCompletion(true);
      
      // Refresh the analysis data to show completed results
      setTimeout(() => {
        console.log('🔄 Fetching updated analysis data...');
        fetchAnalysis();
        
        // Reset the stream state and refresh flag after handling completion
        console.log('🧹 Resetting stream state...');
        resetStream();
        
        // Clear the refresh flag after a delay to ensure status has updated
        setTimeout(() => {
          console.log('🏁 Clearing refresh flag - analysis should now be completed');
          setIsRefreshingAfterCompletion(false);
        }, 2000);
      }, 1000);
    }
  }, [finalSlug, fetchAnalysis, resetStream, error, isStreaming, thinkingText.length]);

  // Effect to handle stream errors
  useEffect(() => {
    if (streamError) {
      setError(streamError);
    }
  }, [streamError]);

  useEffect(() => {
    if (analysis?.title) {
      document.title = `${analysis.title} - breakdown.ad Analysis`
    } else if (youtubeMetadata?.title) {
      document.title = `${youtubeMetadata.title} - breakdown.ad Analysis`
    } else {
      document.title = 'Loading Analysis - breakdown.ad'
    }
  }, [analysis?.title, youtubeMetadata?.title])

  const generateFeature = async (featureType: string, setContent: Function, setLoadingState: Function) => {
    console.log(`🚀 generateFeature called with: ${featureType}`)
    setLoadingState(true)
    setError('')
    
    // Check authentication and credits first
    if (!isAuthenticated) {
      console.error('❌ User not authenticated')
      setError('Please sign in to generate content.')
      setLoadingState(false)
      return
    }
    
    if (!checkCredits(1)) {
      setError('Insufficient credits. Please upgrade your plan to continue.')
      setLoadingState(false)
      return
    }
    
    try {
      console.log(`🚀 Starting LLM feature generation for ${featureType}`)
      
      // Call the proper API endpoint for LLM feature generation
      const response = await fetch(`/api/analyses/${analysisId}/generate-llm-feature`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          report_type_name: featureType
        })
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate feature')
      }
      
      const result = await response.json()
      console.log(`✅ Feature generation completed successfully for ${featureType}:`, result)
      
      // Content is generated synchronously, set it directly
      if (result.generated_content) {
        setContent(result.generated_content)
        console.log(`📝 Setting generated content for ${featureType}`)
      }
      
      // Always refresh analysis data to get the latest state
      fetchAnalysis()
      
      setLoadingState(false)

    } catch (err) {
      console.error(`❌ Error generating ${featureType}:`, err)
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
      setLoadingState(false)
    }
  }

  // Detailed Production Note Generation (3 credits)
  const generateEnhancedScript = async () => {
    console.log('🚀 generateEnhancedScript called')
    setEnhancedScriptLoading(true)
    setError('')
    
    // Check authentication and credits first
    if (!isAuthenticated) {
      console.error('❌ User not authenticated for production note')
      setShowLoginOverlay(true)
      setEnhancedScriptLoading(false)
      return
    }
    
    if (!checkCredits(3)) {
      setError('Insufficient credits. Detailed Production Note requires 3 credits. Please upgrade your plan to continue.')
      setEnhancedScriptLoading(false)
      return
    }
    
    try {
      // Deduct 3 credits first
      const creditDeducted = await deductCredits(3)
      if (!creditDeducted) {
        setError('Failed to process credit. Please try again.')
        setEnhancedScriptLoading(false)
        return
      }
      
      // Call the enhanced script API endpoint
      const response = await fetch(`/api/analyses/${analysisId}/generate-enhanced-script`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        console.error('API Error:', errorData)
        throw new Error(errorData.error || 'Failed to generate production note')
      }
      
      const result = await response.json()
      setEnhancedScript(result.enhanced_analysis)
      fetchAnalysis() // Refresh data
      setEnhancedScriptLoading(false)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred while generating production note.')
      setEnhancedScriptLoading(false)
    }
  }

  const togglePublicStatus = async () => {
    setTogglePublicLoading(true)
    setError('')

    if (!isAuthenticated) {
      setError('Please sign in to modify analysis visibility.')
      setTogglePublicLoading(false)
      return
    }

    try {
      const res = await fetch(`/api/analyses/${analysisId}/toggle-public`, {
        method: 'POST',
      })

      if (!res.ok) {
        const errData = await res.json()
        throw new Error(errData.error || 'Failed to update analysis visibility.')
      }

      const result = await res.json()
      
      // Update local state
      setAnalysis((prev: any) => prev ? { ...prev, is_public: result.analysis.is_public } : prev)
      
      setTogglePublicLoading(false)
      // Show success message briefly
      setTimeout(() => setError(''), 3000)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update visibility.')
      setTogglePublicLoading(false)
    }
  }

  const handleShare = () => {
    setShowShareModal(true)
  }

  const getShareUrl = () => {
    if (!analysis) return ''
    return `${window.location.origin}/ad/${analysis.slug || analysis.id}`
  }

  const getShareText = () => {
    if (!analysis) return ''
    const adTitle = analysis.video_title || analysis.title || `${analysis.inferred_brand || 'Brand'} Ad`
    return `Check out this AI analysis of "${adTitle}" on`
  }

  const shareToTwitter = () => {
    const text = encodeURIComponent(getShareText())
    const url = encodeURIComponent(getShareUrl())
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank')
  }

  const shareToLinkedIn = () => {
    const url = encodeURIComponent(getShareUrl())
    const title = encodeURIComponent(`${analysis?.inferred_brand || 'Brand'} Ad Analysis - breakdown.ad`)
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`, '_blank')
  }

  const shareToWhatsApp = () => {
    const text = encodeURIComponent(`${getShareText()} ${getShareUrl()}`)
    window.open(`https://wa.me/?text=${text}`, '_blank')
  }

  const shareToInstagram = () => {
    // Instagram doesn't have a direct web sharing API, so we copy to clipboard and show instructions
    copyToClipboard()
    alert('Link copied! Open Instagram and paste the link in your story or post.')
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(getShareUrl())
      alert('Link copied to clipboard!')
    } catch (error) {
      console.error('Clipboard error:', error)
      // Fallback: show the URL to copy manually
      prompt('Copy this link:', getShareUrl())
    }
  }

  const formatGeneratedContent = (_: string, content: any): string => {
    if (!content) return 'No content generated.'
    // This function can be expanded to format different report types
    return content.raw_content || content.scorecard_table || JSON.stringify(content, null, 2)
  }

  // Render Logic
  // Only show auth loading if we don't have preloaded analysis and we're still loading auth
  if (authLoading && !preloadedAnalysis && !analysis) {
    return <div className="min-h-screen flex items-center justify-center"><p>Loading...</p></div>
  }
  
  // Allow public access if analysis is public, otherwise require authentication
  const isTestingMode = process.env.NODE_ENV === 'development'
  const isPublicAnalysis = analysis?.is_public === true
  
  // Only block access if we know for certain the analysis is private and user is not authenticated
  // This allows public analyses to load first, then show login overlay on scroll
  if (!isAuthenticated && !isTestingMode && !isFeaturedMode && analysis && analysis.is_public === false) {
    return (
      <div className="min-h-screen bg-white">
        <Navigation />
        <div className="flex items-center justify-center min-h-[calc(100vh-64px)]">
          <div className="text-center">
            <p className="mb-4">Please sign in to view this analysis.</p>
            <Link href="/sign-in"><Button>Sign In</Button></Link>
          </div>
        </div>
      </div>
    )
  }
  if (isInitialLoading) {
    return <LoadingSkeleton />
  }

  if (loading && !analysis) {
    return <div className="min-h-screen flex items-center justify-center"><p>Loading analysis data...</p></div>
  }

  // If analysis is null after loading, show specific error message
  if (!analysis && !loading) {
    const getErrorDetails = () => {
      if (error.includes('not found for')) {
        return {
          title: 'Analysis Not Found',
          description: error,
          actionText: 'Back to Dashboard',
          actionLink: '/studio'
        }
      }
      if (error.includes('private') || error.includes('sign in')) {
        return {
          title: 'Sign In Required',
          description: error,
          actionText: 'Sign In',
          actionLink: '/sign-in'
        }
      }
      if (error.includes('Access denied') || error.includes('belongs to another user')) {
        return {
          title: 'Access Denied',
          description: error,
          actionText: 'Back to Library',
          actionLink: '/ad-library'
        }
      }
      return {
        title: 'Analysis Not Available',
        description: error || `Unable to load analysis: ${analysisId}`,
        actionText: 'Back to Dashboard',
        actionLink: '/studio'
      }
    }
    
    const errorDetails = getErrorDetails()
    
    return (
      <div className="min-h-screen bg-white">
        <Navigation />
        <div className="flex items-center justify-center min-h-[calc(100vh-64px)]">
          <Card className="mb-8 text-center max-w-md">
            <CardHeader>
              <CardTitle>{errorDetails.title}</CardTitle>
              <CardDescription className="text-left">{errorDetails.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href={errorDetails.actionLink}>
                <Button className="w-full">{errorDetails.actionText}</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <Navigation />

      <ProcessingStatusModal
        isOpen={isStreaming}
      />

      <main className="container mx-auto px-3 md:px-4 py-3 md:py-4 max-w-6xl">
        <ErrorDisplay error={error} />

        {!isFeaturedMode && (
          <Suspense fallback={null}>
            <DeleteAnalysisModal 
              isOpen={showDeleteConfirm}
              onClose={() => setShowDeleteConfirm(false)}
              onConfirm={handleDeleteAnalysis}
              isDeleting={isDeleting}
            />

            <ShareAnalysisModal 
              isOpen={showShareModal}
              onClose={() => setShowShareModal(false)}
              analysis={analysis}
              shareHandlers={{
                shareToTwitter,
                shareToLinkedIn,
                shareToWhatsApp,
                shareToInstagram,
                copyToClipboard
              }}
            />
          </Suspense>
        )}

        {console.log('Rendering video/campaign analysis components. analysis:', analysis, 'videoMetadata:', videoMetadata, 'youtubeMetadata:', youtubeMetadata)}
        {analysis && (analysis.video_url || youtubeVideoId) && (
          <div className="mb-3">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1 min-w-0">
                <h1 className="text-lg md:text-xl font-medium text-gray-900">{`${videoMetadata.title} - Ad Analysis`}</h1>
              </div>
              {!isFeaturedMode && (
                <div className="flex items-center gap-2 shrink-0">
                  {/* Publish/Unpublish Toggle Button - Hidden on mobile */}
                  {isAuthenticated && analysis && (
                    <Button
                      onClick={togglePublicStatus}
                      variant={analysis.is_public ? "outline" : "default"}
                      size="sm"
                      disabled={togglePublicLoading}
                      className={`hidden md:flex items-center gap-2 ${
                        analysis.is_public
                          ? "border-gray-300 text-gray-600 hover:bg-gray-50"
                          : "bg-gray-900 hover:bg-gray-800 text-white"
                      }`}
                    >
                      {analysis.is_public ? (
                        <>
                          <Globe className="h-4 w-4" />
                          Unpublish
                        </>
                      ) : (
                        <>
                          <Globe className="h-4 w-4" />
                          Publish
                        </>
                      )}
                    </Button>
                  )}
                  {/* Share Button - Icon only on mobile, full button on desktop */}
                  <Button
                    onClick={handleShare}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Share2 className="h-4 w-4" />
                    <span className="hidden md:inline">Share</span>
                  </Button>
                </div>
              )}
            </div>
            {/* Badges - Horizontal scroll below title and buttons */}
            <div className="flex gap-2 mt-3 overflow-x-auto scrollbar-hide pb-2">
              <div className="flex gap-2 min-w-max">
                {getBadges().map((badge, index) => (
                  <Badge key={index} variant={badge.variant} className="bg-gray-50 text-gray-700 border-gray-200 whitespace-nowrap flex-shrink-0">
                    {badge.label}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Skip step 1 - pending analysis card is disabled */}

        {/* Processing Analysis Section */}
        {analysis?.status === 'processing' && (
          <div className="flex items-center justify-center min-h-[400px]">
            <Card className="w-full max-w-lg text-center">
              <CardHeader>
                <CardTitle className="flex items-center justify-center gap-2">
                  <div className="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                  Analysis Ready to Process
                </CardTitle>
                <CardDescription>
                  Your analysis is queued and ready. Click below to start the AI analysis with live progress updates.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={() => startStream(analysisId, analysis.video_url)}
                  disabled={isStreaming}
                  className="w-full"
                >
                  {isStreaming ? 'Processing...' : 'Start Analysis'}
                </Button>
                {streamError && (
                  <p className="text-red-600 text-sm mt-2">{streamError}</p>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {(analysis?.status === 'completed' || analysis?.status === 'generated' || analysis?.status === 'pending') && (
          <>
            {/* Mobile Layout - Reordered content */}
            <div className="lg:hidden space-y-4">
              {/* Mobile: Video Player first */}
              <section>
                {analysis && (analysis.video_url || youtubeVideoId) && (
                  <div className="w-full aspect-video mb-8">
                    <VideoPlayer
                      youtubeVideoId={youtubeVideoId}
                      videoMetadata={videoMetadata}
                      isLoading={isInitialLoading || loading}
                    />
                  </div>
                )}
              </section>

              {/* Mobile: First 2 sidebar cards */}
              <div className="space-y-4">
                {/* Scorecard Sidebar Module */}
                <ScorecardSidebar parsedData={parsedData} />
                
                {/* Executive Briefing Card (AI Expert Note) */}
                <ExecutiveBriefingCard parsedData={parsedData} />
              </div>
              
              {/* Mobile: Main content cards */}
              <section>
                <BottomLineCard theOneThingThatMatters={parsedData?.executive_briefing?.the_one_thing_that_matters} />
              </section>
              
              <section>
                <CoreAnalysisCard parsedData={parsedData} />
              </section>
              
              <section>
                <DiagnosisCard parsedData={parsedData} />
              </section>
              
              <section className="space-y-6">
                <div className="bg-white rounded-lg p-4 md:p-6 border border-gray-100">
                  <h2 className="text-base md:text-lg font-medium text-gray-900 mb-3 md:mb-4 flex items-center">
                    <BarChart3 className="w-4 h-4 md:w-5 md:h-5 mr-2 text-gray-600" />
                    Strategy Deep Dive
                  </h2>
                  
                  <TabsComponent parsedData={parsedData} />
                </div>
              </section>

              {/* Mobile: Last 2-3 sidebar cards (Ad details and related articles) */}
              <div className="space-y-4">
                {/* Metadata Sidebar Module */}
                <MetadataSidebar
                  parsedData={parsedData}
                  youtubeMetadata={youtubeMetadata}
                  videoMetadata={videoMetadata}
                  validationData={analysis?.llm_correction?.type === 'metadata_validation' ? analysis.llm_correction.validation_result : undefined}
                  analysisId={analysisId}
                  currentMetadata={parsedData?.metadata}
                  isOwner={analysis?.is_owner}
                />

                {/* Citations Card */}
                <CitationsCard citations={parsedData?.citations} />
                
                {/* Generate Insights Sidebar Module */}
                {!isFeaturedMode && (
                  <div className="relative bg-white rounded-lg p-4 md:p-6 border border-gray-100">
                    <h3 className="text-sm md:text-base font-medium text-gray-900 mb-3 flex items-center">
                      <Lightbulb className="w-4 h-4 mr-2 text-gray-600" />
                      Curator Tools
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">
                      As the curator of this ad, use your credits to generate detailed production notes and additional marketing assets.
                    </p>
                    <div className="space-y-3">
                      {analysis?.deciphered_script?.enhanced_analysis || enhancedScript ? (
                        <Link href={`/ad/${analysis.id}/production-note`} className="w-full">
                          <Button className="w-full text-sm" size="sm" variant="outline">
                            View Production Note
                          </Button>
                        </Link>
                      ) : analysis?.is_owner ? (
                        <Button
                          onClick={generateEnhancedScript}
                          disabled={enhancedScriptLoading || loading}
                          className="w-full text-sm"
                          size="sm"
                        >
                          {enhancedScriptLoading ? 'Generating Production Note...' : 'Detailed Production Note (3 Credits)'}
                        </Button>
                      ) : (
                        <div className="text-sm text-gray-500 p-3 border rounded-lg bg-gray-50">
                          Detailed Production Note generation is only available for curators of the ad.
                        </div>
                      )}
                    </div>
                  </div>
                )}
                
                {/* Share Analysis */}
                {!isFeaturedMode && (
                  <div className="bg-white rounded-lg p-4 md:p-6 border border-gray-100">
                    <h3 className="text-sm md:text-base font-medium text-gray-900 mb-3 flex items-center">
                      <Share2 className="w-4 h-4 mr-2 text-gray-600" />
                      Share this Analysis
                    </h3>
                    <Button
                      onClick={handleShare}
                      className="w-full"
                      variant="outline"
                    >
                      Share Analysis
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Desktop Layout - Original layout preserved */}
            <div className="hidden lg:grid grid-cols-1 lg:grid-cols-3 gap-4">
              {/* Main Content Column - 2/3 width */}
              <div className="lg:col-span-2 space-y-4">
                {/* HERO SECTION */}
                <section>
                  {analysis && (analysis.video_url || youtubeVideoId) && (
                    <div className="w-full aspect-video mb-4">
                      <VideoPlayer
                        youtubeVideoId={youtubeVideoId}
                        videoMetadata={videoMetadata}
                        isLoading={isInitialLoading || loading}
                      />
                    </div>
                  )}
                </section>
                
                {/* BOTTOM LINE SECTION */}
                <section>
                  <BottomLineCard theOneThingThatMatters={parsedData?.executive_briefing?.the_one_thing_that_matters} />
                </section>
                
                {/* CORE ANALYSIS SECTION */}
                <section>
                  <CoreAnalysisCard parsedData={parsedData} />
                </section>
                
                {/* DIAGNOSIS SECTION */}
                <section>
                  <DiagnosisCard parsedData={parsedData} />
                </section>
                
                {/* DEEP DIVE SECTION */}
                <section className="space-y-6">
                  <div className="bg-white rounded-lg p-4 md:p-6 border border-gray-100">
                    <h2 className="text-base md:text-lg font-medium text-gray-900 mb-3 md:mb-4 flex items-center">
                      <BarChart3 className="w-4 h-4 md:w-5 md:h-5 mr-2 text-gray-600" />
                      Strategy Deep Dive
                    </h2>
                    
                    <TabsComponent parsedData={parsedData} />
                  </div>
                </section>
                
              </div>
              
              {/* SIDEBAR: SUPPORTING DATA */}
              <div className="lg:col-span-1">
                <div className="lg:sticky lg:top-6 space-y-4">
                  {/* Scorecard Sidebar Module */}
                  <ScorecardSidebar parsedData={parsedData} />
                  
                  {/* Executive Briefing Card (Gut Reaction) */}
                  <ExecutiveBriefingCard parsedData={parsedData} />

                  {/* Metadata Sidebar Module */}
                  <MetadataSidebar
                    parsedData={parsedData}
                    youtubeMetadata={youtubeMetadata}
                    videoMetadata={videoMetadata}
                    validationData={analysis?.llm_correction?.type === 'metadata_validation' ? analysis.llm_correction.validation_result : undefined}
                    analysisId={analysisId}
                    currentMetadata={parsedData?.metadata}
                    isOwner={analysis?.is_owner}
                  />
                  
                  {/* Citations Card */}
                  <CitationsCard citations={parsedData?.citations} />
                  
                  {/* Generate Insights Sidebar Module */}
                  {!isFeaturedMode && (
                    <div className="relative bg-white rounded-lg p-4 md:p-6 border border-gray-100">
                      <h3 className="text-sm md:text-base font-medium text-gray-900 mb-3 flex items-center">
                        <Lightbulb className="w-4 h-4 mr-2 text-gray-600" />
                        Curator Tools
                      </h3>
                      <p className="text-gray-600 text-sm mb-4">
                        As the curator of this ad, use your credits to generate detailed production notes and additional marketing assets.
                      </p>
                      <div className="space-y-3">
                        {analysis?.deciphered_script?.enhanced_analysis || enhancedScript ? (
                          <Link href={`/ad/${analysis.id}/production-note`} className="w-full">
                            <Button className="w-full text-sm" size="sm" variant="outline">
                              View Production Note
                            </Button>
                          </Link>
                        ) : analysis?.is_owner ? (
                          <Button
                            onClick={generateEnhancedScript}
                            disabled={enhancedScriptLoading || loading}
                            className="w-full text-sm"
                            size="sm"
                          >
                            {enhancedScriptLoading ? 'Generating Production Note...' : 'Detailed Production Note (3 Credits)'}
                          </Button>
                        ) : (
                          <div className="text-sm text-gray-500 p-3 border rounded-lg bg-gray-50">
                            Detailed Production Note generation is only available for curators of the ad.
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Share Analysis */}
                  {!isFeaturedMode && (
                    <div className="bg-white rounded-lg p-4 md:p-6 border border-gray-100">
                      <h3 className="text-sm md:text-base font-medium text-gray-900 mb-3 flex items-center">
                        <Share2 className="w-4 h-4 mr-2 text-gray-600" />
                        Share this Analysis
                      </h3>
                      <Button
                        onClick={handleShare}
                        className="w-full"
                        variant="outline"
                      >
                        Share Analysis
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </main>
      
      {/* Login Overlay for unauthenticated users viewing public analyses */}
      {showLoginOverlay && !isAuthenticated && analysis?.is_public && !isFeaturedMode && (
        <LoginOverlay 
          isVisible={showLoginOverlay}
          title="Unlock Full Analysis"
          description="Sign up to access the complete analysis with detailed insights, AI-powered recommendations, and advanced features."
        />
      )}

      {/* Chat Components */}
      {!isFeaturedMode && parsedData && (
        <Suspense fallback={null}>
          <FloatingChatButton 
            onClick={() => setIsChatOpen(!isChatOpen)}
            isOpen={isChatOpen}
          />
          <ChatOverlay 
            isOpen={isChatOpen}
            onClose={() => setIsChatOpen(false)}
            adContext={parsedData}
          />
        </Suspense>
      )}
    </div>
  )
}
