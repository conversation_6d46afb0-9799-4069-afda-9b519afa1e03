'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MessageSquare, Target, Lightbulb, CheckCircle, TrendingUp } from 'lucide-react'

interface CoreAnalysisCardProps {
  parsedData: any
}

interface AnalysisSection {
  title: string
  content: string
  icon: React.ReactNode
  color: string
}

export default function CoreAnalysisCard({ parsedData }: CoreAnalysisCardProps) {
  if (!parsedData?.core_analysis?.essay) {
    return null
  }

  const parseEssayIntoSections = (essay: string): AnalysisSection[] => {
    const sections: AnalysisSection[] = []
    
    // Split by markdown headers (looking for **text:** patterns)
    const parts = essay.split(/\*\*([^*]+):\*\*/)
    
    // If we have parts, process them
    if (parts.length > 1) {
      for (let i = 1; i < parts.length; i += 2) {
        const title = parts[i].trim()
        const content = parts[i + 1]?.trim() || ''
        
        if (title && content) {
          // Assign icons and colors based on section titles
          let icon = <MessageSquare className="w-5 h-5" />
          let color = 'blue'
          
          if (title.toLowerCase().includes('insight')) {
            icon = <Lightbulb className="w-5 h-5" />
            color = 'yellow'
          } else if (title.toLowerCase().includes('execution')) {
            icon = <Target className="w-5 h-5" />
            color = 'green'
          } else if (title.toLowerCase().includes('proof')) {
            icon = <CheckCircle className="w-5 h-5" />
            color = 'purple'
          } else if (title.toLowerCase().includes('strategic') || title.toLowerCase().includes('takeaway')) {
            icon = <TrendingUp className="w-5 h-5" />
            color = 'orange'
          } else if (title.toLowerCase().includes('debrief')) {
            icon = <MessageSquare className="w-5 h-5" />
            color = 'indigo'
          }
          
          sections.push({ title, content, icon, color })
        }
      }
    }
    
    // If no sections found, return the whole essay as one section
    
    if (sections.length === 0) {
      sections.push({
        title: '',
        content: essay,
        icon:'',
        color: 'blue'
      })
    }
    
    return sections
  }

  const sections = parseEssayIntoSections(parsedData.core_analysis.essay)

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: { border: 'border-gray-200', bg: 'bg-white', text: 'text-gray-700', icon: 'text-gray-500' },
      yellow: { border: 'border-gray-200', bg: 'bg-white', text: 'text-gray-700', icon: 'text-gray-500' },
      green: { border: 'border-gray-200', bg: 'bg-white', text: 'text-gray-700', icon: 'text-gray-500' },
      purple: { border: 'border-gray-200', bg: 'bg-white', text: 'text-gray-700', icon: 'text-gray-500' },
      orange: { border: 'border-gray-200', bg: 'bg-white', text: 'text-gray-700', icon: 'text-gray-500' },
      indigo: { border: 'border-gray-200', bg: 'bg-white', text: 'text-gray-700', icon: 'text-gray-500' }
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardHeader className="pb-3">
        <CardTitle className="text-base md:text-lg font-medium text-gray-900 flex items-center">
          <MessageSquare className="w-5 h-5 mr-2 text-gray-500" />
          Executive Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {sections.map((section, index) => {
            const colors = getColorClasses(section.color)
            return (
              <Card key={index} className={`${colors.border} ${colors.bg} border shadow-none`}>
                <CardHeader className="pb-2">
                  <CardTitle className={`text-sm md:text-base font-medium ${colors.text} flex items-center`}>
                    <span className={colors.icon}>{section.icon}</span>
                    <span className="ml-2">{section.title}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className={`${colors.text} leading-relaxed whitespace-pre-wrap text-sm`}>
                    {section.content}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}