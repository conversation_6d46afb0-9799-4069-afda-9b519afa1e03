'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { BarChart3, Info } from 'lucide-react'
import RatingLogicModal from '@/components/analysis/RatingLogicModal'

interface ScorecardSidebarProps {
  parsedData: any
}

export default function ScorecardSidebar({ parsedData }: ScorecardSidebarProps) {
  const scorecard = parsedData?.executive_briefing?.scorecard
  const [showRatingLogicModal, setShowRatingLogicModal] = React.useState(false)
  const [showFullJustification, setShowFullJustification] = React.useState(false)

  if (!scorecard) {
    return null
  }

  const formatScoreLabel = (key: string) => {
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  const getScoreBadge = (score: number) => {
    if (score === 10) return { label: 'Unicorn', color: 'bg-emerald-100 text-emerald-800 border-emerald-200' }
    if (score === 9) return { label: 'Game-Changer', color: 'bg-green-100 text-green-800 border-green-200' }
    if (score === 8) return { label: 'Powerhouse', color: 'bg-lime-100 text-lime-800 border-lime-200' }
    if (score === 7) return { label: 'Promising', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
    if (score >= 5) return { label: 'Table Stakes', color: 'bg-amber-100 text-amber-800 border-amber-200' }
    if (score >= 3) return { label: 'Off Target', color: 'bg-orange-100 text-orange-800 border-orange-200' }
    return { label: 'Misfire', color: 'bg-red-100 text-red-800 border-red-200' }
  }

  const getProgressBarColor = (score: number) => {
    // Red-Amber-Green scale
    const normalizedScore = Math.max(1, Math.min(10, score)) // Clamp between 1-10
    
    if (normalizedScore <= 5) {
      // Red to Amber (scores 1-5)
      const ratio = (normalizedScore - 1) / 4 // 0-1 scale for red to amber
      const red = Math.round(220 - (220 - 245) * ratio) // Red 220 to Amber 245
      const green = Math.round(38 + (158 - 38) * ratio)  // Red 38 to Amber 158
      const blue = Math.round(38 + (11 - 38) * ratio)    // Red 38 to Amber 11
      return `rgb(${red}, ${green}, ${blue})`
    } else {
      // Amber to Green (scores 6-10)
      const ratio = (normalizedScore - 6) / 4 // 0-1 scale for amber to green
      const red = Math.round(245 - (245 - 34) * ratio)   // Amber 245 to Green 34
      const green = Math.round(158 + (197 - 158) * ratio) // Amber 158 to Green 197
      const blue = Math.round(11 + (94 - 11) * ratio)     // Amber 11 to Green 94
      return `rgb(${red}, ${green}, ${blue})`
    }
  }

  const individualScores = Object.entries(scorecard).filter(([key]) => key !== 'overall_impact_score');

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">

      <CardContent className="pt-4">

                
        {/* Disclaimer / Experimental Tag */}
        <div className="flex items-center text-xs mb-0 text-blue-600 cursor-pointer hover:text-blue-800 w-fit ml-auto"
             onClick={() => setShowRatingLogicModal(true)}>
          <span className="font-base">Experimental</span>
          <Info className="w-3 h-3 ml-1 text-blue-600" />
        </div>

        {/* Overall Score - Prominent Display */}
        {scorecard.overall_impact_score && (
          <div className="text-center mb-3 pb-3 border-b border-gray-200">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1">
              {scorecard.overall_impact_score.score}
              <span className="text-lg text-gray-500">/10</span>
            </div>
            {(() => {
              const badge = getScoreBadge(scorecard.overall_impact_score.score)
              return (
                <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${badge.color} mb-2`}>
                  {badge.label}
                </div>
              )
            })()}
            {/* <div className="text-xs font-medium text-gray-600 mb-2">Overall Score</div> */}
            <div className="text-xs text-gray-500 leading-relaxed text-left">
              {scorecard.overall_impact_score.justification.length > 200 ? (
                <div>
                  {showFullJustification ? (
                    <div>
                      {scorecard.overall_impact_score.justification}
                      <button
                        onClick={() => setShowFullJustification(false)}
                        className="text-blue-600 hover:text-blue-800 font-medium ml-1"
                      >
                        Read Less
                      </button>
                    </div>
                  ) : (
                    <div className="relative">
                      <div className="line-clamp-5">
                        {scorecard.overall_impact_score.justification}
                      </div>
                      <div className="absolute bottom-0 right-0 bg-white pl-2">
                        <button
                          onClick={() => setShowFullJustification(true)}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          ...Read More
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div>{scorecard.overall_impact_score.justification}</div>
              )}
            </div>
          </div>
        )}

        
        {/* Individual Scores */}
        <div className="space-y-2">
          {individualScores.map(([key, value]) => {
            const score = (value as any).score
            const progressWidth = (score / 10) * 100
            const barColor = getProgressBarColor(score)
            
            return (
              <div key={key} className="group relative">
                <div className="flex justify-between items-center py-0 px-3 rounded-lg hover:bg-gray-50 transition-colors cursor-help">
                  <span className="text-sm font-medium text-gray-700">{formatScoreLabel(key)}</span>
                  <div className="flex items-center ml-4">
                    <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                      <div 
                        className="h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progressWidth}%`, backgroundColor: barColor }}
                      ></div>
                    </div>
                    <Info className="w-3 h-3 text-gray-400" />
                  </div>
                </div>
                {/* Custom Tooltip */}
                <div className="absolute left-0 top-full mt-2 w-full bg-white border border-gray-200 text-gray-700 text-xs rounded-lg px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 pointer-events-none shadow-lg">
                  {(value as any).justification}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>

      <RatingLogicModal 
        isOpen={showRatingLogicModal}
        onClose={() => setShowRatingLogicModal(false)}
      />
    </Card>
  )
}