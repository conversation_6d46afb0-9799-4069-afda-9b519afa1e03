
'use client'

import React, { Suspense, lazy, useState } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { PlayCircle } from 'lucide-react'

const LazyYouTube = lazy(() => import('react-youtube'))

interface VideoPlayerProps {
  youtubeVideoId: string | null
  videoMetadata: {
    thumbnail: string
    title: string
    duration: string
  }
  isLoading?: boolean
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  youtubeVideoId,
  videoMetadata,
  isLoading = false
}) => {
  const [showPlayer, setShowPlayer] = useState(false)

  const youtubeOpts = {
    height: '100%',
    width: '100%',
    playerVars: {
      autoplay: 1,
      modestbranding: 1,
      rel: 0,
    },
  }

  const handlePlayClick = () => {
    setShowPlayer(true)
  }

  return (
    <Card className="h-full w-full">
      <CardContent className="p-0">
        <div className="relative bg-black rounded-lg overflow-hidden aspect-video">
          {showPlayer && youtubeVideoId ? (
            <div className="absolute inset-0">
              <Suspense fallback={<Skeleton className="w-full h-full" />}>
                <LazyYouTube
                  videoId={youtubeVideoId}
                  opts={youtubeOpts}
                  className="w-full h-full"
                  iframeClassName="w-full h-full rounded-lg"
                />
              </Suspense>
            </div>
          ) : (
            <div className="relative w-full h-full cursor-pointer" onClick={handlePlayClick}>
              <Image 
                src={videoMetadata.thumbnail}
                alt={videoMetadata.title}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                style={{ objectFit: 'cover' }}
                className="rounded-lg"
                priority={false}
                loading="lazy"
                quality={85}
              />
              <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                <PlayCircle className="w-16 h-16 text-white opacity-80 hover:opacity-100 transition-opacity" />
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default VideoPlayer
