'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ChevronDown, Menu, X } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import { UserButton } from '@clerk/nextjs'
import Image from 'next/image'

export default function Navigation() {
  const { isAuthenticated } = useAuth()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const pathname = usePathname()

  // Helper function to check if a path is active
  const isActivePath = (path: string) => {
    if (path === '/') return pathname === '/'
    return pathname.startsWith(path)
  }

  // Helper function to get nav link classes
  const getNavLinkClasses = (path: string) => {
    if (isActivePath(path)) {
      return "bg-blue-600 hover:bg-blue-700 text-white px-4 py-1 rounded-full font-medium text-base transition-colors"
    }
    return "text-gray-600 hover:text-gray-900 font-normal text-base"
  }

  // Helper function for mobile nav link classes
  const getMobileNavLinkClasses = (path: string) => {
    if (isActivePath(path)) {
      return "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-full font-medium text-base transition-colors inline-block text-center"
    }
    return "text-gray-600 hover:text-gray-900 font-normal text-base"
  }

  return (
    <nav className="relative z-50 bg-white/80 backdrop-blur-sm border-0 border-gray-200/50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Link href="/" className="flex items-center gap-2">
              <Image
                src="/logo.png"
                alt="breakdown.ad Logo"
                width={40}
                height={40}
                priority
                className="w-6 h-6 md:w-10 md:h-10"
              />
              <span className="font-bold text-xl text-gray-900 hidden md:inline">Breakdown.ad</span>
              {/* Show breakdown.ad text on mobile only for ad-library page */}
              {pathname === '/ad-library' && (
                <span className="font-bold text-lg text-gray-900 md:hidden">breakdown.ad</span>
              )}
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <Link href="/ad-library" className={getNavLinkClasses("/ad-library")}>
              Ad Library
            </Link>
            
            <Link href="/featured" className={getNavLinkClasses("/featured")}>
              Featured
            </Link>
            
            <Link href="/ad-agent" className={getNavLinkClasses("/ad-agent")}>
              Ad Agent
            </Link>

            {/* More Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900 font-normal text-base h-8 px-2">
                  More
                  <ChevronDown className="ml-1 h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem asChild>
                  <Link href="/pricing">Pricing</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/frameworks">Frameworks</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/about">About</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/faq">FAQ</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/contact">Contact</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/terms">Terms of Service</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/privacy">Privacy Policy</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Right side - Auth */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated ? (
              <div className="flex items-center space-x-4">
                <Link href="/studio">
                  <Button size="sm" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                    Studio
                  </Button>
                </Link>
                <UserButton />
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
                  <Link href="/sign-in">Sign In</Link>
                </Button>
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                  <Link href="/sign-up">Get Started</Link>
                </Button>
              </div>
            )}
          </div>

          {/* Mobile header - Ad Library button and menu */}
          <div className="md:hidden flex items-center space-x-0">
            {/* Hide Ad Library button when on /ad-library page */}
            {pathname !== '/ad-library' && (
              <Link href="/ad-library">
                <Button size="sm" className="bg-blue-500 text-xs hover:bg-purple-600 text-white">
                  Ad Library
                </Button>
              </Link>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <div className="flex flex-col space-y-3">
              <Link href="/ad-library" className={getMobileNavLinkClasses("/ad-library")}>
                Ad Library
              </Link>
              <Link href="/featured" className={getMobileNavLinkClasses("/featured")}>
                Featured
              </Link>
              <Link href="/ad-agent" className={getMobileNavLinkClasses("/ad-agent")}>
                Ad Agent
              </Link>
              <Link href="/pricing" className={getMobileNavLinkClasses("/pricing")}>
                Pricing
              </Link>
              <Link href="/frameworks" className={getMobileNavLinkClasses("/frameworks")}>
                Frameworks
              </Link>
              <Link href="/about" className={getMobileNavLinkClasses("/about")}>
                About
              </Link>
              <Link href="/faq" className={getMobileNavLinkClasses("/faq")}>
                FAQ
              </Link>
              <Link href="/contact" className={getMobileNavLinkClasses("/contact")}>
                Contact
              </Link>
              <Link href="/terms" className={getMobileNavLinkClasses("/terms")}>
                Terms
              </Link>
              <Link href="/privacy" className={getMobileNavLinkClasses("/privacy")}>
                Privacy
              </Link>
              
              {/* Mobile Auth */}
              <div className="pt-4 border-t border-gray-200">
                {isAuthenticated ? (
                  <div className="flex flex-col space-y-2">
                    <Link href="/studio">
                      <Button size="sm" className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                        Studio
                      </Button>
                    </Link>
                    <div className="flex justify-center">
                      <UserButton />
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col space-y-2">
                    <Link href="/sign-in">
                      <Button variant="outline" size="sm" className="w-full">
                        Sign In
                      </Button>
                    </Link>
                    <Link href="/sign-up">
                      <Button size="sm" className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                        Get Started
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}