'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { AnalysisMarkdown } from '@/components/ui/markdown-renderer'
import { cn } from '@/lib/utils'

interface SectionedMarkdownProps {
  content: string
  className?: string
  sectionClassName?: string
  showSectionCards?: boolean
  showTableOfContents?: boolean
}

interface Section {
  title: string
  content: string
  level: number
  id: string
}

function parseMarkdownSections(content: string): Section[] {
  if (!content || typeof content !== 'string') {
    return []
  }

  const lines = content.split('\n')
  const sections: Section[] = []
  let currentSection: Section | null = null
  let contentBuffer: string[] = []

  // Helper to create URL-friendly ID from title
  const createId = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .trim()
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Remove multiple consecutive hyphens
  }

  for (const line of lines) {
    // Check if this line is a heading (## or ###)
    const h2Match = line.match(/^## (.+)$/)
    const h3Match = line.match(/^### (.+)$/)
    
    if (h2Match || h3Match) {
      // Save previous section if it exists
      if (currentSection) {
        currentSection.content = contentBuffer.join('\n').trim()
        if (currentSection.content || currentSection.title) {
          sections.push(currentSection)
        }
      }
      
      // Start new section
      const title = h2Match ? h2Match[1] : h3Match![1]
      currentSection = {
        title,
        content: '',
        level: h2Match ? 2 : 3,
        id: createId(title)
      }
      contentBuffer = []
    } else {
      // Add line to current content buffer
      contentBuffer.push(line)
    }
  }
  
  // Don't forget the last section
  if (currentSection) {
    currentSection.content = contentBuffer.join('\n').trim()
    if (currentSection.content || currentSection.title) {
      sections.push(currentSection)
    }
  }
  
  // If no sections were found, treat entire content as one section without a title
  if (sections.length === 0 && content.trim()) {
    sections.push({
      title: '',
      content: content,
      level: 2,
      id: 'content'
    })
  }
  
  return sections
}

export default function SectionedMarkdown({ 
  content, 
  className, 
  sectionClassName,
  showSectionCards = true,
  showTableOfContents = false
}: SectionedMarkdownProps) {
  const sections = parseMarkdownSections(content)
  
  if (sections.length === 0) {
    return null
  }

  // Table of Contents Component
  const TableOfContents = () => {
    if (!showTableOfContents || sections.length <= 1) return null
    
    return (
      <Card className="mb-6 border-purple-200 bg-purple-50/30">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg text-purple-900 flex items-center gap-2">
            <div className="w-1 h-6 bg-purple-600 rounded-full"></div>
            Table of Contents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <nav className="space-y-2">
            {sections.map((section, index) => (
              <a
                key={index}
                href={`#${section.id}`}
                className={cn(
                  'block px-3 py-2 rounded-md text-sm transition-colors hover:bg-purple-100',
                  section.level === 2 
                    ? 'font-medium text-purple-900 border-l-2 border-purple-300' 
                    : 'text-purple-700 ml-4 border-l border-purple-200'
                )}
              >
                {section.title}
              </a>
            ))}
          </nav>
        </CardContent>
      </Card>
    )
  }

  if (!showSectionCards) {
    // Render without cards - just styled sections
    return (
      <div className={cn('space-y-8', className)}>
        <TableOfContents />
        {sections.map((section, index) => (
          <div 
            key={index}
            id={section.id}
            className={cn(
              'analysis-section scroll-mt-20',
              section.level === 2 ? 'border-l-4 border-purple-200 pl-6 bg-purple-50/30 py-4 rounded-r-lg' : 'ml-4',
              sectionClassName
            )}
          >
            {section.title && (
              section.level === 2 ? (
                <h2 className="text-xl font-bold text-purple-900 mb-4 pb-2 border-b border-purple-200">
                  {section.title}
                </h2>
              ) : (
                <h3 className="text-lg font-semibold text-purple-800 mb-3">
                  {section.title}
                </h3>
              )
            )}
            
            {section.content && (
              <AnalysisMarkdown 
                content={section.content}
                className="section-content"
              />
            )}
          </div>
        ))}
      </div>
    )
  }

  // Render with cards - simple light blue theme
  return (
    <div className={cn('space-y-4', className)}>
      {sections.map((section, index) => (
        <Card
          key={index}
          id={section.id}
          className={cn(
            'analysis-section-card scroll-mt-20 border-blue-100 shadow-sm hover:shadow-md transition-shadow duration-200',
            sectionClassName
          )}
        >
          {section.title && (
            <CardHeader className="pb-3 bg-blue-50/30">
              <CardTitle className="text-lg text-blue-900 font-semibold">
                {section.title}
              </CardTitle>
            </CardHeader>
          )}

          {section.content && (
            <CardContent className="pt-0">
              <AnalysisMarkdown
                content={section.content}
                className="section-content"
              />
            </CardContent>
          )}
        </Card>
      ))}
    </div>
  )
}

// Convenience component specifically for analysis results
export function AnalysisSections({
  content,
  className,
  showTableOfContents = false
}: {
  content: string;
  className?: string;
  showTableOfContents?: boolean;
}) {
  return (
    <SectionedMarkdown
      content={content}
      showSectionCards={true}
      showTableOfContents={showTableOfContents}
      className={className}
    />
  )
}

// Convenience component for inline sectioned content (no cards)
export function InlineSections({ 
  content, 
  className,
  showTableOfContents = false 
}: { 
  content: string; 
  className?: string;
  showTableOfContents?: boolean;
}) {
  return (
    <SectionedMarkdown 
      content={content}
      showSectionCards={false}
      showTableOfContents={showTableOfContents}
      className={className}
    />
  )
}