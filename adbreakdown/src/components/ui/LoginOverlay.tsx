'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Lock, Sparkles, BarChart3, MessageSquare, Star, ArrowRight, Eye } from 'lucide-react'
import Link from 'next/link'
import { useEffect } from 'react'

interface LoginOverlayProps {
  isVisible: boolean
  title?: string
  description?: string
  features?: string[]
}

export default function LoginOverlay({ 
  isVisible, 
  title = "Unlock Full Analysis",
  description = "Sign up to access the complete analysis with detailed insights, AI-powered recommendations, and advanced features.",
  features = [
    "Complete AI analysis breakdown",
    "Emotion timeline with audience insights",
    "Advanced targeting recommendations", 
    "Competitor comparison data",
    "Enhanced script analysis",
    "Export and sharing capabilities"
  ]
}: LoginOverlayProps) {
  // Prevent body scroll when overlay is visible
  useEffect(() => {
    if (isVisible) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
    
    // Cleanup function to restore scroll
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isVisible])


  if (!isVisible) return null

  return (
    <div className="fixed inset-0 z-50">
      {/* Full page backdrop */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
      
      {/* Main overlay content */}
      <div className="absolute inset-0 flex items-center justify-center p-4">
        <Card className="w-full max-w-lg shadow-2xl border-0 bg-white/95 backdrop-blur-sm relative">
          
          <CardHeader className="text-center pb-4">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full">
                <Lock className="h-6 w-6 text-white" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
              {title}
            </CardTitle>
            <CardDescription className="text-gray-600 text-base leading-relaxed">
              {description}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Preview badge */}
            <div className="flex items-center justify-center">
              <Badge variant="secondary" className="px-3 py-1 bg-blue-50 text-blue-700 border border-blue-200">
                <Eye className="h-4 w-4 mr-1" />
                You&apos;re viewing a preview
              </Badge>
            </div>

            {/* Features list */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 text-center mb-4">
                Get access to:
              </h4>
              <div className="grid gap-2">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3 p-2 rounded-lg bg-gray-50">
                    <div className="flex-shrink-0">
                      {index === 0 && <BarChart3 className="h-5 w-5 text-blue-600" />}
                      {index === 1 && <Sparkles className="h-5 w-5 text-purple-600" />}
                      {index === 2 && <MessageSquare className="h-5 w-5 text-green-600" />}
                      {index === 3 && <Star className="h-5 w-5 text-yellow-600" />}
                      {index >= 4 && <ArrowRight className="h-5 w-5 text-gray-600" />}
                    </div>
                    <span className="text-sm text-gray-700 font-medium">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Action buttons */}
            <div className="space-y-3 pt-4">
              <Link href="/sign-up" className="block">
                <Button 
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 shadow-lg transition-all duration-200 transform hover:scale-[1.02]"
                  size="lg"
                >
                  <Sparkles className="h-5 w-5 mr-2" />
                  Get Full Access - Free Trial
                </Button>
              </Link>
              
              <Link href="/sign-in" className="block">
                <Button 
                  variant="outline" 
                  className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 py-3"
                  size="lg"
                >
                  Already have an account? Sign in
                </Button>
              </Link>
            </div>

            {/* Trust signals */}
            <div className="text-center pt-4 border-t border-gray-100">
              <div className="flex items-center justify-center gap-4 text-xs text-gray-500 mb-2">
                <span className="flex items-center gap-1">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  Free 7-day trial
                </span>
                <span className="flex items-center gap-1">
                  <Lock className="h-3 w-3" />
                  No credit card required
                </span>
              </div>
              <p className="text-xs text-gray-400">
                Join thousands of marketers using breakdown.ad
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}