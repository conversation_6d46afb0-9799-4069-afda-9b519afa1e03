'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON><PERSON>, <PERSON>ertTriangle, Crown } from 'lucide-react'
import Link from 'next/link'
import CreditExhaustionModal from '@/components/ui/CreditExhaustionModal'

interface AnalysisInputCardProps {
  onAnalyze: (url: string) => Promise<void>
  loading: boolean
  creditsRemaining?: number
}

export default function AnalysisInputCard({ onAnalyze, loading, creditsRemaining = 0 }: AnalysisInputCardProps) {
  const [youtubeUrl, setYoutubeUrl] = useState('')
  const [error, setError] = useState('')
  const [showCreditModal, setShowCreditModal] = useState(false)
  const [creditError, setCreditError] = useState<{ required: number; remaining: number } | null>(null)

  const handleSubmit = async () => {
    if (!youtubeUrl.trim()) {
      setError('Please enter a YouTube URL')
      return
    }

    // Check credits before proceeding
    if (creditsRemaining <= 0) {
      setCreditError({ required: 1, remaining: creditsRemaining })
      setShowCreditModal(true)
      return
    }

    // Validate YouTube URL format
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)([\w-]+)/
    if (!youtubeRegex.test(youtubeUrl)) {
      setError('Please enter a valid YouTube URL')
      return
    }

    setError('')
    try {
      await onAnalyze(youtubeUrl)
      setYoutubeUrl('')
    } catch (err: any) {
      // Handle credit exhaustion specifically
      if (err.message && err.message.includes('Insufficient credits')) {
        setCreditError({ required: 1, remaining: creditsRemaining })
        setShowCreditModal(true)
      } else {
        setError(err.message || 'Failed to analyze video')
      }
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !loading) {
      handleSubmit()
    }
  }

  return (
    <Card className="border-2 border-dashed border-blue-200 hover:border-blue-300 transition-colors">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-blue-600" />
          Analyze New Ad
        </CardTitle>
        <CardDescription>
          Paste a YouTube video URL to start AI-powered analysis
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          
          
          <div className="flex gap-4">
            <Input 
              placeholder="https://youtube.com/watch?v=..." 
              className="flex-1"
              value={youtubeUrl}
              onChange={(e) => setYoutubeUrl(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={loading}
            />
            <Button 
              onClick={handleSubmit}
              disabled={loading || !youtubeUrl.trim()}
              className="bg-blue-600 hover:bg-blue-700 min-w-[120px]"
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  Redirecting...
                </div>
              ) : (
                'Analyze Ad'
              )}
            </Button>
          </div>
          {error && (
            <div className="text-red-600 text-sm p-3 bg-red-50 rounded-lg border border-red-200">
              {error}
            </div>
          )}
          
        </div>
      </CardContent>
      
      {/* Credit Exhaustion Modal */}
      {creditError && (
        <CreditExhaustionModal 
          isOpen={showCreditModal}
          onClose={() => {
            setShowCreditModal(false)
            setCreditError(null)
          }}
          creditsRemaining={creditError.remaining}
          creditsRequired={creditError.required}
        />
      )}
    </Card>
  )
}
