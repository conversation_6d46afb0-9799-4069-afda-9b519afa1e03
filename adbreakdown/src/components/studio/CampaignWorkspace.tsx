'use client';

import React, { useState, useCallback } from 'react';
import { MessageSquare, FileText, GripVertical } from 'lucide-react';
import CampaignChat from './CampaignChat';
import SourcesPanel from './SourcesPanel';
import { CampaignCanvas } from './CampaignCanvas';

interface CampaignWorkspaceProps {
  className?: string;
}

export default function CampaignWorkspace({ className = '' }: CampaignWorkspaceProps) {
  const [leftPanelWidth, setLeftPanelWidth] = useState(33); // 33% of total width
  const [activeTab, setActiveTab] = useState<'chat' | 'sources'>('chat');
  const [isDragging, setIsDragging] = useState(false);
  const [campaignData, setCampaignData] = useState({
    brief: '',
    script: '',
    targetAudience: '',
    objectives: '',
  });

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);

    const startX = e.clientX;
    const startWidth = leftPanelWidth;
    const containerWidth = (e.currentTarget.parentElement as HTMLElement)?.offsetWidth || 1200;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const deltaPercent = (deltaX / containerWidth) * 100;
      const newWidth = Math.max(20, Math.min(60, startWidth + deltaPercent)); // Min 20%, Max 60%
      setLeftPanelWidth(newWidth);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [leftPanelWidth]);

  const handleCampaignUpdate = useCallback((updates: any) => {
    setCampaignData(prev => ({ ...prev, ...updates }));
  }, []);

  return (
    <div className={`flex h-full bg-background ${isDragging ? 'cursor-col-resize' : ''} ${className}`}>
      {/* Left Panel */}
      <div 
        className="flex flex-col border-r border-border overflow-hidden"
        style={{ width: `${leftPanelWidth}%` }}
      >
        {/* Tab Header */}
        <div className="flex border-b border-border bg-background">
          <button
            onClick={() => setActiveTab('chat')}
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'chat'
                ? 'bg-primary/5 text-primary border-b-2 border-primary'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
          >
            <MessageSquare className="w-4 h-4" />
            Chat
          </button>
          <button
            onClick={() => setActiveTab('sources')}
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'sources'
                ? 'bg-primary/5 text-primary border-b-2 border-primary'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
          >
            <FileText className="w-4 h-4" />
            Sources
          </button>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-hidden">
          {activeTab === 'chat' && (
            <CampaignChat 
              onCampaignUpdate={handleCampaignUpdate}
              campaignData={campaignData}
            />
          )}
          {activeTab === 'sources' && (
            <SourcesPanel />
          )}
        </div>
      </div>

      {/* Draggable Divider */}
      <div
        className="w-1 bg-border hover:bg-primary/20 cursor-col-resize flex items-center justify-center group transition-colors"
        onMouseDown={handleMouseDown}
      >
        <GripVertical className="w-3 h-6 text-muted-foreground group-hover:text-primary transition-colors" />
      </div>

      {/* Right Panel - Canvas */}
      <div className="flex-1 overflow-hidden">
        <CampaignCanvas 
          campaignData={campaignData}
          onCampaignUpdate={handleCampaignUpdate}
        />
      </div>
    </div>
  );
}