'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, FileText, Link, Upload, X, ExternalLink } from 'lucide-react';

interface Source {
  id: string;
  name: string;
  type: 'document' | 'url' | 'text';
  content?: string;
  url?: string;
  size?: string;
  addedAt: Date;
}

export default function SourcesPanel() {
  const [sources, setSources] = useState<Source[]>([
    {
      id: '1',
      name: 'Brand Guidelines.pdf',
      type: 'document',
      size: '2.4 MB',
      addedAt: new Date('2024-01-15'),
    },
    {
      id: '2',
      name: 'Competitor Analysis',
      type: 'url',
      url: 'https://example.com/analysis',
      addedAt: new Date('2024-01-14'),
    }
  ]);

  const [isUploading, setIsUploading] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);

    try {
      for (const file of files) {
        const newSource: Source = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          name: file.name,
          type: 'document',
          size: `${(file.size / 1024 / 1024).toFixed(1)} MB`,
          addedAt: new Date(),
        };

        setSources(prev => [...prev, newSource]);
      }
    } catch (error) {
      console.error('Error uploading files:', error);
    } finally {
      setIsUploading(false);
      // Reset input
      event.target.value = '';
    }
  };

  const removeSource = (id: string) => {
    setSources(prev => prev.filter(source => source.id !== id));
  };

  const getSourceIcon = (type: Source['type']) => {
    switch (type) {
      case 'document':
        return <FileText className="w-4 h-4" />;
      case 'url':
        return <Link className="w-4 h-4" />;
      case 'text':
        return <FileText className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getSourceTypeColor = (type: Source['type']) => {
    switch (type) {
      case 'document':
        return 'bg-blue-100 text-blue-800';
      case 'url':
        return 'bg-green-100 text-green-800';
      case 'text':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold">Campaign Sources</h3>
          <Badge variant="secondary">{sources.length}</Badge>
        </div>
        
        {/* Upload Button */}
        <div className="relative">
          <input
            type="file"
            multiple
            accept=".pdf,.doc,.docx,.txt,.md"
            onChange={handleFileUpload}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            disabled={isUploading}
          />
          <Button 
            className="w-full" 
            variant="outline" 
            disabled={isUploading}
          >
            <Upload className="w-4 h-4 mr-2" />
            {isUploading ? 'Uploading...' : 'Add Source'}
          </Button>
        </div>
      </div>

      {/* Sources List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {sources.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">No sources added yet</p>
            <p className="text-xs">Upload documents or add URLs to provide context for your campaign</p>
          </div>
        ) : (
          sources.map((source) => (
            <Card key={source.id} className="group hover:shadow-sm transition-shadow">
              <CardContent className="p-3">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex items-start gap-2 flex-1">
                    <div className="flex-shrink-0 mt-0.5">
                      {getSourceIcon(source.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-sm font-medium truncate">{source.name}</h4>
                        <Badge 
                          variant="secondary" 
                          className={`text-xs ${getSourceTypeColor(source.type)}`}
                        >
                          {source.type}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        {source.size && <span>{source.size}</span>}
                        <span>{source.addedAt.toLocaleDateString()}</span>
                      </div>
                      
                      {source.url && (
                        <div className="flex items-center gap-1 mt-1">
                          <ExternalLink className="w-3 h-3" />
                          <span className="text-xs text-muted-foreground truncate">
                            {source.url}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeSource(source.id)}
                    className="opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0 h-6 w-6 p-0"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <div className="text-xs text-muted-foreground text-center">
          Supported formats: PDF, DOC, DOCX, TXT, MD
        </div>
      </div>
    </div>
  );
}