'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Calendar, Clock, ArrowRight, CalendarDays, Target, BarChart3 } from 'lucide-react'
import Link from 'next/link'

export default function MarketingCalendarCard() {
  return (
    <Card className="flex flex-col bg-gradient-to-br from-violet-50/40 via-white to-purple-50/20 border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader className="flex-none border-b border-gray-200 bg-white rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-gray-100 rounded-lg">
              <Calendar className="h-5 w-5 text-gray-600" />
            </div>
            <div>
              <CardTitle className="text-lg text-gray-900">Campaign Calendar</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Schedule and plan your ad campaigns
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium">
              COMING SOON
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col space-y-4 p-6">
        {/* Feature Preview Cards */}
        <div className="grid grid-cols-3 gap-2 flex-none">
          <div className="group bg-white/60 border border-violet-200 rounded-lg p-2 text-center hover:bg-white/80 hover:border-violet-300 transition-all duration-200 cursor-default">
            <div className="p-1 bg-violet-100 rounded-md inline-block mb-1">
              <CalendarDays className="h-3 w-3 text-violet-600" />
            </div>
            <p className="text-xs font-medium text-gray-900">Schedule</p>
          </div>
          <div className="group bg-white/60 border border-violet-200 rounded-lg p-2 text-center hover:bg-white/80 hover:border-violet-300 transition-all duration-200 cursor-default">
            <div className="p-1 bg-green-100 rounded-md inline-block mb-1">
              <Target className="h-3 w-3 text-green-600" />
            </div>
            <p className="text-xs font-medium text-gray-900">Plan</p>
          </div>
          <div className="group bg-white/60 border border-violet-200 rounded-lg p-2 text-center hover:bg-white/80 hover:border-violet-300 transition-all duration-200 cursor-default">
            <div className="p-1 bg-blue-100 rounded-md inline-block mb-1">
              <BarChart3 className="h-3 w-3 text-blue-600" />
            </div>
            <p className="text-xs font-medium text-gray-900">Track</p>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col justify-center">
          <div className="bg-white/60 border border-violet-200 rounded-xl p-6 text-center hover:bg-white/70 transition-colors">
            <div className="p-3 bg-violet-100 rounded-xl inline-block mb-4">
              <Calendar className="h-10 w-10 text-violet-600" />
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Campaign Planning Made Easy</h4>
            <p className="text-sm text-gray-600 mb-6 leading-relaxed">
              Plan your ad campaigns, schedule analyses, and track performance over time. 
              Set reminders for campaign launches and optimization reviews.
            </p>
            
            {/* Mock Calendar Preview */}
            <div className="bg-white border border-violet-200 rounded-lg p-3 mb-6">
              <div className="grid grid-cols-7 gap-1 text-xs">
                {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, i) => (
                  <div key={i} className="text-center font-medium text-violet-700 py-1">
                    {day}
                  </div>
                ))}
                {Array.from({ length: 21 }, (_, i) => (
                  <div key={i} className={`text-center py-1 text-xs ${
                    i === 14 ? 'bg-violet-100 text-violet-700 rounded-md font-medium' :
                    i === 7 ? 'bg-green-100 text-green-700 rounded-md font-medium' :
                    'text-gray-400'
                  }`}>
                    {i + 1}
                  </div>
                ))}
              </div>
              <div className="flex justify-center gap-4 mt-3 text-xs">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-violet-300 rounded-full"></div>
                  <span className="text-violet-600">Launch</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                  <span className="text-green-600">Review</span>
                </div>
              </div>
            </div>
            
            {/* Feature highlights */}
            <div className="space-y-2 mb-6">
              <div className="flex items-center justify-center gap-2 text-xs text-gray-600">
                <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                <span>Schedule campaign launches</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-xs text-gray-600">
                <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                <span>Track performance over time</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-xs text-gray-600">
                <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                <span>Set optimization reminders</span>
              </div>
            </div>
            
            <Link href="/calendar">
              <Button 
                variant="outline" 
                className="border-violet-300 text-violet-700 hover:bg-violet-100 hover:border-violet-400 w-full group" 
                disabled
              >
                <Clock className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                Open Calendar
                <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-0.5 transition-transform" />
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}