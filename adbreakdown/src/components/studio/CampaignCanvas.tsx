'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { MarkdownRenderer } from './MarkdownRenderer';
import { 
  FileText, 
  Video, 
  Target, 
  TrendingUp, 
  Save, 
  Download,
  Edit3,
  Clock,
  Users,
  DollarSign
} from 'lucide-react';

interface CampaignCanvasProps {
  campaignData: {
    brief: string;
    script: string;
    targetAudience: string;
    objectives: string;
    // Agent updates
    ideaUpdate?: Partial<CampaignIdea>;
    scriptUpdate?: VideoScript;
  };
  onCampaignUpdate: (updates: any) => void;
}

interface CampaignIdea {
  title: string;
  summary: string;
  hook: string;
  problem: string;
  solution: string;
  keyBenefits: string;
  cta: string;
  visualCues: string;
  audioCues: string;
  brandProduct: string;
  targetAudience: string;
  usp: string;
}

interface VideoScript {
  title: string;
  content: string;
}

export function CampaignCanvas({ campaignData, onCampaignUpdate }: CampaignCanvasProps) {
  const [activeTab, setActiveTab] = useState('idea');
  const [isEditing, setIsEditing] = useState(false);
  
  const [idea, setIdea] = useState<CampaignIdea>({
    title: 'Untitled Campaign',
    summary: '',
    hook: '',
    problem: '',
    solution: '',
    keyBenefits: '',
    cta: '',
    visualCues: '',
    audioCues: '',
    brandProduct: '',
    targetAudience: '',
    usp: '',
  });

  const [script, setScript] = useState<VideoScript>({
    title: 'Untitled Script',
    content: '',
  });

  // Update local state when campaignData changes
  useEffect(() => {
    // Handle agent updates
    if (campaignData.ideaUpdate) {
      setIdea(prev => ({
        ...prev,
        ...campaignData.ideaUpdate
      }));
    }

    if (campaignData.scriptUpdate) {
      setScript(prev => ({
        ...prev,
        ...campaignData.scriptUpdate
      }));
    }

    // Legacy support for old format
    if (campaignData.brief) {
      setIdea(prev => ({
        ...prev,
        targetAudience: campaignData.targetAudience || prev.targetAudience,
        summary: campaignData.objectives || prev.summary,
      }));
    }
  }, [campaignData]);

  const handleSave = () => {
    onCampaignUpdate({
      brief: JSON.stringify(idea),
      script: JSON.stringify(script),
    });
    setIsEditing(false);
  };

  const handleExport = () => {
    const exportData = {
      idea,
      script,
      exportedAt: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `campaign-${idea.title.toLowerCase().replace(/\s+/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border bg-background">
        <div className="flex items-center gap-3">
          <h2 className="text-xl font-semibold">Campaign Canvas</h2>
          <Badge variant="secondary">Draft</Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(!isEditing)}
          >
            <Edit3 className="w-4 h-4 mr-2" />
            {isEditing ? 'View' : 'Edit'}
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={handleSave}>
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-2 mx-4 mt-4">
            <TabsTrigger value="idea" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Idea
            </TabsTrigger>
            <TabsTrigger value="script" className="flex items-center gap-2">
              <Video className="w-4 h-4" />
              Script
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-y-auto p-4">
            <TabsContent value="idea" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Campaign Idea
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Campaign Title */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Campaign Title</label>
                    {isEditing ? (
                      <input
                        className="w-full p-2 border rounded-md"
                        value={idea.title}
                        onChange={(e) => setIdea(prev => ({ ...prev, title: e.target.value }))}
                      />
                    ) : (
                      <h3 className="text-lg font-semibold">{idea.title}</h3>
                    )}
                  </div>

                  {/* Summary */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Summary</label>
                    {isEditing ? (
                      <Textarea
                        value={idea.summary}
                        onChange={(e) => setIdea(prev => ({ ...prev, summary: e.target.value }))}
                        placeholder="Brief overview of the campaign concept"
                        className="min-h-[80px]"
                      />
                    ) : (
                      <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                        {idea.summary ? (
                          <MarkdownRenderer content={idea.summary} className="text-sm" />
                        ) : (
                          'No summary defined yet'
                        )}
                      </div>
                    )}
                  </div>

                  {/* Hook */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Hook</label>
                    {isEditing ? (
                      <Textarea
                        value={idea.hook}
                        onChange={(e) => setIdea(prev => ({ ...prev, hook: e.target.value }))}
                        placeholder="The attention-grabbing opening"
                        className="min-h-[60px]"
                      />
                    ) : (
                      <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                        {idea.hook ? (
                          <MarkdownRenderer content={idea.hook} className="text-sm" />
                        ) : (
                          'No hook defined yet'
                        )}
                      </div>
                    )}
                  </div>

                  {/* Problem/Need */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Problem/Need</label>
                    {isEditing ? (
                      <Textarea
                        value={idea.problem}
                        onChange={(e) => setIdea(prev => ({ ...prev, problem: e.target.value }))}
                        placeholder="What problem does this address?"
                        className="min-h-[60px]"
                      />
                    ) : (
                      <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                        {idea.problem ? (
                          <MarkdownRenderer content={idea.problem} className="text-sm" />
                        ) : (
                          'No problem defined yet'
                        )}
                      </div>
                    )}
                  </div>

                  {/* Solution */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Solution</label>
                    {isEditing ? (
                      <Textarea
                        value={idea.solution}
                        onChange={(e) => setIdea(prev => ({ ...prev, solution: e.target.value }))}
                        placeholder="How does your product/service solve the problem?"
                        className="min-h-[60px]"
                      />
                    ) : (
                      <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                        {idea.solution ? (
                          <MarkdownRenderer content={idea.solution} className="text-sm" />
                        ) : (
                          'No solution defined yet'
                        )}
                      </div>
                    )}
                  </div>

                  {/* Key Benefits */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Key Benefits</label>
                    {isEditing ? (
                      <Textarea
                        value={idea.keyBenefits}
                        onChange={(e) => setIdea(prev => ({ ...prev, keyBenefits: e.target.value }))}
                        placeholder="Main benefits and value propositions"
                        className="min-h-[60px]"
                      />
                    ) : (
                      <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                        {idea.keyBenefits ? (
                          <MarkdownRenderer content={idea.keyBenefits} className="text-sm" />
                        ) : (
                          'No key benefits defined yet'
                        )}
                      </div>
                    )}
                  </div>

                  {/* Call to Action */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Call to Action</label>
                    {isEditing ? (
                      <Textarea
                        value={idea.cta}
                        onChange={(e) => setIdea(prev => ({ ...prev, cta: e.target.value }))}
                        placeholder="What action should viewers take?"
                        className="min-h-[40px]"
                      />
                    ) : (
                      <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                        {idea.cta ? (
                          <MarkdownRenderer content={idea.cta} className="text-sm" />
                        ) : (
                          'No call to action defined yet'
                        )}
                      </div>
                    )}
                  </div>

                  {/* Visual and Audio Cues */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Visual Cues</label>
                      {isEditing ? (
                        <Textarea
                          value={idea.visualCues}
                          onChange={(e) => setIdea(prev => ({ ...prev, visualCues: e.target.value }))}
                          placeholder="Visual elements, scenes, transitions"
                          className="min-h-[60px]"
                        />
                      ) : (
                        <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                          {idea.visualCues ? (
                            <MarkdownRenderer content={idea.visualCues} className="text-sm" />
                          ) : (
                            'No visual cues defined yet'
                          )}
                        </div>
                      )}
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium mb-2 block">Audio Cues</label>
                      {isEditing ? (
                        <Textarea
                          value={idea.audioCues}
                          onChange={(e) => setIdea(prev => ({ ...prev, audioCues: e.target.value }))}
                          placeholder="Music, sound effects, voice-over style"
                          className="min-h-[60px]"
                        />
                      ) : (
                        <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                          {idea.audioCues ? (
                            <MarkdownRenderer content={idea.audioCues} className="text-sm" />
                          ) : (
                            'No audio cues defined yet'
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Brand/Product */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Brand/Product</label>
                    {isEditing ? (
                      <Textarea
                        value={idea.brandProduct}
                        onChange={(e) => setIdea(prev => ({ ...prev, brandProduct: e.target.value }))}
                        placeholder="Brand positioning and product details"
                        className="min-h-[60px]"
                      />
                    ) : (
                      <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                        {idea.brandProduct ? (
                          <MarkdownRenderer content={idea.brandProduct} className="text-sm" />
                        ) : (
                          'No brand/product info defined yet'
                        )}
                      </div>
                    )}
                  </div>

                  {/* Target Audience and USP */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block flex items-center gap-2">
                        <Users className="w-4 h-4" />
                        Target Audience
                      </label>
                      {isEditing ? (
                        <Textarea
                          value={idea.targetAudience}
                          onChange={(e) => setIdea(prev => ({ ...prev, targetAudience: e.target.value }))}
                          placeholder="Demographics, psychographics, behaviors"
                          className="min-h-[60px]"
                        />
                      ) : (
                        <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                          {idea.targetAudience ? (
                            <MarkdownRenderer content={idea.targetAudience} className="text-sm" />
                          ) : (
                            'No target audience defined yet'
                          )}
                        </div>
                      )}
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium mb-2 block">USP (Unique Selling Proposition)</label>
                      {isEditing ? (
                        <Textarea
                          value={idea.usp}
                          onChange={(e) => setIdea(prev => ({ ...prev, usp: e.target.value }))}
                          placeholder="What makes you different?"
                          className="min-h-[60px]"
                        />
                      ) : (
                        <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                          {idea.usp ? (
                            <MarkdownRenderer content={idea.usp} className="text-sm" />
                          ) : (
                            'No USP defined yet'
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="script" className="mt-0 h-full">
              <Card className="h-full">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Video className="w-5 h-5" />
                      Video Script
                    </CardTitle>
                    {isEditing && (
                      <input
                        className="max-w-xs p-2 border rounded-md text-sm"
                        value={script.title}
                        onChange={(e) => setScript(prev => ({ ...prev, title: e.target.value }))}
                        placeholder="Script title..."
                      />
                    )}
                    {!isEditing && (
                      <h3 className="text-lg font-semibold">{script.title}</h3>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="flex-1 p-4">
                  {isEditing ? (
                    <Textarea
                      value={script.content}
                      onChange={(e) => setScript(prev => ({ ...prev, content: e.target.value }))}
                      placeholder="Write your video script here...

Example structure:
[HOOK - 0-5 seconds]
Attention-grabbing opening

[PROBLEM - 5-10 seconds]
Identify the pain point

[SOLUTION - 10-20 seconds]
Present your product/service

[BENEFITS - 20-25 seconds]
Key value propositions

[CALL TO ACTION - 25-30 seconds]
What should viewers do next?

[VISUAL NOTES]
• Scene descriptions
• Camera angles
• Graphics/text overlays

[AUDIO NOTES]
• Music style
• Voice-over tone
• Sound effects"
                      className="w-full h-full min-h-[400px] resize-none font-mono text-sm leading-relaxed"
                    />
                  ) : (
                    <div className="w-full h-full min-h-[400px] p-4 bg-muted rounded-md overflow-y-auto">
                      {script.content ? (
                        <div className="prose prose-sm max-w-none">
                          <MarkdownRenderer 
                            content={script.content} 
                            className="text-sm leading-relaxed [&>h1]:text-lg [&>h1]:font-bold [&>h1]:mb-4 [&>h2]:text-base [&>h2]:font-semibold [&>h2]:mb-3 [&>h3]:text-sm [&>h3]:font-medium [&>h3]:mb-2 [&>p]:mb-3 [&>ul]:mb-3 [&>ol]:mb-3 [&>li]:mb-1 [&>blockquote]:border-l-4 [&>blockquote]:border-primary [&>blockquote]:pl-4 [&>blockquote]:italic [&>code]:bg-background [&>code]:px-1 [&>code]:py-0.5 [&>code]:rounded [&>pre]:bg-background [&>pre]:p-3 [&>pre]:rounded [&>pre]:overflow-x-auto"
                          />
                        </div>
                      ) : (
                        <div className="text-muted-foreground text-center py-8">
                          <Video className="w-12 h-12 mx-auto mb-3 opacity-50" />
                          <p className="text-sm">No script content yet.</p>
                          <p className="text-xs">Click Edit to start writing your video script, or chat with the AI to generate one automatically.</p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}