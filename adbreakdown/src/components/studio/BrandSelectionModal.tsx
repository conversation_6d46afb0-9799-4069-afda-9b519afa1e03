'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  Plus, 
  ExternalLink, 
  Loader2,
  Check
} from 'lucide-react';

interface Brand {
  id: string;
  brand_name: string;
  website_url?: string;
  description?: string;
  industry?: string;
  created_at: string;
}

interface BrandSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBrandSelect: (brand: Brand | null) => void;
  selectedBrand?: Brand | null;
}

export default function BrandSelectionModal({ 
  isOpen, 
  onClose, 
  onBrandSelect, 
  selectedBrand 
}: BrandSelectionModalProps) {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [creating, setCreating] = useState(false);
  const [newBrand, setNewBrand] = useState({
    name: '',
    website: '',
    description: '',
  });

  // Fetch user's brands
  useEffect(() => {
    if (isOpen) {
      fetchBrands();
    }
  }, [isOpen]);

  const fetchBrands = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/brands');
      if (response.ok) {
        const data = await response.json();
        setBrands(data.brands || []);
      }
    } catch (error) {
      console.error('Error fetching brands:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBrand = async () => {
    if (!newBrand.name.trim()) return;

    setCreating(true);
    try {
      const response = await fetch('/api/brands', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brand_name: newBrand.name,
          website_url: newBrand.website || null,
          description: newBrand.description || null,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setBrands(prev => [...prev, data.brand]);
        setNewBrand({ name: '', website: '', description: '' });
        setShowCreateForm(false);
      }
    } catch (error) {
      console.error('Error creating brand:', error);
    } finally {
      setCreating(false);
    }
  };

  const handleBrandSelect = async (brand: Brand) => {
    // Fetch full brand profile data when selected
    try {
      const response = await fetch(`/api/brands/${brand.id}`);
      if (response.ok) {
        const data = await response.json();
        onBrandSelect(data.brand); // Pass the full brand profile
      } else {
        onBrandSelect(brand); // Fallback to basic brand data
      }
    } catch (error) {
      console.error('Error fetching full brand profile:', error);
      onBrandSelect(brand); // Fallback to basic brand data
    }
    onClose();
  };

  const handleClose = () => {
    setShowCreateForm(false);
    setNewBrand({ name: '', website: '', description: '' });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5" />
            Select Brand Context
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col h-full">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span className="ml-2">Loading brands...</span>
            </div>
          ) : (
            <>
              {/* No Brand Option */}
              <div className="mb-4">
                <Card 
                  className={`cursor-pointer transition-colors ${
                    selectedBrand === null ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
                  }`}
                  onClick={() => handleBrandSelect(null as any)}
                >
                  <CardContent className="flex items-center justify-between p-3">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                        <Building2 className="w-5 h-5 text-muted-foreground" />
                      </div>
                      <div>
                        <h3 className="font-medium">No specific brand</h3>
                        <p className="text-sm text-muted-foreground">Create campaigns without brand context</p>
                      </div>
                    </div>
                    {selectedBrand === null && (
                      <Check className="w-5 h-5 text-primary" />
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Existing Brands */}
              <div className="flex-1 overflow-y-auto space-y-3 mb-4">
                {brands.map((brand) => (
                  <Card 
                    key={brand.id}
                    className={`cursor-pointer transition-colors ${
                      selectedBrand?.id === brand.id ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleBrandSelect(brand)}
                  >
                    <CardContent className="flex items-center justify-between p-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                          <Building2 className="w-5 h-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{brand.brand_name}</h3>
                          {brand.description && (
                            <p className="text-sm text-muted-foreground line-clamp-1">
                              {brand.description}
                            </p>
                          )}
                          {brand.website_url && (
                            <div className="flex items-center gap-1 mt-1">
                              <ExternalLink className="w-3 h-3 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">
                                {brand.website_url}
                              </span>
                            </div>
                          )}
                          {brand.industry && (
                            <Badge variant="secondary" className="mt-1 text-xs">
                              {brand.industry}
                            </Badge>
                          )}
                        </div>
                      </div>
                      {selectedBrand?.id === brand.id && (
                        <Check className="w-5 h-5 text-primary" />
                      )}
                    </CardContent>
                  </Card>
                ))}

                {brands.length === 0 && !showCreateForm && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Building2 className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p className="text-sm">No brands found</p>
                    <p className="text-xs">Create your first brand profile to get started</p>
                  </div>
                )}
              </div>

              {/* Create Brand Form */}
              {showCreateForm ? (
                <div className="border-t pt-4 space-y-4">
                  <h3 className="font-medium">Create New Brand</h3>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="brand-name">Brand Name *</Label>
                      <Input
                        id="brand-name"
                        value={newBrand.name}
                        onChange={(e) => setNewBrand(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter brand name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="brand-website">Website URL</Label>
                      <Input
                        id="brand-website"
                        value={newBrand.website}
                        onChange={(e) => setNewBrand(prev => ({ ...prev, website: e.target.value }))}
                        placeholder="https://example.com"
                      />
                    </div>
                    <div>
                      <Label htmlFor="brand-description">Description</Label>
                      <Textarea
                        id="brand-description"
                        value={newBrand.description}
                        onChange={(e) => setNewBrand(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Brief description of the brand"
                        rows={2}
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      onClick={handleCreateBrand} 
                      disabled={!newBrand.name.trim() || creating}
                      className="flex-1"
                    >
                      {creating ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4 mr-2" />
                          Create Brand
                        </>
                      )}
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setShowCreateForm(false)}
                      disabled={creating}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="border-t pt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowCreateForm(true)}
                    className="w-full"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create New Brand
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}