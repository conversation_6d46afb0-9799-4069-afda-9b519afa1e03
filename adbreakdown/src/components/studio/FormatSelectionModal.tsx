'use client';

import React, { useState } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Video, 
  Image, 
  FileText, 
  Radio, 
  Tv, 
  Smartphone,
  Monitor,
  Headphones,
  Instagram,
  Check
} from 'lucide-react';

interface AssetFormat {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  category: 'video' | 'image' | 'audio' | 'text';
  dimensions?: string;
  duration?: string;
  platform?: string;
}

interface FormatSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onFormatSelect: (format: AssetFormat | null) => void;
  selectedFormat?: AssetFormat | null;
}

const assetFormats: AssetFormat[] = [
  // Video Formats
  {
    id: 'video-short',
    name: 'Short-form Video',
    description: 'Instagram Reels, TikTok, YouTube Shorts',
    icon: Smartphone,
    category: 'video',
    dimensions: '9:16 (1080x1920)',
    duration: '15-60 seconds',
    platform: 'Social Media'
  },
  {
    id: 'video-square',
    name: 'Square Video',
    description: 'Instagram feed, Facebook posts',
    icon: Instagram,
    category: 'video',
    dimensions: '1:1 (1080x1080)',
    duration: '15-60 seconds',
    platform: 'Social Media'
  },
  {
    id: 'video-landscape',
    name: 'Landscape Video',
    description: 'YouTube, Facebook, TV ads',
    icon: Monitor,
    category: 'video',
    dimensions: '16:9 (1920x1080)',
    duration: '30-120 seconds',
    platform: 'YouTube, TV'
  },
  {
    id: 'video-tv',
    name: 'TV Commercial',
    description: 'Traditional television advertising',
    icon: Tv,
    category: 'video',
    dimensions: '16:9 (1920x1080)',
    duration: '15-60 seconds',
    platform: 'Television'
  },
  
  // Image Formats
  {
    id: 'image-social',
    name: 'Social Media Image',
    description: 'Instagram, Facebook, Twitter posts',
    icon: Image,
    category: 'image',
    dimensions: '1080x1080 or 1200x630',
    platform: 'Social Media'
  },
  {
    id: 'image-story',
    name: 'Story Image',
    description: 'Instagram Stories, Facebook Stories',
    icon: Smartphone,
    category: 'image',
    dimensions: '9:16 (1080x1920)',
    platform: 'Stories'
  },
  {
    id: 'image-banner',
    name: 'Display Banner',
    description: 'Website banners, Google Ads',
    icon: Monitor,
    category: 'image',
    dimensions: 'Various (728x90, 300x250)',
    platform: 'Web, Display'
  },
  
  // Audio Formats
  {
    id: 'audio-radio',
    name: 'Radio Ad',
    description: 'Traditional radio advertising',
    icon: Radio,
    category: 'audio',
    duration: '15-60 seconds',
    platform: 'Radio'
  },
  {
    id: 'audio-podcast',
    name: 'Podcast Ad',
    description: 'Podcast sponsorships and ads',
    icon: Headphones,
    category: 'audio',
    duration: '30-90 seconds',
    platform: 'Podcasts'
  },
  
  // Text Formats
  {
    id: 'text-copy',
    name: 'Ad Copy',
    description: 'Written copy for various platforms',
    icon: FileText,
    category: 'text',
    platform: 'Multiple'
  },
  {
    id: 'text-script',
    name: 'Video Script',
    description: 'Detailed video production script',
    icon: Video,
    category: 'text',
    platform: 'Video Production'
  }
];

const categoryColors = {
  video: 'bg-blue-100 text-blue-800',
  image: 'bg-green-100 text-green-800',
  audio: 'bg-purple-100 text-purple-800',
  text: 'bg-orange-100 text-orange-800'
};

export default function FormatSelectionModal({ 
  isOpen, 
  onClose, 
  onFormatSelect, 
  selectedFormat 
}: FormatSelectionModalProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', name: 'All Formats' },
    { id: 'video', name: 'Video' },
    { id: 'image', name: 'Image' },
    { id: 'audio', name: 'Audio' },
    { id: 'text', name: 'Text' }
  ];

  const filteredFormats = selectedCategory === 'all' 
    ? assetFormats 
    : assetFormats.filter(format => format.category === selectedCategory);

  const handleFormatSelect = (format: AssetFormat) => {
    onFormatSelect(format);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Video className="w-5 h-5" />
            Select Asset Format
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col h-full">
          {/* Category Filter */}
          <div className="flex gap-2 mb-4 pb-3 border-b">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name}
              </Button>
            ))}
          </div>

          {/* No Format Option */}
          <div className="mb-4">
            <Card 
              className={`cursor-pointer transition-colors ${
                selectedFormat === null ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
              }`}
              onClick={() => handleFormatSelect(null as any)}
            >
              <CardContent className="flex items-center justify-between p-3">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                    <FileText className="w-5 h-5 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="font-medium">No specific format</h3>
                    <p className="text-sm text-muted-foreground">Create campaigns without format constraints</p>
                  </div>
                </div>
                {selectedFormat === null && (
                  <Check className="w-5 h-5 text-primary" />
                )}
              </CardContent>
            </Card>
          </div>

          {/* Format Grid */}
          <div className="flex-1 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {filteredFormats.map((format) => {
                const Icon = format.icon;
                return (
                  <Card 
                    key={format.id}
                    className={`cursor-pointer transition-colors ${
                      selectedFormat?.id === format.id ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleFormatSelect(format)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                          <Icon className="w-5 h-5 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium truncate">{format.name}</h3>
                            <Badge 
                              variant="secondary" 
                              className={`text-xs ${categoryColors[format.category]}`}
                            >
                              {format.category}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                            {format.description}
                          </p>
                          <div className="space-y-1">
                            {format.dimensions && (
                              <div className="text-xs text-muted-foreground">
                                <span className="font-medium">Size:</span> {format.dimensions}
                              </div>
                            )}
                            {format.duration && (
                              <div className="text-xs text-muted-foreground">
                                <span className="font-medium">Duration:</span> {format.duration}
                              </div>
                            )}
                            {format.platform && (
                              <div className="text-xs text-muted-foreground">
                                <span className="font-medium">Platform:</span> {format.platform}
                              </div>
                            )}
                          </div>
                        </div>
                        {selectedFormat?.id === format.id && (
                          <Check className="w-5 h-5 text-primary flex-shrink-0" />
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}