'use client'

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON>rk<PERSON>, Target, Zap, TrendingUp, ArrowRight, Play } from 'lucide-react'
import Link from 'next/link'

export default function AIStudioCard() {
  return (
    <Card className="flex flex-col bg-gradient-to-br from-emerald-50/40 via-white to-green-50/20 border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader className="flex-none border-b border-emerald-100/60 bg-white/40 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-emerald-100 rounded-lg">
              <Bot className="h-5 w-5 text-emerald-600" />
            </div>
            <div>
              <CardTitle className="text-lg text-gray-900">AI Studio</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Your AI-powered ad creation assistant
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium">
              COMING SOON
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col space-y-4 p-6">
        {/* Studio Features Preview */}
        <div className="grid grid-cols-2 gap-3 flex-none">
          <div className="group bg-white/60 border border-emerald-200 rounded-lg p-3 text-center hover:bg-white/80 hover:border-emerald-300 transition-all duration-200 cursor-default">
            <div className="p-1.5 bg-emerald-100 rounded-lg inline-block mb-2">
              <Sparkles className="h-4 w-4 text-emerald-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Creative Generation</p>
            <p className="text-xs text-gray-600">AI scripts & concepts</p>
          </div>
          <div className="group bg-white/60 border border-emerald-200 rounded-lg p-3 text-center hover:bg-white/80 hover:border-emerald-300 transition-all duration-200 cursor-default">
            <div className="p-1.5 bg-green-100 rounded-lg inline-block mb-2">
              <Target className="h-4 w-4 text-green-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Smart Targeting</p>
            <p className="text-xs text-gray-600">Audience recommendations</p>
          </div>
          <div className="group bg-white/60 border border-emerald-200 rounded-lg p-3 text-center hover:bg-white/80 hover:border-emerald-300 transition-all duration-200 cursor-default">
            <div className="p-1.5 bg-yellow-100 rounded-lg inline-block mb-2">
              <Zap className="h-4 w-4 text-yellow-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Instant Insights</p>
            <p className="text-xs text-gray-600">Real-time feedback</p>
          </div>
          <div className="group bg-white/60 border border-emerald-200 rounded-lg p-3 text-center hover:bg-white/80 hover:border-emerald-300 transition-all duration-200 cursor-default">
            <div className="p-1.5 bg-orange-100 rounded-lg inline-block mb-2">
              <TrendingUp className="h-4 w-4 text-orange-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Performance Optimization</p>
            <p className="text-xs text-gray-600">Campaign improvements</p>
          </div>
        </div>

        {/* Description */}
        <div className="flex-1 flex flex-col justify-center">
          <div className="bg-white/60 border border-emerald-200 rounded-xl p-6 text-center hover:bg-white/70 transition-colors">
            <div className="p-3 bg-emerald-100 rounded-xl inline-block mb-4">
              <Bot className="h-10 w-10 text-emerald-600" />
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Your AI Ad Agent</h4>
            <p className="text-sm text-gray-600 mb-6 leading-relaxed">
              The future of advertising is here. Our AI Agent will be your personal marketing assistant, 
              helping you create, optimize, and scale video ads like never before.
            </p>
            
            {/* Feature highlights */}
            <div className="space-y-2 mb-6">
              <div className="flex items-center justify-center gap-2 text-xs text-gray-600">
                <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                <span>Generate ad scripts and concepts</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-xs text-gray-600">
                <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                <span>Optimize for maximum performance</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-xs text-gray-600">
                <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                <span>24/7 AI-powered assistance</span>
              </div>
            </div>
            
            <Link href="/studio">
              <Button 
                className="bg-emerald-700 hover:bg-emerald-800 text-white border-0 shadow-sm w-full group" 
                disabled
              >
                <Play className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                Launch AI Studio
                <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-0.5 transition-transform" />
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}