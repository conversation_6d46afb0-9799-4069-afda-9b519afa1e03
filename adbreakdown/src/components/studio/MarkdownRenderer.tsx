import React from 'react';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  // Simple markdown parsing for common formats
  const parseMarkdown = (text: string): React.ReactNode => {
    // Split by lines to handle different markdown elements
    const lines = text.split('\n');
    const elements: React.ReactNode[] = [];
    let listItems: string[] = [];
    let inCodeBlock = false;
    let codeLines: string[] = [];
    let codeLanguage = '';

    const flushListItems = () => {
      if (listItems.length > 0) {
        elements.push(
          <ul key={`list-${elements.length}`} className="list-disc list-inside space-y-1 my-2">
            {listItems.map((item, i) => (
              <li key={i} className="text-sm">{parseInlineMarkdown(item)}</li>
            ))}
          </ul>
        );
        listItems = [];
      }
    };

    const flushCodeBlock = () => {
      if (codeLines.length > 0) {
        elements.push(
          <pre key={`code-${elements.length}`} className="bg-muted p-3 rounded-md overflow-x-auto my-2">
            <code className={`text-sm ${codeLanguage ? `language-${codeLanguage}` : ''}`}>
              {codeLines.join('\n')}
            </code>
          </pre>
        );
        codeLines = [];
        codeLanguage = '';
      }
    };

    lines.forEach((line, index) => {
      // Handle code blocks
      if (line.trim().startsWith('```')) {
        if (inCodeBlock) {
          flushCodeBlock();
          inCodeBlock = false;
        } else {
          flushListItems();
          inCodeBlock = true;
          codeLanguage = line.trim().substring(3);
        }
        return;
      }

      if (inCodeBlock) {
        codeLines.push(line);
        return;
      }

      // Handle list items
      if (line.trim().match(/^[-*+]\s+/)) {
        const listItem = line.trim().substring(2);
        listItems.push(listItem);
        return;
      }

      // Handle numbered lists
      if (line.trim().match(/^\d+\.\s+/)) {
        flushListItems();
        const listItem = line.trim().replace(/^\d+\.\s+/, '');
        elements.push(
          <div key={`numbered-${elements.length}`} className="flex items-start gap-2 my-1">
            <span className="text-sm font-medium text-muted-foreground">{line.trim().match(/^\d+/)?.[0]}.</span>
            <span className="text-sm flex-1">{parseInlineMarkdown(listItem)}</span>
          </div>
        );
        return;
      }

      // Flush any pending lists
      flushListItems();

      // Handle headers
      if (line.trim().startsWith('###')) {
        elements.push(
          <h3 key={`h3-${elements.length}`} className="text-base font-semibold mt-4 mb-2">
            {parseInlineMarkdown(line.trim().substring(3).trim())}
          </h3>
        );
        return;
      }

      if (line.trim().startsWith('##')) {
        elements.push(
          <h2 key={`h2-${elements.length}`} className="text-lg font-semibold mt-4 mb-2">
            {parseInlineMarkdown(line.trim().substring(2).trim())}
          </h2>
        );
        return;
      }

      if (line.trim().startsWith('#')) {
        elements.push(
          <h1 key={`h1-${elements.length}`} className="text-xl font-bold mt-4 mb-2">
            {parseInlineMarkdown(line.trim().substring(1).trim())}
          </h1>
        );
        return;
      }

      // Handle blockquotes
      if (line.trim().startsWith('>')) {
        elements.push(
          <blockquote key={`quote-${elements.length}`} className="border-l-4 border-primary pl-4 italic text-muted-foreground my-2">
            {parseInlineMarkdown(line.trim().substring(1).trim())}
          </blockquote>
        );
        return;
      }

      // Handle horizontal rules
      if (line.trim().match(/^[-_*]{3,}$/)) {
        elements.push(<hr key={`hr-${elements.length}`} className="my-4 border-border" />);
        return;
      }

      // Handle empty lines
      if (line.trim() === '') {
        if (elements.length > 0) {
          elements.push(<br key={`br-${elements.length}`} />);
        }
        return;
      }

      // Regular paragraph
      elements.push(
        <p key={`p-${elements.length}`} className="text-sm leading-relaxed my-1">
          {parseInlineMarkdown(line)}
        </p>
      );
    });

    // Flush any remaining items
    flushListItems();
    flushCodeBlock();

    return elements;
  };

  // Parse inline markdown (bold, italic, code, links)
  const parseInlineMarkdown = (text: string): React.ReactNode => {
    const parts: React.ReactNode[] = [];
    let remaining = text;
    let key = 0;

    while (remaining.length > 0) {
      // Bold (**text**)
      const boldMatch = remaining.match(/^\*\*(.*?)\*\*/);
      if (boldMatch) {
        parts.push(<strong key={key++} className="font-semibold">{boldMatch[1]}</strong>);
        remaining = remaining.substring(boldMatch[0].length);
        continue;
      }

      // Italic (*text*)
      const italicMatch = remaining.match(/^\*(.*?)\*/);
      if (italicMatch) {
        parts.push(<em key={key++} className="italic">{italicMatch[1]}</em>);
        remaining = remaining.substring(italicMatch[0].length);
        continue;
      }

      // Inline code (`code`)
      const codeMatch = remaining.match(/^`(.*?)`/);
      if (codeMatch) {
        parts.push(
          <code key={key++} className="bg-muted px-1.5 py-0.5 rounded text-xs font-mono">
            {codeMatch[1]}
          </code>
        );
        remaining = remaining.substring(codeMatch[0].length);
        continue;
      }

      // Links [text](url)
      const linkMatch = remaining.match(/^\[([^\]]+)\]\(([^)]+)\)/);
      if (linkMatch) {
        const url = linkMatch[2];
        // Only allow http, https, and mailto protocols (or internal anchors)
        const isValidUrl = /^(https?:\/\/|mailto:)/i.test(url)
          || url.startsWith('/')
          || url.startsWith('#');
        if (!isValidUrl) {
          // Treat as plain text if URL is potentially malicious
          parts.push(`[${linkMatch[1]}](${url})`);
          remaining = remaining.substring(linkMatch[0].length);
          continue;
        }
        parts.push(
          <a
            key={key++}
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:underline"
          >
            {linkMatch[1]}
          </a>
        );
        remaining = remaining.substring(linkMatch[0].length);
        continue;
      }

      // Regular text
      const nextSpecialChar = remaining.search(/[\*`\[]/);
      if (nextSpecialChar === -1) {
        parts.push(remaining);
        break;
      } else {
        parts.push(remaining.substring(0, nextSpecialChar));
        remaining = remaining.substring(nextSpecialChar);
      }
    }

    return parts;
  };

  return (
    <div className={`markdown-content ${className}`}>
      {parseMarkdown(content)}
    </div>
  );
}