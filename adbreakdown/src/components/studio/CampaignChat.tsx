'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send, Bot, User, Loader2, Building2, Video, ChevronDown, Zap, MessageSquare } from 'lucide-react';
import { MarkdownRenderer } from './MarkdownRenderer';
import BrandSelectionModal from './BrandSelectionModal';
import FormatSelectionModal from './FormatSelectionModal';
import { extractCampaignData, isIdeaComplete, generateScriptPrompt } from '@/lib/campaign-extractor';
import { createCampaignAgentService, CampaignAgentService } from '@/lib/agents/campaign-agent-service';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface CampaignChatProps {
  onCampaignUpdate: (updates: any) => void;
  campaignData: any;
}

export default function CampaignChat({ onCampaignUpdate }: CampaignChatProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Hello! I\'m your campaign creation assistant. I can help you develop compelling campaign briefs and video ad scripts. What campaign are you working on today?',
      timestamp: new Date(),
    }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [streamingContent, setStreamingContent] = useState('');
  const [selectedBrand, setSelectedBrand] = useState<any>(null);
  const [selectedFormat, setSelectedFormat] = useState<any>(null);
  const [isBrandModalOpen, setIsBrandModalOpen] = useState(false);
  const [isFormatModalOpen, setIsFormatModalOpen] = useState(false);
  const [useAgentMode, setUseAgentMode] = useState(true); // Enable agent mode by default
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const streamReaderRef = useRef<ReadableStreamDefaultReader<Uint8Array> | null>(null);
  const agentServiceRef = useRef<CampaignAgentService | null>(null);

  // Initialize agent service
  useEffect(() => {
    if (!agentServiceRef.current) {
      agentServiceRef.current = createCampaignAgentService();
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, streamingContent, scrollToBottom]);

  // Cleanup function to abort ongoing requests
  useEffect(() => {
    const currentAbortController = abortControllerRef.current;
    const currentStreamReader = streamReaderRef.current;

    return () => {
      if (currentAbortController) {
        currentAbortController.abort();
      }
      if (currentStreamReader) {
        currentStreamReader.cancel();
      }
    };
  }, []);

  // Limit message history to prevent memory issues
  const trimMessages = (messages: Message[]) => {
    const MAX_MESSAGES = 20; // Keep last 20 messages
    if (messages.length > MAX_MESSAGES) {
      return [messages[0], ...messages.slice(-MAX_MESSAGES + 1)]; // Keep first message (greeting) + last 19
    }
    return messages;
  };

  const handleCampaignExtraction = async (content: string) => {
    // Extract structured campaign data from AI response
    const extractedData = extractCampaignData(content);
    
    if (extractedData.idea && Object.keys(extractedData.idea).length > 0) {
      // Update the campaign idea fields
      onCampaignUpdate({
        ideaUpdate: extractedData.idea
      });

      // Check if idea is complete enough to generate script
      if (extractedData.shouldGenerateScript && isIdeaComplete(extractedData.idea as any)) {
        // Auto-generate script
        setTimeout(() => {
          generateScript(extractedData.idea as any);
        }, 1000);
      }
    }

    if (extractedData.script) {
      // Update the script directly
      onCampaignUpdate({
        scriptUpdate: extractedData.script
      });
    }
  };

  const generateScript = async (ideaData: any) => {
    try {
      const scriptPrompt = generateScriptPrompt(ideaData, selectedFormat);

      // Create abort controller for script generation
      const scriptAbortController = new AbortController();

      const response = await fetch('/api/chat/campaign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [
            { role: 'user', content: scriptPrompt }
          ],
          context: [], // Could include brand context if needed
        }),
        signal: scriptAbortController.signal,
      });

      if (response.ok) {
        const reader = response.body?.getReader();
        if (reader) {
          const decoder = new TextDecoder();
          let scriptContent = '';

          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              const chunk = decoder.decode(value);
              const lines = chunk.split('\n');

              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  const data = line.slice(6);
                  if (data === '[DONE]') {
                    onCampaignUpdate({
                      scriptUpdate: {
                        title: ideaData.title || 'Generated Script',
                        content: scriptContent
                      }
                    });
                    return;
                  }

                  try {
                    const parsed = JSON.parse(data);
                    if (parsed.content) {
                      scriptContent += parsed.content;
                    }
                  } catch (parseError) {
                    console.error('Error parsing script generation:', parseError);
                  }
                }
              }
            }
          } catch (streamError) {
            console.error('Script generation stream error:', streamError);
          } finally {
            reader.cancel();
          }
        }
      }
    } catch (error) {
      console.error('Error generating script:', error);
    }
  };

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    // Abort any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (streamReaderRef.current) {
      streamReaderRef.current.cancel();
    }

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController();

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => trimMessages([...prev, userMessage]));
    setInput('');
    setIsLoading(true);
    setStreamingContent('');

    // Use agent mode if enabled
    if (useAgentMode && agentServiceRef.current) {
      try {
        const agentResponse = await agentServiceRef.current.processMessage(
          userMessage.content,
          selectedBrand,
          selectedFormat
        );

        const assistantMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content: agentResponse.content,
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, assistantMessage]);

        // Handle campaign updates
        if (agentResponse.campaignUpdate) {
          onCampaignUpdate(agentResponse.campaignUpdate);
        }

        setIsLoading(false);
        return;

      } catch (error) {
        console.error('Agent mode error:', error);
        // Fall back to regular mode
      }
    }

    try {
      // Prepare context information
      const contextInfo = [];
      
      if (selectedBrand) {
        contextInfo.push(`BRAND PROFILE: ${selectedBrand.brand_name}`);
        
        // Basic Information
        if (selectedBrand.tagline) {
          contextInfo.push(`Tagline: ${selectedBrand.tagline}`);
        }
        if (selectedBrand.website_url) {
          contextInfo.push(`Website: ${selectedBrand.website_url}`);
        }
        if (selectedBrand.industry_category) {
          contextInfo.push(`Industry: ${selectedBrand.industry_category}`);
        }
        if (selectedBrand.company_size) {
          contextInfo.push(`Company Size: ${selectedBrand.company_size}`);
        }
        
        // Brand Positioning & Mission
        if (selectedBrand.positioning_statement) {
          contextInfo.push(`Brand Positioning: ${selectedBrand.positioning_statement}`);
        }
        if (selectedBrand.mission_statement) {
          contextInfo.push(`Mission: ${selectedBrand.mission_statement}`);
        }
        if (selectedBrand.vision_statement) {
          contextInfo.push(`Vision: ${selectedBrand.vision_statement}`);
        }
        
        // Brand Values & Personality
        if (selectedBrand.brand_values && selectedBrand.brand_values.length > 0) {
          contextInfo.push(`Brand Values: ${selectedBrand.brand_values.join(', ')}`);
        }
        if (selectedBrand.brand_personality && selectedBrand.brand_personality.length > 0) {
          contextInfo.push(`Brand Personality: ${selectedBrand.brand_personality.join(', ')}`);
        }
        
        // Voice & Communication
        if (selectedBrand.voice_attributes && selectedBrand.voice_attributes.length > 0) {
          contextInfo.push(`Voice Attributes: ${selectedBrand.voice_attributes.join(', ')}`);
        }
        if (selectedBrand.tone_keywords && selectedBrand.tone_keywords.length > 0) {
          contextInfo.push(`Tone Keywords: ${selectedBrand.tone_keywords.join(', ')}`);
        }
        if (selectedBrand.communication_style) {
          contextInfo.push(`Communication Style: ${selectedBrand.communication_style}`);
        }
        
        // Target Audience
        if (selectedBrand.primary_demographics) {
          const demographics = selectedBrand.primary_demographics;
          const demoText = Object.entries(demographics)
            .filter(([_, value]) => value)
            .map(([key, value]) => `${key}: ${value}`)
            .join(', ');
          if (demoText) {
            contextInfo.push(`Primary Demographics: ${demoText}`);
          }
        }
        if (selectedBrand.psychographics && selectedBrand.psychographics.length > 0) {
          contextInfo.push(`Psychographics: ${selectedBrand.psychographics.join(', ')}`);
        }
        
        // Competitive Landscape
        if (selectedBrand.direct_competitors && selectedBrand.direct_competitors.length > 0) {
          contextInfo.push(`Direct Competitors: ${selectedBrand.direct_competitors.join(', ')}`);
        }
        if (selectedBrand.competitive_advantages && selectedBrand.competitive_advantages.length > 0) {
          contextInfo.push(`Competitive Advantages: ${selectedBrand.competitive_advantages.join(', ')}`);
        }
        
        // Visual Identity
        if (selectedBrand.primary_colors && selectedBrand.primary_colors.length > 0) {
          contextInfo.push(`Primary Colors: ${selectedBrand.primary_colors.join(', ')}`);
        }
        if (selectedBrand.font_primary) {
          contextInfo.push(`Primary Font: ${selectedBrand.font_primary}`);
        }
      }
      
      if (selectedFormat) {
        contextInfo.push(`Format Context: ${selectedFormat.name} - ${selectedFormat.description}`);
        if (selectedFormat.dimensions) {
          contextInfo.push(`Format Dimensions: ${selectedFormat.dimensions}`);
        }
        if (selectedFormat.duration) {
          contextInfo.push(`Format Duration: ${selectedFormat.duration}`);
        }
        if (selectedFormat.platform) {
          contextInfo.push(`Target Platform: ${selectedFormat.platform}`);
        }
        if (selectedFormat.category) {
          contextInfo.push(`Format Category: ${selectedFormat.category}`);
        }
      }

      console.log('Sending context to API:', contextInfo); // Debug log

      const response = await fetch('/api/chat/campaign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: trimMessages([...messages, userMessage]),
          context: contextInfo,
        }),
        signal: abortControllerRef.current?.signal,
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response stream');
      }

      // Store reader reference for cleanup
      streamReaderRef.current = reader;

      const decoder = new TextDecoder();
      let fullContent = '';
      const assistantMessageId = Date.now().toString();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                // Finalize the message
                const finalMessage: Message = {
                  id: assistantMessageId,
                  role: 'assistant',
                  content: fullContent,
                  timestamp: new Date(),
                };

                setMessages(prev => [...prev, finalMessage]);
                setStreamingContent('');

                // Extract campaign data from the response
                handleCampaignExtraction(fullContent);
                return; // Exit the function successfully
              }

              try {
                const parsed = JSON.parse(data);
                if (parsed.content) {
                  fullContent += parsed.content;
                  setStreamingContent(fullContent);
                }
              } catch (parseError) {
                console.error('Error parsing stream data:', parseError);
              }
            }
          }
        }
      } finally {
        // Clean up stream reader reference
        streamReaderRef.current = null;
      }
    } catch (error) {
      console.error('Error sending message:', error);

      // Handle abort errors gracefully
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Request was aborted');
        return;
      }

      const errorMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      // Clean up references
      abortControllerRef.current = null;
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex gap-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                message.role === 'user' 
                  ? 'bg-primary text-primary-foreground' 
                  : 'bg-muted text-muted-foreground'
              }`}>
                {message.role === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
              </div>
              <div className={`rounded-lg p-3 ${
                message.role === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted text-foreground'
              }`}>
                <div className="text-sm">
                  {message.role === 'assistant' ? (
                    <MarkdownRenderer content={message.content} />
                  ) : (
                    <div className="whitespace-pre-wrap">{message.content}</div>
                  )}
                </div>
                <div className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Streaming Message */}
        {streamingContent && (
          <div className="flex gap-3 justify-start">
            <div className="flex gap-3 max-w-[80%]">
              <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-muted text-muted-foreground">
                <Bot className="w-4 h-4" />
              </div>
              <div className="rounded-lg p-3 bg-muted text-foreground">
                <div className="text-sm">
                  <MarkdownRenderer content={streamingContent} />
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <Loader2 className="w-3 h-3 animate-spin" />
                  <span className="text-xs opacity-70">Typing...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-border p-4">
        {/* Context Selection */}
        <div className="flex gap-2 mb-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsBrandModalOpen(true)}
            className="flex items-center gap-2 flex-1"
          >
            <Building2 className="w-4 h-4" />
            <span className="truncate">
              {selectedBrand ? selectedBrand.brand_name : 'Select Brand'}
            </span>
            <ChevronDown className="w-3 h-3 ml-auto" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFormatModalOpen(true)}
            className="flex items-center gap-2 flex-1"
          >
            <Video className="w-4 h-4" />
            <span className="truncate">
              {selectedFormat ? selectedFormat.name : 'Select Format'}
            </span>
            <ChevronDown className="w-3 h-3 ml-auto" />
          </Button>

          <Button
            variant={useAgentMode ? "default" : "outline"}
            size="sm"
            onClick={() => setUseAgentMode(!useAgentMode)}
            className="flex items-center gap-2"
            title={useAgentMode ? "Agent Mode: ON" : "Agent Mode: OFF"}
          >
            {useAgentMode ? <Zap className="w-4 h-4" /> : <MessageSquare className="w-4 h-4" />}
            <span className="hidden sm:inline">
              {useAgentMode ? 'Agent' : 'Chat'}
            </span>
          </Button>
        </div>

        {/* Message Input */}
        <div className="flex gap-2">
          <Textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask me to help create a campaign brief or script..."
            className="min-h-[60px] max-h-[120px] resize-none"
            disabled={isLoading}
          />
          <Button
            onClick={sendMessage}
            disabled={!input.trim() || isLoading}
            size="icon"
            className="flex-shrink-0 h-[60px] w-[60px]"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
        
        {/* Context Display */}
        <div className="flex flex-wrap gap-2 mt-2">
          {selectedBrand && (
            <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full flex items-center gap-1">
              <Building2 className="w-3 h-3" />
              {selectedBrand.brand_name}
            </div>
          )}
          {selectedFormat && (
            <div className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full flex items-center gap-1">
              <Video className="w-3 h-3" />
              {selectedFormat.name}
            </div>
          )}
          {useAgentMode && agentServiceRef.current && (
            <div className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full flex items-center gap-1">
              <Zap className="w-3 h-3" />
              Agent Mode
            </div>
          )}
        </div>
        
        <div className="text-xs text-muted-foreground mt-2">
          Press Enter to send, Shift+Enter for new line
        </div>
      </div>

      {/* Modals */}
      <BrandSelectionModal
        isOpen={isBrandModalOpen}
        onClose={() => setIsBrandModalOpen(false)}
        onBrandSelect={setSelectedBrand}
        selectedBrand={selectedBrand}
      />
      
      <FormatSelectionModal
        isOpen={isFormatModalOpen}
        onClose={() => setIsFormatModalOpen(false)}
        onFormatSelect={setSelectedFormat}
        selectedFormat={selectedFormat}
      />
    </div>
  );
}