'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, AlertTriangle, XCircle, TrendingUp, Target, Lightbulb } from 'lucide-react'
import { cn } from '@/lib/utils'

interface JsonAnalysisData {
  ad_context?: {
    ad_name?: string
    ad_description?: string
    primary_business_objective?: string
    target_audience_persona?: string
    core_human_insight?: string
    grounding_data?: string
  }
  executive_summary?: {
    launch_decision?: 'LAUNCH' | 'FIX FIRST' | 'KILL'
    confidence_level?: 'High' | 'Medium' | 'Low'
    key_risk?: string
    biggest_opportunity?: string
  }
  competitive_context?: {
    summary?: string
    dominant_patterns?: string[]
    differentiation_analysis?: string
  }
  performance_forecast?: {
    attention_retention?: {
      completion_rate?: string
      reason?: string
    }
    brand_recall?: {
      level?: 'High' | 'Medium' | 'Low'
      reason?: string
    }
    action_rate?: {
      level?: 'High' | 'Medium' | 'Low'
      reason?: string
    }
    standout_factor?: {
      level?: 'High' | 'Medium' | 'Low'
      reason?: string
    }
  }
  critical_fixes?: Array<{
    fix?: string
    expected_impact?: string
    priority?: 'High' | 'Medium' | 'Low'
  }>
  risk_alerts?: {
    high_risk?: string[]
    medium_risk?: string[]
    missed_opportunities?: string[]
  }
  non_obvious_insights?: {
    industry_reality?: string
    performance_driver?: string
    tactical_advantage?: string
  }
  next_steps?: {
    edits?: string[]
    ab_testing?: string
    metrics?: string
    // Legacy field names for backward compatibility
    this_week?: string[]
    launch_strategy?: string
    post_launch?: string
  }
  citations?: Array<{
    title?: string
    url?: string
    source?: string
    relevance?: string
  }>
}

interface JsonAnalysisDisplayProps {
  content: string
  className?: string
}

const getLaunchDecisionIcon = (decision: string) => {
  switch (decision) {
    case 'LAUNCH':
      return <CheckCircle className="h-5 w-5 text-green-600" />
    case 'FIX FIRST':
      return <AlertTriangle className="h-5 w-5 text-yellow-600" />
    case 'KILL':
      return <XCircle className="h-5 w-5 text-red-600" />
    default:
      return <AlertTriangle className="h-5 w-5 text-gray-600" />
  }
}

const getLaunchDecisionColor = (decision: string) => {
  switch (decision) {
    case 'LAUNCH':
      return 'bg-green-100 text-grey-800 border-green-200'
    case 'FIX FIRST':
      return 'bg-yellow-50 text-grey-800 border-yellow-100'
    case 'KILL':
      return 'bg-red-100 text-grey-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getLevelColor = (level: string) => {
  switch (level) {
    case 'High':
      return 'bg-green-100 text-green-800'
    case 'Medium':
      return 'bg-yellow-100 text-yellow-800'
    case 'Low':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'High':
      return 'bg-red-100 text-red-800'
    case 'Medium':
      return 'bg-yellow-100 text-yellow-800'
    case 'Low':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export function JsonAnalysisDisplay({ content, className }: JsonAnalysisDisplayProps) {
  let analysisData: JsonAnalysisData
  
  try {
    analysisData = JSON.parse(content)
    
    // Validate that we have at least some recognizable content
    if (!analysisData || typeof analysisData !== 'object') {
      throw new Error('Invalid JSON structure')
    }
    
    // Check if we have at least one main section
    const hasSomeContent = analysisData.ad_context || 
                          analysisData.executive_summary || 
                          analysisData.competitive_context || 
                          analysisData.performance_forecast ||
                          analysisData.critical_fixes ||
                          analysisData.risk_alerts ||
                          analysisData.non_obvious_insights ||
                          analysisData.next_steps
    
    if (!hasSomeContent) {
      throw new Error('No recognizable analysis sections found')
    }
  } catch (error) {
    console.error('JSON Analysis Display Parse Error:', error)
    console.log('Content received:', content.substring(0, 500) + '...')
    
    return (
      <Card className="border-red-200 bg-red-50">
        <CardHeader>
          <CardTitle className="text-red-800">JSON Parse Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-700 mb-2">Failed to parse analysis JSON structure.</p>
          <p className="text-sm text-red-600 mb-4">Error: {error instanceof Error ? error.message : 'Unknown error'}</p>
          <details className="mb-4">
            <summary className="text-sm font-medium text-red-700 cursor-pointer mb-2">Show Raw Content</summary>
            <pre className="text-xs text-red-600 bg-white p-2 rounded border overflow-x-auto max-h-96">
              {content}
            </pre>
          </details>
          <p className="text-xs text-gray-600">
            This analysis will fallback to markdown display. The issue is likely with the AI response format.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Ad Context Card */}
      {analysisData.ad_context && (
        <Card className="border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-600" />
              Ad Context
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {analysisData.ad_context.ad_name && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">Ad Name</h4>
                <p className="font-semibold">{analysisData.ad_context.ad_name}</p>
              </div>
            )}
            {analysisData.ad_context.primary_business_objective && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">Business Objective</h4>
                <p className="text-sm">{analysisData.ad_context.primary_business_objective}</p>
              </div>
            )}
            {analysisData.ad_context.target_audience_persona && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">Target Audience</h4>
                <p className="text-sm">{analysisData.ad_context.target_audience_persona}</p>
              </div>
            )}
            {analysisData.ad_context.core_human_insight && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">Core Human Insight</h4>
                <p className="text-sm italic">{analysisData.ad_context.core_human_insight}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Executive Summary Card */}
      {analysisData.executive_summary && (
        <Card className={cn("border-2", getLaunchDecisionColor(analysisData.executive_summary.launch_decision || ''))}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getLaunchDecisionIcon(analysisData.executive_summary.launch_decision || '')}
                Executive Summary
              </div>
              {analysisData.executive_summary.launch_decision && (
                <Badge variant="outline" className={getLaunchDecisionColor(analysisData.executive_summary.launch_decision)}>
                  {analysisData.executive_summary.launch_decision}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {analysisData.executive_summary.confidence_level && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Confidence Level:</span>
                <Badge className={getLevelColor(analysisData.executive_summary.confidence_level || 'Medium')}>
                  {analysisData.executive_summary.confidence_level}
                </Badge>
              </div>
            )}
            {analysisData.executive_summary.key_risk && (
              <div>
                <h4 className="font-medium text-sm text-red-600 mb-1">Key Risk</h4>
                <p className="text-sm text-gray-600">{analysisData.executive_summary.key_risk}</p>
              </div>
            )}
            {analysisData.executive_summary.biggest_opportunity && (
              <div>
                <h4 className="font-medium text-sm text-green-600 mb-1">Biggest Opportunity</h4>
                <p className="text-sm text-grey-600">{analysisData.executive_summary.biggest_opportunity}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Performance Forecast Card */}
      {analysisData.performance_forecast && (
        <Card className="border-purple-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              Performance Forecast
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {analysisData.performance_forecast.attention_retention && (
                <div>
                  <h4 className="font-medium text-sm text-gray-600 mb-1">Attention Retention</h4>
                  {analysisData.performance_forecast.attention_retention.completion_rate && (
                    <p className="text-sm font-semibold">{analysisData.performance_forecast.attention_retention.completion_rate}</p>
                  )}
                  {analysisData.performance_forecast.attention_retention.reason && (
                    <p className="text-xs text-gray-500">{analysisData.performance_forecast.attention_retention.reason}</p>
                  )}
                </div>
              )}
              {analysisData.performance_forecast.brand_recall && (
                <div>
                  <h4 className="font-medium text-sm text-gray-600 mb-1">Brand Recall</h4>
                  {analysisData.performance_forecast.brand_recall.level && (
                    <Badge className={getLevelColor(analysisData.performance_forecast.brand_recall.level || 'Medium')}>
                      {analysisData.performance_forecast.brand_recall.level}
                    </Badge>
                  )}
                  {analysisData.performance_forecast.brand_recall.reason && (
                    <p className="text-xs text-gray-500 mt-1">{analysisData.performance_forecast.brand_recall.reason}</p>
                  )}
                </div>
              )}
              {analysisData.performance_forecast.action_rate && (
                <div>
                  <h4 className="font-medium text-sm text-gray-600 mb-1">Action Rate</h4>
                  {analysisData.performance_forecast.action_rate.level && (
                    <Badge className={getLevelColor(analysisData.performance_forecast.action_rate.level || 'Medium')}>
                      {analysisData.performance_forecast.action_rate.level}
                    </Badge>
                  )}
                  {analysisData.performance_forecast.action_rate.reason && (
                    <p className="text-xs text-gray-500 mt-1">{analysisData.performance_forecast.action_rate.reason}</p>
                  )}
                </div>
              )}
              {analysisData.performance_forecast.standout_factor && (
                <div>
                  <h4 className="font-medium text-sm text-gray-600 mb-1">Standout Factor</h4>
                  {analysisData.performance_forecast.standout_factor.level && (
                    <Badge className={getLevelColor(analysisData.performance_forecast.standout_factor.level || 'Medium')}>
                      {analysisData.performance_forecast.standout_factor.level}
                    </Badge>
                  )}
                  {analysisData.performance_forecast.standout_factor.reason && (
                    <p className="text-xs text-gray-500 mt-1">{analysisData.performance_forecast.standout_factor.reason}</p>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Critical Fixes Card */}
      {analysisData.critical_fixes && Array.isArray(analysisData.critical_fixes) && analysisData.critical_fixes.length > 0 && (
        <Card className="border-orange-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              Critical Fixes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analysisData.critical_fixes.map((fix, index) => (
                <div key={index} className="border rounded-lg p-3 bg-gray-50">
                  <div className="flex items-start justify-between mb-2">
                    <p className="text-sm font-medium flex-1">{fix.fix}</p>
                    <Badge className={getPriorityColor(fix.priority || 'Medium')}>
                      {fix.priority || 'Medium'}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600">Expected Impact: {fix.expected_impact}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Risk Alerts Card */}
      {analysisData.risk_alerts && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              Risk Alerts
            </CardTitle>
          </CardHeader>
        <CardContent className="space-y-4">
          {analysisData.risk_alerts.high_risk && Array.isArray(analysisData.risk_alerts.high_risk) && analysisData.risk_alerts.high_risk.length > 0 && (
            <div>
              <h4 className="font-medium text-sm text-red-700 mb-2">🚨 High Risk</h4>
              <ul className="space-y-1">
                {analysisData.risk_alerts.high_risk.map((risk, index) => (
                  <li key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                    {risk}
                  </li>
                ))}
              </ul>
            </div>
          )}
          {analysisData.risk_alerts.medium_risk && Array.isArray(analysisData.risk_alerts.medium_risk) && analysisData.risk_alerts.medium_risk.length > 0 && (
            <div>
              <h4 className="font-medium text-sm text-yellow-700 mb-2">⚠️ Medium Risk</h4>
              <ul className="space-y-1">
                {analysisData.risk_alerts.medium_risk.map((risk, index) => (
                  <li key={index} className="text-sm text-yellow-600 bg-yellow-50 p-2 rounded">
                    {risk}
                  </li>
                ))}
              </ul>
            </div>
          )}
          {analysisData.risk_alerts.missed_opportunities && Array.isArray(analysisData.risk_alerts.missed_opportunities) && analysisData.risk_alerts.missed_opportunities.length > 0 && (
            <div>
              <h4 className="font-medium text-sm text-blue-700 mb-2">💡 Missed Opportunities</h4>
              <ul className="space-y-1">
                {analysisData.risk_alerts.missed_opportunities.map((opportunity, index) => (
                  <li key={index} className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                    {opportunity}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
        </Card>
      )}

      {/* Competitive Context Card */}
      {analysisData.competitive_context && (
        <Card className="border-green-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-green-600" />
              Competitive Context
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {analysisData.competitive_context.summary && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">Summary</h4>
                <p className="text-sm">{analysisData.competitive_context.summary}</p>
              </div>
            )}
            {analysisData.competitive_context.dominant_patterns && Array.isArray(analysisData.competitive_context.dominant_patterns) && analysisData.competitive_context.dominant_patterns.length > 0 && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">Dominant Patterns</h4>
                <ul className="space-y-1 list-disc ml-5">
                  {analysisData.competitive_context.dominant_patterns.map((pattern, index) => (
                    <li key={index} className="text-sm text-gray-700">
                      {pattern}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {analysisData.competitive_context.differentiation_analysis && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">Differentiation Analysis</h4>
                <p className="text-sm">{analysisData.competitive_context.differentiation_analysis}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Non-Obvious Insights Card */}
      {analysisData.non_obvious_insights && (
        <Card className="border-indigo-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-indigo-600" />
              Non-Obvious Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {analysisData.non_obvious_insights.industry_reality && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">Industry Reality</h4>
                <p className="text-sm">{analysisData.non_obvious_insights.industry_reality}</p>
              </div>
            )}
            {analysisData.non_obvious_insights.performance_driver && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">Performance Driver</h4>
                <p className="text-sm">{analysisData.non_obvious_insights.performance_driver}</p>
              </div>
            )}
            {analysisData.non_obvious_insights.tactical_advantage && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">Tactical Advantage</h4>
                <p className="text-sm">{analysisData.non_obvious_insights.tactical_advantage}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Next Steps Card */}
      {analysisData.next_steps && (
        <Card className="border-cyan-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-cyan-600" />
              Next Steps
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Handle both new field names (edits) and legacy field names (this_week) */}
            {((analysisData.next_steps.edits && Array.isArray(analysisData.next_steps.edits) && analysisData.next_steps.edits.length > 0) ||
              (analysisData.next_steps.this_week && Array.isArray(analysisData.next_steps.this_week) && analysisData.next_steps.this_week.length > 0)) && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-2">
                  {analysisData.next_steps.edits ? 'Edits' : 'This Week'}
                </h4>
                <ul className="space-y-1">
                  {(analysisData.next_steps.edits || analysisData.next_steps.this_week || []).map((edit, index) => (
                    <li key={index} className="text-sm bg-cyan-50 p-2 rounded flex items-start gap-2">
                      <div className="w-2 h-2 bg-cyan-500 rounded-full mt-1.5 shrink-0"></div>
                      {edit}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {/* Handle both new field names (ab_testing) and legacy field names (launch_strategy) */}
            {(analysisData.next_steps.ab_testing || analysisData.next_steps.launch_strategy) && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">
                  {analysisData.next_steps.ab_testing ? 'A/B Testing' : 'Launch Strategy'}
                </h4>
                <p className="text-sm bg-cyan-50 p-2 rounded">
                  {analysisData.next_steps.ab_testing || analysisData.next_steps.launch_strategy}
                </p>
              </div>
            )}
            {/* Handle both new field names (metrics) and legacy field names (post_launch) */}
            {(analysisData.next_steps.metrics || analysisData.next_steps.post_launch) && (
              <div>
                <h4 className="font-medium text-sm text-gray-600 mb-1">
                  {analysisData.next_steps.metrics ? 'Metrics' : 'Post Launch'}
                </h4>
                <p className="text-sm bg-cyan-50 p-2 rounded">
                  {analysisData.next_steps.metrics || analysisData.next_steps.post_launch}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Citations Card */}
      {analysisData.citations && Array.isArray(analysisData.citations) && analysisData.citations.length > 0 && (
        <Card className="border-gray-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-gray-600" />
              Citations & Sources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analysisData.citations.map((citation, index) => (
                <div key={index} className="border rounded-lg p-3 bg-gray-50 hover:bg-gray-100 transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    {citation.url ? (
                      <a 
                        href={citation.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="font-medium text-sm text-blue-600 hover:text-blue-800 underline flex-1 pr-2"
                      >
                        {citation.title}
                      </a>
                    ) : (
                      <h4 className="font-medium text-sm text-gray-900 flex-1 pr-2">
                        {citation.title}
                      </h4>
                    )}
                    {citation.source && (
                      <Badge variant="outline" className="text-xs shrink-0">
                        {citation.source}
                      </Badge>
                    )}
                  </div>
                  {citation.relevance && (
                    <p className="text-xs text-gray-600">{citation.relevance}</p>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}