'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Shield, ChevronRight, Lock, Eye, AlertTriangle, CheckCircle2, ExternalLink, Crown, Upload, X, Trash2 } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import Link from 'next/link'
import Image from 'next/image'
import { useUser } from '@clerk/nextjs'

interface PrivateAnalysis {
  id: string
  slug: string
  title: string
  inferred_brand: string
  youtube_url: string
  status: string
  created_at: string
  thumbnail_url?: string
}

interface PrivateAnalysisCardProps {
  creditsRemaining: number
}

export default function PrivateAnalysisCard({ creditsRemaining }: PrivateAnalysisCardProps) {
  const [privateLoading, setPrivateLoading] = useState(false)
  const [privateAnalyses, setPrivateAnalyses] = useState<PrivateAnalysis[]>([])
  const [analysesLoading, setAnalysesLoading] = useState(true)
  const [youtubeUrl, setYoutubeUrl] = useState('')
  const [error, setError] = useState('')
  const [needsGoogleAuth, setNeedsGoogleAuth] = useState(false)
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [uploadForm, setUploadForm] = useState({
    brand: '',
    title: '',
    file: null as File | null
  })
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)
  const [isAnalyzingFile, setIsAnalyzingFile] = useState(false)
  const [deletingAnalysis, setDeletingAnalysis] = useState<string | null>(null)
  const { user } = useUser()

  // Check Google OAuth status
  useEffect(() => {
    const checkGoogleOAuthStatus = async () => {
      if (!user) return

      try {
        const response = await fetch('/api/auth/google/status')
        if (response.ok) {
          const data = await response.json()
          setNeedsGoogleAuth(!data.connected)
        } else {
          setNeedsGoogleAuth(true)
        }
      } catch (error) {
        console.error('❌ Error checking OAuth status:', error)
        setNeedsGoogleAuth(true)
      }
    }

    checkGoogleOAuthStatus()
  }, [user])

  // Check for OAuth success/error on page load
  useEffect(() => {
    const checkForOAuthResult = async () => {
      const urlParams = new URLSearchParams(window.location.search)
      
      if (urlParams.get('oauth_success') === 'true') {
        console.log('✅ OAuth connection successful!')
        setNeedsGoogleAuth(false)
        // Clear the URL parameter
        window.history.replaceState({}, document.title, window.location.pathname)
        // Refresh OAuth status
        const response = await fetch('/api/auth/google/status')
        if (response.ok) {
          const data = await response.json()
          setNeedsGoogleAuth(!data.connected)
        }
      }
      
      if (urlParams.get('oauth_error')) {
        const errorType = urlParams.get('oauth_error')
        console.error('❌ OAuth error:', errorType)
        setError(`OAuth connection failed: ${errorType}`)
        // Clear the URL parameter
        window.history.replaceState({}, document.title, window.location.pathname)
      }
    }
    
    checkForOAuthResult()
  }, [])

  useEffect(() => {
    const fetchPrivateAnalyses = async () => {
      try {
        setAnalysesLoading(true)
        const response = await fetch('/api/analyses/private?limit=5')
        if (response.ok) {
          const data = await response.json()
          setPrivateAnalyses(data.analyses || [])
        }
      } catch (error) {
        console.error('Error fetching private analyses:', error)
      } finally {
        setAnalysesLoading(false)
      }
    }

    fetchPrivateAnalyses()
  }, [])

  const handleConnectGoogle = () => {
    try {
      setError('')
      console.log('🔗 Initiating direct Google OAuth flow...')
      
      // Redirect to our OAuth initiation endpoint
      window.location.href = '/api/auth/google/initiate'
      
    } catch (error: any) {
      console.error('❌ Error initiating OAuth:', error)
      setError('Failed to initiate Google connection. Please try again.')
    }
  }

  const handlePrivateAnalyze = async (youtubeUrl: string) => {
    try {
      setPrivateLoading(true)
      setError('')
      console.log('🔒 Starting private analysis:', { youtubeUrl })

      const response = await fetch('/api/analyses/private', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ youtubeUrl })
      })

      const data = await response.json()

      if (!response.ok) {
        if (data.requiresAuth) {
          throw new Error('OAuth authentication required')
        }
        throw new Error(data.error || 'Failed to create private analysis')
      }

      // Navigate to private analysis page
      console.log('✅ Private analysis created, redirecting to:', data.redirect)
      if (data.redirect) {
        window.location.href = data.redirect
      } else {
        window.location.href = `/studio/private/${data.analysis.slug}`
      }

    } catch (error: any) {
      console.error('❌ Error creating private analysis:', error)
      if (error.message && error.message.includes('OAuth')) {
        setNeedsGoogleAuth(true)
        setError('Please connect your Google account to access this video')
      } else {
        setError(error.message || 'An error occurred while processing your request')
      }
    } finally {
      setPrivateLoading(false)
    }
  }

  const handleSubmit = async () => {
    if (!youtubeUrl.trim()) {
      setError('Please enter a YouTube URL')
      return
    }

    // Check if Google auth is connected
    if (needsGoogleAuth) {
      setError('Please connect your Google account to analyze private videos')
      return
    }

    // Check credits before proceeding
    if (creditsRemaining <= 0) {
      setError('Insufficient credits. Please upgrade your plan.')
      return
    }

    // Validate YouTube URL format
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)([\w-]+)/
    if (!youtubeRegex.test(youtubeUrl)) {
      setError('Please enter a valid YouTube URL')
      return
    }

    await handlePrivateAnalyze(youtubeUrl)
    setYoutubeUrl('')
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Check file type (video files)
      const allowedTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov', 'video/wmv']
      if (!allowedTypes.includes(file.type)) {
        setError('Please select a valid video file (MP4, WebM, OGG, AVI, MOV, WMV)')
        return
      }
      
      // Check file size (limit to 100MB)
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (file.size > maxSize) {
        setError('File size must be under 100MB')
        return
      }
      
      setUploadForm(prev => ({ ...prev, file }))
      setError('')
    }
  }

  const handleUploadAnalyze = async () => {
    if (!uploadForm.brand.trim() || !uploadForm.title.trim() || !uploadForm.file) {
      setError('Please fill in all fields and select a file')
      return
    }

    if (creditsRemaining <= 0) {
      setError('Insufficient credits. Please upgrade your plan.')
      return
    }

    setIsUploading(true)
    setUploadProgress(0)
    setError('')

    try {
      // 1. Get signed URL from your API
      console.log('Requesting signed URL for upload modal...')
      const signedUrlRes = await fetch('/api/uploads/signed-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ fileName: uploadForm.file.name, contentType: uploadForm.file.type }),
      })

      if (!signedUrlRes.ok) {
        const errorData = await signedUrlRes.json()
        throw new Error(errorData.error || 'Failed to get signed URL')
      }

      const { url, fileName: uniqueFileName } = await signedUrlRes.json()
      console.log('Received signed URL:', url)

      // 2. Upload file directly to GCS using the signed URL
      console.log('Uploading file to GCS...')
      const xhr = new XMLHttpRequest()
      xhr.open('PUT', url)
      xhr.setRequestHeader('Content-Type', uploadForm.file.type)

      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const percentCompleted = Math.round((event.loaded * 100) / event.total)
          setUploadProgress(percentCompleted)
          console.log(`Upload progress: ${percentCompleted}%`)
        }
      }

      await new Promise<void>((resolve, reject) => {
        xhr.onload = () => {
          console.log('XHR onload - Status:', xhr.status, 'StatusText:', xhr.statusText)
          if (xhr.status >= 200 && xhr.status < 300) {
            console.log('File uploaded successfully to GCS.')
            resolve()
          } else {
            console.error('GCS upload failed - Status:', xhr.status, 'StatusText:', xhr.statusText)
            console.error('Response text:', xhr.responseText)
            reject(new Error(`GCS upload failed: ${xhr.status} ${xhr.statusText}`))
          }
        }
        xhr.onerror = (event) => {
          console.error('XHR onerror event:', event)
          reject(new Error(`Network error during file upload. Status: ${xhr.status}, ReadyState: ${xhr.readyState}`))
        }
        xhr.ontimeout = () => {
          console.error('XHR timeout during GCS upload')
          reject(new Error('Upload timeout - please try again with a smaller file'))
        }
        xhr.send(uploadForm.file)
      })

      setIsUploading(false)
      setIsAnalyzingFile(true)
      console.log('Creating analysis entry in database...')

      // 3. Create analysis using the main private analysis endpoint with GCS URI
      const analysisRes = await fetch('/api/analyses/private', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          gcsUri: `gs://${process.env.NEXT_PUBLIC_GCS_BUCKET_NAME}/${uniqueFileName}`,
          brand: uploadForm.brand,
          title: uploadForm.title
        }),
      })

      if (!analysisRes.ok) {
        const errorData = await analysisRes.json()
        throw new Error(errorData.error || 'Failed to create analysis')
      }

      const analysisData = await analysisRes.json()
      console.log('Private analysis created successfully!', analysisData)
      
      // Reset form and close modal immediately
      setUploadForm({ brand: '', title: '', file: null })
      setUploadProgress(0)
      setIsAnalyzingFile(false)
      setShowUploadModal(false)
      
      // Navigate to the analysis page immediately (analysis will continue in background)
      console.log('✅ Navigating to analysis page:', analysisData.redirect)
      if (analysisData.redirect) {
        window.location.href = analysisData.redirect
      } else {
        // Fallback redirect
        window.location.href = `/studio/private/${analysisData.analysis.slug}`
      }

    } catch (error: any) {
      console.error('❌ Error uploading video:', error)
      setError(error.message || 'An error occurred while uploading your video')
      setIsUploading(false)
      setIsAnalyzingFile(false)
    }
  }

  const resetUploadForm = () => {
    setUploadForm({ brand: '', title: '', file: null })
    setError('')
    setUploadProgress(0)
    setIsUploading(false)
    setIsAnalyzingFile(false)
    setShowUploadModal(false)
  }

  const handleDeleteAnalysis = async (analysis: PrivateAnalysis, event: React.MouseEvent) => {
    event.preventDefault() // Prevent navigation to analysis page
    event.stopPropagation()

    const confirmDelete = window.confirm(
      `Are you sure you want to delete "${analysis.title}"?\n\n` +
      'This action cannot be undone. ' +
      (analysis.youtube_url.startsWith('gs://') 
        ? 'The uploaded video file will also be permanently deleted from storage.' 
        : 'This will remove the analysis from your account.')
    )

    if (!confirmDelete) return

    try {
      setDeletingAnalysis(analysis.id)
      
      const response = await fetch(`/api/analyses/private/${analysis.slug}/delete`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete analysis')
      }

      const result = await response.json()
      console.log('✅ Analysis deleted successfully:', result)

      // Remove the analysis from the local state
      setPrivateAnalyses(prev => prev.filter(a => a.id !== analysis.id))
      
    } catch (error: any) {
      console.error('❌ Error deleting analysis:', error)
      setError(`Failed to delete analysis: ${error.message}`)
    } finally {
      setDeletingAnalysis(null)
    }
  }

  return (
    <Card className="flex flex-col bg-gradient-to-br from-blue-150 via-indigo-150 to-purple-300 border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader className="flex-none border-b border-gray-200 bg-white rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-slate-100 rounded-lg">
              <Shield className="h-5 w-5 text-slate-600" />
            </div>
            <div>
              <CardTitle className="text-lg text-gray-900">Pre-Launch Analysis</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Private video analysis with optimization insights
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="px-2 py-1 bg-slate-100 text-slate-700 rounded-full text-xs font-medium">
              BETA
            </div>
            {!needsGoogleAuth && (
              <div className="p-1.5 bg-green-100 rounded-lg" title="Google Account Connected">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col space-y-4 p-6">
        {/* Google Auth Status & Input Section */}
        <div className="flex-none space-y-3">
          {/* YouTube Access Status */}
          {needsGoogleAuth ? (
            <div className="flex items-center justify-between gap-3">
              <div className="group relative flex-1">
                <div className="flex items-center justify-between p-3 border border-amber-200 bg-amber-50/50 rounded-xl hover:bg-amber-50 transition-colors cursor-pointer"
                     onClick={handleConnectGoogle}>
                  <div className="flex items-center gap-3">
                    {/* YouTube Logo SVG */}
                    <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect width="24" height="24" rx="5" fill="#FF0000"/>
                      <polygon points="10,8 16,12 10,16" fill="#fff"/>
                    </svg>
                    <div>
                      <p className="text-sm font-medium text-amber-900">Youtube Access</p>
                      <p className="text-xs text-amber-700">Grant access (read only) to analyse unlisted video ads</p>
                    </div>
                  </div>
                  <ExternalLink className="h-4 w-4 text-amber-600 group-hover:text-amber-700 transition-colors" />
                </div>
              </div>
              
              <Dialog open={showUploadModal} onOpenChange={(open) => {
                if (!isUploading && !isAnalyzingFile) {
                  setShowUploadModal(open)
                }
              }}>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2 bg-white hover:bg-gray-50 border-gray-300"
                    disabled={needsGoogleAuth}
                  >
                    <Upload className="h-4 w-4" />
                    Upload Video
                  </Button>
                </DialogTrigger>
                
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <div className="flex items-center justify-between">
                      <DialogTitle className="text-lg font-semibold">Upload Video for Analysis</DialogTitle>
                      {!isUploading && !isAnalyzingFile && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={resetUploadForm}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </DialogHeader>
                  
                  <div className="space-y-4 py-4">
                    {/* Brand Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Brand Name</label>
                      <Input
                        type="text"
                        placeholder="Enter brand name"
                        value={uploadForm.brand}
                        onChange={(e) => setUploadForm(prev => ({ ...prev, brand: e.target.value }))}
                        className="w-full"
                        disabled={isUploading || isAnalyzingFile}
                      />
                    </div>
                    
                    {/* Title Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Ad Title</label>
                      <Input
                        type="text"
                        placeholder="Enter ad title"
                        value={uploadForm.title}
                        onChange={(e) => setUploadForm(prev => ({ ...prev, title: e.target.value }))}
                        className="w-full"
                        disabled={isUploading || isAnalyzingFile}
                      />
                    </div>
                    
                    {/* File Upload */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Video File</label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                        {uploadForm.file ? (
                          <div className="space-y-2">
                            <div className="flex items-center justify-center gap-2 text-green-600">
                              <CheckCircle2 className="h-5 w-5" />
                              <span className="text-sm font-medium">File Selected</span>
                            </div>
                            <p className="text-sm text-gray-600">{uploadForm.file.name}</p>
                            <p className="text-xs text-gray-500">
                              {(uploadForm.file.size / (1024 * 1024)).toFixed(2)} MB
                            </p>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setUploadForm(prev => ({ ...prev, file: null }))}
                              disabled={isUploading || isAnalyzingFile}
                            >
                              Change File
                            </Button>
                          </div>
                        ) : (
                          <div className="space-y-2">
                            <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                            <div>
                              <label htmlFor="video-upload" className="cursor-pointer">
                                <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                                  Click to upload
                                </span>
                                <span className="text-sm text-gray-500"> or drag and drop</span>
                              </label>
                              <input
                                id="video-upload"
                                type="file"
                                accept="video/*"
                                onChange={handleFileUpload}
                                className="hidden"
                                disabled={isUploading || isAnalyzingFile}
                              />
                            </div>
                            <p className="text-xs text-gray-500">
                              MP4, WebM, OGG, AVI, MOV, WMV up to 100MB
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Progress Display */}
                    {(isUploading || isAnalyzingFile) && (
                      <div className="space-y-3">
                        <Progress value={isUploading ? uploadProgress : 100} className="w-full h-3" />
                        <div className="text-center">
                          {isUploading && (
                            <div className="space-y-2">
                              <p className="text-lg font-semibold text-blue-600">
                                {uploadProgress}%
                              </p>
                              <p className="text-sm text-gray-600">
                                Uploading to secure cloud storage...
                              </p>
                            </div>
                          )}
                          
                          {isAnalyzingFile && (
                            <div className="space-y-2">
                              <div className="flex items-center justify-center gap-2">
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                                <p className="text-lg font-semibold text-blue-600">
                                  Creating Analysis
                                </p>
                              </div>
                              <p className="text-sm text-gray-600">
                                Setting up your private analysis...
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Error Display */}
                    {error && (
                      <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <AlertTriangle className="h-4 w-4 text-red-600 flex-shrink-0" />
                        <p className="text-sm text-red-700">{error}</p>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-3 pt-4">
                      <Button
                        variant="outline"
                        onClick={resetUploadForm}
                        className="flex-1"
                        disabled={isUploading || isAnalyzingFile}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleUploadAnalyze}
                        disabled={isUploading || isAnalyzingFile || !uploadForm.brand.trim() || !uploadForm.title.trim() || !uploadForm.file}
                        className="flex-1 bg-slate-800 hover:bg-slate-900 text-white"
                      >
                        {isUploading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Uploading...
                          </>
                        ) : isAnalyzingFile ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Analyzing...
                          </>
                        ) : (
                          <>
                            <Eye className="h-4 w-4 mr-2" />
                            Analyze
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          ) : (
            <div className="flex items-center justify-between gap-3">
              <div className="flex items-center gap-2 p-2 bg-green-50/50 rounded-lg border border-green-100">
                <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none">
                  <path d="M23.498 12.275c0-.764-.07-1.52-.2-2.262H12.24v4.285h6.319c-.275 1.497-1.103 2.774-2.347 3.627v3.01h3.804c2.221-2.043 3.482-5.054 3.482-8.66z" fill="#4285F4"/>
                  <path d="M12.24 24c3.24 0 5.956-1.075 7.875-2.905l-3.804-3.01c-1.075.72-2.433 1.145-4.071 1.145-3.13 0-5.781-2.114-6.728-4.955H1.620v3.105C3.573 21.068 7.654 24 12.24 24z" fill="#34A853"/>
                  <path d="M5.512 14.275c-.24-.72-.375-1.487-.375-2.275s.135-1.555.375-2.275V6.620H1.620C.59 8.685 0 10.395 0 12s.59 3.315 1.620 5.38l3.892-3.105z" fill="#FBBC05"/>
                  <path d="M12.24 4.77c1.765 0 3.349.607 4.595 1.794l3.448-3.448C18.190 1.19 15.474 0 12.24 0 7.654 0 3.573 2.932 1.620 7.62l3.892 3.105c.947-2.841 3.598-4.955 6.728-4.955z" fill="#EA4335"/>
                </svg>
                <span className="text-sm font-medium">YouTube Access:</span>
                <span className="text-sm text-green-700 font-semibold">Granted</span>
              </div>
              
              <Dialog open={showUploadModal} onOpenChange={(open) => {
                if (!isUploading && !isAnalyzingFile) {
                  setShowUploadModal(open)
                }
              }}>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2 bg-white hover:bg-gray-50 border-gray-300"
                  >
                    <Upload className="h-4 w-4" />
                    Upload Video
                  </Button>
                </DialogTrigger>
                
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <div className="flex items-center justify-between">
                      <DialogTitle className="text-lg font-semibold">Upload Video for Analysis</DialogTitle>
                      {!isUploading && !isAnalyzingFile && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={resetUploadForm}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </DialogHeader>
                  
                  <div className="space-y-4 py-4">
                    {/* Brand Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Brand Name</label>
                      <Input
                        type="text"
                        placeholder="Enter brand name"
                        value={uploadForm.brand}
                        onChange={(e) => setUploadForm(prev => ({ ...prev, brand: e.target.value }))}
                        className="w-full"
                      />
                    </div>
                    
                    {/* Title Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Ad Title</label>
                      <Input
                        type="text"
                        placeholder="Enter ad title"
                        value={uploadForm.title}
                        onChange={(e) => setUploadForm(prev => ({ ...prev, title: e.target.value }))}
                        className="w-full"
                      />
                    </div>
                    
                    {/* File Upload */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Video File</label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                        {uploadForm.file ? (
                          <div className="space-y-2">
                            <div className="flex items-center justify-center gap-2 text-green-600">
                              <CheckCircle2 className="h-5 w-5" />
                              <span className="text-sm font-medium">File Selected</span>
                            </div>
                            <p className="text-sm text-gray-600">{uploadForm.file.name}</p>
                            <p className="text-xs text-gray-500">
                              {(uploadForm.file.size / (1024 * 1024)).toFixed(2)} MB
                            </p>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setUploadForm(prev => ({ ...prev, file: null }))}
                            >
                              Change File
                            </Button>
                          </div>
                        ) : (
                          <div className="space-y-2">
                            <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                            <div>
                              <label htmlFor="video-upload-2" className="cursor-pointer">
                                <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                                  Click to upload
                                </span>
                                <span className="text-sm text-gray-500"> or drag and drop</span>
                              </label>
                              <input
                                id="video-upload-2"
                                type="file"
                                accept="video/*"
                                onChange={handleFileUpload}
                                className="hidden"
                              />
                            </div>
                            <p className="text-xs text-gray-500">
                              MP4, WebM, OGG, AVI, MOV, WMV up to 100MB
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Progress Display */}
                    {(isUploading || isAnalyzingFile) && (
                      <div className="space-y-3">
                        <Progress value={isUploading ? uploadProgress : 100} className="w-full h-3" />
                        <div className="text-center">
                          {isUploading && (
                            <div className="space-y-2">
                              <p className="text-lg font-semibold text-blue-600">
                                {uploadProgress}%
                              </p>
                              <p className="text-sm text-gray-600">
                                Uploading to secure cloud storage...
                              </p>
                            </div>
                          )}
                          
                          {isAnalyzingFile && (
                            <div className="space-y-2">
                              <div className="flex items-center justify-center gap-2">
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                                <p className="text-lg font-semibold text-blue-600">
                                  Creating Analysis
                                </p>
                              </div>
                              <p className="text-sm text-gray-600">
                                Setting up your private analysis...
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Error Display */}
                    {error && (
                      <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <AlertTriangle className="h-4 w-4 text-red-600 flex-shrink-0" />
                        <p className="text-sm text-red-700">{error}</p>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-3 pt-4">
                      <Button
                        variant="outline"
                        onClick={resetUploadForm}
                        className="flex-1"
                        disabled={isUploading || isAnalyzingFile}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleUploadAnalyze}
                        disabled={isUploading || isAnalyzingFile || !uploadForm.brand.trim() || !uploadForm.title.trim() || !uploadForm.file}
                        className="flex-1 bg-slate-800 hover:bg-slate-900 text-white"
                      >
                        {isUploading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Uploading...
                          </>
                        ) : isAnalyzingFile ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Creating Analysis...
                          </>
                        ) : (
                          <>
                            <Eye className="h-4 w-4 mr-2" />
                            Analyze
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          )}

          {/* URL Input */}
          <div className="space-y-2">
            <div className="relative">
              <Input
                type="url"
                placeholder="Enter YouTube URL (private, unlisted, or public)"
                value={youtubeUrl}
                onChange={(e) => {
                  setYoutubeUrl(e.target.value)
                  setError('')
                }}
                className="pr-24 border-slate-200 focus:border-slate-400 focus:ring-slate-400/20 bg-white/80 placeholder:text-slate-400"
                disabled={privateLoading || needsGoogleAuth}
              />
              <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-2">
                <div className="flex items-center gap-1 text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-md">
                  <Crown className="h-3 w-3" />
                  {creditsRemaining}
                </div>
              </div>
            </div>
            
            {error && (
              <div className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-red-600 flex-shrink-0" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            <Button 
              onClick={handleSubmit}
              disabled={privateLoading || !youtubeUrl.trim() || needsGoogleAuth}
              className="w-full bg-slate-800 hover:bg-slate-900 text-white border-0 shadow-sm"
            >
              {privateLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Analyzing Privately...
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Analyze Privately
                </>
              )}
            </Button>
          </div>
        </div>
        
        {/* Recent Private Analyses List */}
        <div className="flex-1 min-h-0">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-gray-900 text-sm">Recent Private Analyses</h4>
            <Link href="/studio/private">
              <Button variant="ghost" size="sm" className="text-slate-600 hover:text-slate-800 hover:bg-slate-100 h-8 px-2">
                <span className="text-xs">View All</span>
                <ChevronRight className="h-3 w-3 ml-1" />
              </Button>
            </Link>
          </div>
          
          <div className="space-y-2 max-h-[280px] overflow-y-auto">
            {analysesLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin h-5 w-5 border-b-2 border-slate-400"></div>
              </div>
            ) : privateAnalyses.length > 0 ? (
              privateAnalyses.map((analysis) => (
                <div key={analysis.id} className="group border border-slate-200 rounded-lg p-3 hover:bg-white/80 hover:border-slate-300 hover:shadow-sm transition-all duration-200">
                    <div className="flex items-center gap-3">
                      {analysis.thumbnail_url && (
                        <div className="relative">
                          <Image 
                            src={analysis.thumbnail_url} 
                            alt={analysis.title}
                            width={60}
                            height={40}
                            className="rounded-md object-cover border border-slate-200"
                          />
                          <div className="absolute -top-1 -right-1 p-0.5 bg-slate-800 rounded-full">
                            <Lock className="h-2.5 w-2.5 text-white" />
                          </div>
                        </div>
                      )}
                      <Link href={`/studio/private/${analysis.slug}`} className="flex-1 min-w-0">
                        <div className="cursor-pointer">
                          <p className="font-medium text-sm text-slate-900 truncate group-hover:text-slate-700">
                            {analysis.title}
                          </p>
                          <p className="text-xs text-slate-500">{analysis.inferred_brand}</p>
                          <div className="flex items-center gap-2 mt-1.5">
                            <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                              analysis.status === 'completed' ? 'bg-green-100 text-green-700 border border-green-200' :
                              analysis.status === 'processing' ? 'bg-blue-100 text-blue-700 border border-blue-200' :
                              'bg-amber-100 text-amber-700 border border-amber-200'
                            }`}>
                              {analysis.status}
                            </span>
                            <span className="text-xs text-slate-400">
                              {new Date(analysis.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </Link>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => handleDeleteAnalysis(analysis, e)}
                          disabled={deletingAnalysis === analysis.id}
                          className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          {deletingAnalysis === analysis.id ? (
                            <div className="animate-spin h-3 w-3 border border-red-500 border-t-transparent rounded-full" />
                          ) : (
                            <Trash2 className="h-3 w-3" />
                          )}
                        </Button>
                        <ChevronRight className="h-4 w-4 text-slate-400 group-hover:text-slate-600 transition-colors" />
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-slate-500">
                  <div className="p-3 bg-slate-100 rounded-xl inline-block mb-3">
                    <Shield className="h-8 w-8 text-slate-400" />
                  </div>
                  <p className="text-sm">No private analyses yet</p>
                  <p className="text-xs text-slate-400 mt-1">Analyze your first private video above</p>
                </div>
              )}
            </div>
        </div>
      </CardContent>
    </Card>
  )
}