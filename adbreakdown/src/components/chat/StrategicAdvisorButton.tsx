'use client'

import React, { useState } from 'react'
import { TrendingUp, MessageCircle, Sparkles } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import StrategicChatOverlay from './StrategicChatOverlay'

interface StrategicAdvisorButtonProps {
  analysisData: any
  variant?: 'floating' | 'inline'
  className?: string
}

export default function StrategicAdvisorButton({ 
  analysisData, 
  variant = 'floating',
  className = '' 
}: StrategicAdvisorButtonProps) {
  const [isChatOpen, setIsChatOpen] = useState(false)

  if (variant === 'floating') {
    return (
      <>
        <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
          <Button
            onClick={() => setIsChatOpen(true)}
            size="lg"
            className="h-14 w-14 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-200 group"
            title="Get strategic marketing advice"
          >
            <div className="relative">
              <TrendingUp className="w-6 h-6 text-white" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse">
                <Sparkles className="w-2 h-2 text-yellow-600 absolute top-0.5 left-0.5" />
              </div>
            </div>
          </Button>
          <div className="absolute -top-10 right-0 bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
            AI Strategist
          </div>
        </div>
        <StrategicChatOverlay
          isOpen={isChatOpen}
          onClose={() => setIsChatOpen(false)}
          analysisData={analysisData}
        />
      </>
    )
  }

  return (
    <>
      <Button
        onClick={() => setIsChatOpen(true)}
        variant="outline"
        className={`bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200 hover:from-purple-100 hover:to-blue-100 ${className}`}
      >
        <TrendingUp className="w-4 h-4 mr-2 text-purple-600" />
        AI Strategist
        <div className="ml-2 w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
      </Button>
      <StrategicChatOverlay
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
        analysisData={analysisData}
      />
    </>
  )
}