'use client'

import React, { useRef, useEffect } from 'react'
import { Send, User, X, TrendingUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { useAuth } from '@/hooks/useAuth'
import { useUser } from '@clerk/nextjs'
import { CompactMarkdown } from '@/components/ui/markdown-renderer'
import Image from 'next/image'
import { useChat } from 'ai/react'

interface StrategicChatOverlayProps {
  isOpen: boolean
  onClose: () => void
  analysisData: any // The complete private analysis data
}

export default function StrategicChatOverlay({ isOpen, onClose, analysisData }: StrategicChatOverlayProps) {
  const { isAuthenticated } = useAuth()
  const { user } = useUser()
  
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: '/api/chat/private-analysis',
    body: {
      analysisData,
    },
    initialMessages: [
        {
            id: '1',
            role: 'assistant',
            content: "Hello! I'm your strategic marketing advisor. I've reviewed your complete ad analysis and I'm ready to provide actionable recommendations to optimize your campaign performance.\n\n**I can help you with:**\n• Creative optimization strategies\n• Audience targeting refinements\n• Competitive positioning analysis\n• A/B testing recommendations\n• Campaign scaling strategies\n• Performance improvement tactics\n\nWhat would you like to focus on first?",
          }
    ]
  });

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleSubmit(e);
  };

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-40 flex justify-end">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-25 transition-opacity"
        onClick={onClose}
      />
      
      {/* Chat Panel */}
      <div className="relative w-full md:w-1/3 min-w-[320px] max-w-[480px] bg-white shadow-2xl flex flex-col h-[80vh] md:h-full mt-[20vh] md:mt-0">
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center">
              <TrendingUp className="w-3 h-3 text-white" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">AI Strategist</h3>
              <p className="text-xs text-gray-500">Marketing optimization AI</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="p-1 h-8 w-8"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.role === 'assistant' && (
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center flex-shrink-0">
                  <TrendingUp className="w-4 h-4 text-white" />
                </div>
              )}
              <div
                className={`max-w-[80%] px-3 py-2 rounded-lg text-sm ${
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                {message.role === 'user' ? (
                  <p>{message.content}</p>
                ) : (
                  <CompactMarkdown content={message.content} />
                )}
              </div>
              {message.role === 'user' && (
                <Avatar className="w-8 h-8 flex-shrink-0">
                  {isAuthenticated && user?.imageUrl ? (
                    <AvatarImage src={user.imageUrl} alt={user.firstName || 'User'} />
                  ) : null}
                  <AvatarFallback className="bg-gray-200 text-gray-600 text-sm font-medium">
                    {isAuthenticated && user?.firstName 
                      ? user.firstName.charAt(0).toUpperCase()
                      : <User className="w-4 h-4" />
                    }
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}
          {isLoading && messages[messages.length -1].role === 'user' && (
            <div className="flex gap-3 justify-start">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center flex-shrink-0">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <div className="bg-gray-100 px-3 py-2 rounded-lg">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Quick Suggestions */}
        <div className="px-4 py-2 bg-gray-50 border-t border-gray-100">
          <div className="text-xs text-gray-500 mb-2">Quick suggestions:</div>
          <div className="flex flex-wrap gap-1">
            {[
              "How can I improve CTR?",
              "Optimize my targeting",
              "Creative recommendations",
              "Competitor analysis"
            ].map((suggestion) => (
              <button
                key={suggestion}
                onClick={() => {
                  if (inputRef.current) {
                    inputRef.current.value = suggestion;
                    handleInputChange({ target: { value: suggestion } } as any);
                  }
                }}
                className="text-xs px-2 py-1 bg-white border border-gray-200 rounded-full hover:bg-gray-50 transition-colors"
                disabled={isLoading}
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>

        {/* Input */}
        <form onSubmit={handleFormSubmit} className="border-t border-gray-200 p-4">
          <div className="flex gap-2">
            <input
              ref={inputRef}
              type="text"
              value={input}
              onChange={handleInputChange}
              placeholder="Ask for strategic advice on optimizing your ad..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
              disabled={isLoading}
            />
            <Button
              type="submit"
              disabled={!input.trim() || isLoading}
              size="sm"
              className="px-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}