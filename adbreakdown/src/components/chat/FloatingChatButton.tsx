'use client'

import React from 'react'
import { MessageCircle, X } from 'lucide-react'

interface FloatingChatButtonProps {
  onClick: () => void
  isOpen: boolean
}

export default function FloatingChatButton({ onClick, isOpen }: FloatingChatButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`fixed bottom-6 right-6 z-50 w-14 h-14 rounded-full shadow-lg transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
        isOpen 
          ? 'bg-gray-600 hover:bg-gray-700' 
          : 'bg-blue-600 hover:bg-blue-700'
      }`}
      aria-label={isOpen ? 'Close chat' : 'Open chat'}
    >
      {isOpen ? (
        <X className="w-6 h-6 text-white mx-auto" />
      ) : (
        <MessageCircle className="w-6 h-6 text-white mx-auto" />
      )}
    </button>
  )
}