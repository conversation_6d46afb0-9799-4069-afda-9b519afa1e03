import { useState, useCallback } from 'react';

export interface StreamMessage {
  type: 'thinking' | 'saving' | 'done' | 'error';
  content?: string;
  slug?: string;
}

export const useAnalysisStream = () => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [thinkingText, setThinkingText] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [finalSlug, setFinalSlug] = useState<string | null>(null);

  const startStream = useCallback(async (analysisId: string, youtubeUrl: string) => {
    if (isStreaming) {
      console.log('🚫 Stream already in progress, ignoring new request');
      return;
    }

    console.log('🌊 Starting analysis stream for:', { analysisId, youtubeUrl });
    setIsStreaming(true);
    setThinkingText('');
    setError(null);
    setFinalSlug(null);

    try {
      console.log('📡 Making streaming request to trigger-vertex-analysis...');
      const response = await fetch(`/api/analyses/${analysisId}/trigger-vertex-analysis`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify({ youtubeUrl }),
      });

      console.log('📡 Response received:', { ok: response.ok, status: response.status, hasBody: !!response.body });

      if (!response.ok || !response.body) {
        const err = await response.json().catch(() => ({ error: 'Failed to start analysis stream' }));
        throw new Error(err.error);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      const processStream = async () => {
        console.log('🔄 Starting stream processing loop...');
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            console.log('✅ Stream completed (done=true)');
            setIsStreaming(false);
            break;
          }

          const chunk = decoder.decode(value);
          console.log('📦 Received chunk:', chunk.length, 'characters');
          
          const lines = chunk.split('\n\n').filter(line => line.trim() !== '');
          console.log('📝 Processing', lines.length, 'lines from chunk');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const jsonString = line.substring(6);
              console.log('📨 Raw stream message:', jsonString);
              
              try {
                const message: StreamMessage = JSON.parse(jsonString);
                console.log('✅ Parsed stream message:', message);
                
                switch (message.type) {
                  case 'thinking':
                    console.log('💭 Adding thinking text:', message.content?.substring(0, 30) + '...');
                    setThinkingText(prev => prev + (message.content || ''));
                    break;
                  case 'saving':
                    console.log('💾 Setting saving status');
                    setThinkingText(prev => prev + 'Saving final analysis...\n');
                    break;
                  case 'done':
                    console.log('🎉 Stream done! Final slug:', message.slug);
                    setThinkingText(prev => prev + 'Analysis completed!\n');
                    setFinalSlug(message.slug || null);
                    setIsStreaming(false);
                    break;
                  case 'error':
                    console.error('❌ Stream error:', message.content);
                    setError(message.content || 'An unknown stream error occurred.');
                    setIsStreaming(false);
                    break;
                  default:
                    console.warn('⚠️ Unknown message type:', message.type);
                }
              } catch (parseError) {
                console.error('❌ Failed to parse stream message:', {
                  jsonString: jsonString.substring(0, 200) + '...',
                  jsonEnd: jsonString.substring(jsonString.length - 100),
                  error: parseError,
                  position: jsonString.length,
                  rawChunk: chunk.substring(0, 300)
                });
                
                // Try to extract any readable content for debugging
                try {
                  const partialMatch = jsonString.match(/\{[^}]*\}/);
                  if (partialMatch) {
                    console.log('🔍 Found partial JSON in malformed message:', partialMatch[0]);
                  }
                } catch (e) {
                  // Ignore secondary parsing errors
                }
                
                // Don't break the stream for parse errors, just skip this message
                // Only set error if it's an actual error message or if we see repeated failures
                if (jsonString.toLowerCase().includes('error') || jsonString.toLowerCase().includes('failed')) {
                  console.error('❌ Stream contains error content, stopping stream');
                  setError('Analysis stream error: ' + (jsonString.substring(0, 100) || 'Invalid response format'));
                  setIsStreaming(false);
                }
              }
            } else {
              console.log('⚠️ Non-data line received:', line);
            }
          }
        }
      };
      processStream();
    } catch (err: any) {
      console.error('❌ Stream error:', err);
      setError(err.message);
      setIsStreaming(false);
    }
  }, [isStreaming]);

  const resetStream = useCallback(() => {
    setIsStreaming(false);
    setThinkingText('');
    setError(null);
    setFinalSlug(null);
  }, []);

  return { isStreaming, thinkingText, error, finalSlug, startStream, resetStream };
};