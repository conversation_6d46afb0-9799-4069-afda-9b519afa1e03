import { useState } from 'react'

interface PDFDownloadOptions {
  filename?: string
  containerSelector?: string
  excludeSelectors?: string[]
}

export const usePDFDownload = () => {
  const [isGenerating, setIsGenerating] = useState(false)

  const downloadPDF = async (options: PDFDownloadOptions = {}) => {
    const {
      filename = 'analysis-report.pdf',
      containerSelector = 'main',
      excludeSelectors = [
        'nav', 'button', '.hidden', '[data-pdf-download]',
        'script', 'style', '.no-pdf', '.download-btn'
      ]
    } = options

    try {
      setIsGenerating(true)

      // Import html2pdf dynamically
      const html2pdf = (await import('html2pdf.js')).default
      
      // Get the main content
      const mainContent = document.querySelector(containerSelector)
      if (!mainContent) {
        throw new Error('Content container not found')
      }
      
      const clonedContent = mainContent.cloneNode(true) as HTMLElement
      
      // Remove navigation, buttons, and other non-essential elements
      excludeSelectors.forEach(selector => {
        const elements = clonedContent.querySelectorAll(selector)
        elements.forEach(el => el.remove())
      })
      
      // Create a formatted PDF container that preserves desktop layout
      const pdfContainer = document.createElement('div')
      pdfContainer.style.width = '1200px' // Keep desktop width
      pdfContainer.style.maxWidth = '1200px'
      pdfContainer.style.margin = '0 auto'
      pdfContainer.style.padding = '40px'
      pdfContainer.style.fontFamily = 'Arial, sans-serif'
      pdfContainer.style.fontSize = '14px'
      pdfContainer.style.lineHeight = '1.5'
      pdfContainer.style.color = '#333'
      pdfContainer.style.backgroundColor = '#fff'
      
      // Keep the desktop layout intact
      clonedContent.style.maxWidth = '100%'
      clonedContent.style.padding = '0'
      clonedContent.style.margin = '0'
      
      // Style headings with standard CSS properties
      const headings = clonedContent.querySelectorAll('h1, h2, h3, h4, h5, h6')
      headings.forEach(heading => {
        const h = heading as HTMLElement
        h.style.color = '#333'
        h.style.marginBottom = '0.5em'
        h.style.marginTop = '1em'
      })
      
      // Style paragraphs
      const paragraphs = clonedContent.querySelectorAll('p')
      paragraphs.forEach(p => {
        const para = p as HTMLElement
        para.style.marginBottom = '0.5em'
      })
      
      // Style cards and containers
      const cards = clonedContent.querySelectorAll('.card, [class*="card"], .border, .rounded')
      cards.forEach(card => {
        const c = card as HTMLElement
        c.style.border = '1px solid #ddd'
        c.style.padding = '1em'
        c.style.marginBottom = '1em'
        c.style.borderRadius = '4px'
        c.style.pageBreakInside = 'avoid' // Prevent cards from breaking across pages
        c.style.breakInside = 'avoid'     // Modern CSS property for page breaks
      })
      
      pdfContainer.appendChild(clonedContent)
      
      // Append to body temporarily
      document.body.appendChild(pdfContainer)
      
      // Configure PDF options to preserve desktop layout
      const pdfOptions = {
        margin: 0.5,
        filename,
        image: { type: 'jpeg' as const, quality: 0.9 },
        html2canvas: { 
          scale: 1,
          useCORS: true,
          width: 1200, // Desktop width
          windowWidth: 1200
        },
        jsPDF: { 
          unit: 'pt' as const, 
          format: [864, 1152] as [number, number], // Custom format to fit desktop layout (1200px * 0.72 = 864pt)
          orientation: 'portrait' as const,
          compress: true
        }
      }

      // Generate and download PDF
      await html2pdf().set(pdfOptions).from(pdfContainer).save()
      
      // Remove temporary container
      document.body.removeChild(pdfContainer)
      
    } catch (error) {
      console.error('PDF generation error:', error)
      throw error
    } finally {
      setIsGenerating(false)
    }
  }

  return {
    downloadPDF,
    isGenerating
  }
}