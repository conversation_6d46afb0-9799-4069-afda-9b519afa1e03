import { MetadataRoute } from 'next'
import fs from 'fs'
import path from 'path'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://breakdown.ad'
  
  console.log('Generating sitemap for:', baseUrl)
  
  // Static pages - always available
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/ad-library`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/featured`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/pricing`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/faq`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/docs`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/case-studies`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/api-docs`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/frameworks`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    }
  ]

  // Framework pages - with better error handling
  const frameworkPages: MetadataRoute.Sitemap = await getFrameworkPages(baseUrl)

  // Dynamic pages for public analyses - with timeout and fallback
  const analysisPages: MetadataRoute.Sitemap = await getAnalysisPages(baseUrl)

  const allPages = [...staticPages, ...frameworkPages, ...analysisPages]
  
  console.log(`Generated sitemap with ${allPages.length} pages`)
  
  return allPages
}

async function getFrameworkPages(baseUrl: string): Promise<MetadataRoute.Sitemap> {
  try {
    const frameworksDir = path.join(process.cwd(), 'public', 'frameworks')
    
    // Check if directory exists
    if (!fs.existsSync(frameworksDir)) {
      console.warn('Frameworks directory not found:', frameworksDir)
      return []
    }
    
    const frameworkFiles = fs.readdirSync(frameworksDir)
      .filter(file => file.endsWith('.md') && file !== 'index.md')
    
    const pages = frameworkFiles.map(file => {
      try {
        const slug = file.replace(/\.md$/, '')
        const filePath = path.join(frameworksDir, file)
        const stats = fs.statSync(filePath)
        
        return {
          url: `${baseUrl}/frameworks/${slug}`,
          lastModified: stats.mtime,
          changeFrequency: 'monthly' as const,
          priority: 0.7,
        }
      } catch (error) {
        console.error(`Error processing framework file ${file}:`, error)
        return null
      }
    }).filter(Boolean) as MetadataRoute.Sitemap
    
    console.log(`Found ${pages.length} framework pages`)
    return pages
    
  } catch (error) {
    console.error('Error reading framework files for sitemap:', error)
    return []
  }
}

async function getAnalysisPages(baseUrl: string): Promise<MetadataRoute.Sitemap> {
  try {
    // Set a timeout for database operations
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Database query timeout')), 10000) // 10 second timeout
    })
    
    const queryPromise = (async () => {
      const { OptimizedAnalysisQueries } = await import('@/lib/optimizedQueries')
      const publicAnalysesData = await OptimizedAnalysisQueries.getPublicAnalysesList(1, 1000) // Increased limit
      
      if (!publicAnalysesData?.analyses) {
        console.warn('No public analyses found')
        return []
      }
      
      const pages = publicAnalysesData.analyses
        .filter(analysis => analysis.slug && analysis.created_at) // Ensure required fields exist
        .map(analysis => ({
          url: `${baseUrl}/ad/${analysis.slug}`,
          lastModified: new Date(analysis.created_at),
          changeFrequency: 'monthly' as const,
          priority: 0.6,
        }))
      
      console.log(`Found ${pages.length} analysis pages`)
      return pages
    })()
    
    return await Promise.race([queryPromise, timeoutPromise])
    
  } catch (error) {
    console.error('Error generating dynamic sitemap entries:', error)
    
    // Return empty array instead of failing completely
    return []
  }
}
