// This file has been deprecated in favor of the main sitemap.ts
// Having multiple sitemaps can confuse search engines
// All sitemap functionality is now handled by src/app/sitemap.ts

import { NextResponse } from 'next/server'

export async function GET() {
  // Redirect to the main sitemap
  return NextResponse.redirect(new URL('/sitemap.xml', process.env.NEXT_PUBLIC_APP_URL || 'https://breakdown.ad'))
}