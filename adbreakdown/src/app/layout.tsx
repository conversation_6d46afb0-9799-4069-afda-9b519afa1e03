import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs'
import './globals.css'
import { generateMetadata, seoConfigs, structuredData } from '@/lib/seo'
import GoogleAnalytics from '@/components/GoogleAnalytics'

const inter = Inter({ 
  subsets: ['latin'], 
  variable: '--font-inter',
  display: 'swap',
  preload: true
})

export const metadata: Metadata = generateMetadata({
  ...seoConfigs.home,
  structuredData: [
    structuredData.organization,
    structuredData.website,
    structuredData.softwareApplication
  ]
})

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkProvider>
      <html lang="en" className={inter.variable}>
        <head>
          <GoogleAnalytics />
          {/* Preconnect to external domains */}
          <link rel="preconnect" href="https://img.youtube.com" />
          <link rel="preconnect" href="https://i.ytimg.com" />
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
          {/* DNS prefetch for performance */}
          <link rel="dns-prefetch" href="https://www.googletagmanager.com" />
          <link rel="dns-prefetch" href="https://www.google-analytics.com" />
        </head>
        <body className={inter.className}>
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}
