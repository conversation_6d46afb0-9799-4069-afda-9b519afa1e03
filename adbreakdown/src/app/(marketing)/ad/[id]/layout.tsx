import { generateMetadata as generateSEOMetadata } from '@/lib/seo'
import { OptimizedAnalysisQueries } from '@/lib/optimizedQueries'

interface Props {
  params: Promise<{ id: string }>
}

export async function generateMetadata({ params }: Props) {
  const { id } = await params
  
  try {
    const analysis = await OptimizedAnalysisQueries.getBasicAnalysis(id)
    
    if (!analysis) {
      return generateSEOMetadata({
        title: 'Analysis Not Found',
        description: 'The requested video ad analysis could not be found.',
        noIndex: true
      })
    }

    return generateSEOMetadata({
      title: `${analysis.title} - AI Video Ad Analysis`,
      description: `Comprehensive AI analysis of ${analysis.inferred_brand}'s video ad: ${analysis.title}. Discover insights on sentiment, targeting, and creative effectiveness.`,
      keywords: [
        'video ad analysis',
        analysis.inferred_brand,
        'ai analysis',
        'sentiment analysis',
        'marketing insights',
        'ad performance',
        'creative analysis'
      ],
      canonicalUrl: `/ad/${analysis.slug}`,
      ogImage: analysis.video_thumbnail_url,
      structuredData: {
        "@context": "https://schema.org",
        "@type": "VideoObject",
        "name": analysis.title,
        "description": `AI analysis of ${analysis.inferred_brand}'s video advertisement`,
        "thumbnailUrl": analysis.video_thumbnail_url,
        "uploadDate": analysis.created_at,
        "duration": `PT${analysis.video_duration}S`,
        "contentUrl": analysis.video_url,
        "embedUrl": analysis.video_url,
        "publisher": {
          "@type": "Organization",
          "name": "AdBreakdown",
          "url": "https://breakdown.ad"
        }
      }
    })
  } catch (error) {
    console.error('Error generating metadata for analysis:', error)
    return generateSEOMetadata({
      title: 'Video Ad Analysis',
      description: 'AI-powered video ad analysis with comprehensive insights and recommendations.',
      keywords: ['video ad analysis', 'ai analysis', 'marketing insights']
    })
  }
}

export default function AdAnalysisLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}