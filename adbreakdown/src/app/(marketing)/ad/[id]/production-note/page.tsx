'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import MarkdownRenderer from '@/components/ui/markdown-renderer'
import Navigation from '@/components/Navigation'
import LoadingSkeleton from '@/components/analysis/LoadingSkeleton'
import ErrorDisplay from '@/components/analysis/ErrorDisplay'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function ProductionNotePage() {
  const params = useParams()
  const analysisId = params.id as string
  const [enhancedScript, setEnhancedScript] = useState<string | null>(null)
  const [analysis, setAnalysis] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const fetchAnalysis = useCallback(async () => {
    if (!analysisId) return
    setLoading(true)
    try {
      const timestamp = Date.now()
      const res = await fetch(`/api/analyses/${analysisId}?_t=${timestamp}`, {
        cache: 'no-store',
      })
      if (!res.ok) {
        const errData = await res.json()
        throw new Error(errData.error || 'Failed to fetch analysis data.')
      }
      const data = await res.json()
      setAnalysis(data)
      // Handle legacy data structure
      if (data.deciphered_script?.enhanced_analysis) {
        setEnhancedScript(data.deciphered_script.enhanced_analysis)
      }

      // Handle new reports structure
      if (data.reports) {
        const enhancedScriptReport = data.reports.find((r: any) => r.report_types.name === 'enhanced_script');
        if (enhancedScriptReport && enhancedScriptReport.content) {
          setEnhancedScript(typeof enhancedScriptReport.content === 'string' ? enhancedScriptReport.content : enhancedScriptReport.content.raw_content);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
    } finally {
      setLoading(false)
    }
  }, [analysisId])

  useEffect(() => {
    fetchAnalysis()
  }, [fetchAnalysis])

  const renderMarkdownContent = (content: string) => (
    <MarkdownRenderer 
      content={content} 
      variant="default"
      className="generated-content table-container"
    />
  )

  if (loading) {
    return <LoadingSkeleton />
  }

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      <main className="container mx-auto px-3 md:px-4 py-3 md:py-4 max-w-4xl">
        <ErrorDisplay error={error} />
        <div className="mb-4">
          <Link href={`/ad/${analysis?.slug || analysisId}`}>
            <Button variant="outline">Back to Analysis</Button>
          </Link>
        </div>
        {enhancedScript ? (
          <Card>
            <CardHeader>
              <CardTitle>Enhanced Script Analysis (Production Note)</CardTitle>
            </CardHeader>
            <CardContent>
              {renderMarkdownContent(enhancedScript)}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Production Note Not Available</CardTitle>
            </CardHeader>
            <CardContent>
              <p>The enhanced script analysis has not been generated for this ad yet.</p>
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  )
}