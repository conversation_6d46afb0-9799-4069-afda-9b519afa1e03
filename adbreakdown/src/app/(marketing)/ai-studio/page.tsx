'use client'

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON>rkles, Target, TrendingUp, Wand2, MessageSquare, PenTool, Brain } from 'lucide-react'
import Navigation from '@/components/Navigation'
import Link from 'next/link'

export default function StudioPage() {
  return (
    <div className="flex flex-1 flex-col gap-4">
      <Navigation />
      {/* Header */}
      <div className="flex items-center gap-4 mb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">AI Studio</h1>
          <p className="text-gray-600">Your AI-powered ad creation assistant</p>
        </div>
      </div>

      {/* Coming Soon Card */}
      <Card className="max-w-4xl mx-auto bg-gradient-to-br from-emerald-50 to-teal-50 border-emerald-200/60 shadow-lg">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-emerald-100 rounded-full">
              <Bot className="h-12 w-12 text-emerald-600" />
            </div>
          </div>
          <CardTitle className="text-2xl text-emerald-900">AI Studio</CardTitle>
          <CardDescription className="text-lg text-emerald-700">
            Revolutionary AI-powered ad creation and optimization tools
          </CardDescription>
          <div className="flex justify-center mt-4">
            <div className="px-4 py-2 bg-emerald-100 text-emerald-800 rounded-full text-sm font-semibold">
              COMING SOON
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-4">
          {/* Feature Preview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-emerald-100 rounded-lg">
                <PenTool className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <h3 className="font-semibold text-emerald-900">AI Script Generator</h3>
                <p className="text-sm text-emerald-700">Generate compelling ad scripts based on your brand voice and target audience</p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-emerald-100 rounded-lg">
                <MessageSquare className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <h3 className="font-semibold text-emerald-900">Creative Brief Assistant</h3>
                <p className="text-sm text-emerald-700">Get AI-powered help creating detailed creative briefs for your campaigns</p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-emerald-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <h3 className="font-semibold text-emerald-900">Performance Predictor</h3>
                <p className="text-sm text-emerald-700">Predict ad performance using AI analysis of historical data</p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-emerald-100 rounded-lg">
                <Target className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <h3 className="font-semibold text-emerald-900">A/B Test Optimizer</h3>
                <p className="text-sm text-emerald-700">Get intelligent recommendations for A/B testing your ad variations</p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-emerald-100 rounded-lg">
                <Brain className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <h3 className="font-semibold text-emerald-900">Brand Voice Analyzer</h3>
                <p className="text-sm text-emerald-700">Ensure consistency across campaigns with AI brand voice analysis</p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-emerald-100 rounded-lg">
                <Wand2 className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <h3 className="font-semibold text-emerald-900">Smart Templates</h3>
                <p className="text-sm text-emerald-700">Access AI-generated templates tailored to your industry and goals</p>
              </div>
            </div>
          </div>

          {/* Coming Soon Message */}
          <div className="text-center p-6 bg-white/60 rounded-lg">
            <Sparkles className="h-8 w-8 text-emerald-600 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-emerald-900 mb-2">
              The Future of Ad Creation is Here
            </h3>
            <p className="text-emerald-700 mb-4">
              Our AI Studio is currently in development, powered by cutting-edge AI technology.
              Create better ads faster with intelligent assistance at every step of your workflow.
            </p>
            <p className="text-sm text-emerald-600">
              Be the first to know when AI Studio launches - exciting features coming soon!
            </p>
          </div>
        </CardContent>
      </Card>

      {/* CTA Section */}
      <div className="mt-12 text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Start analyzing ads while you wait
        </h2>
        <div className="flex flex-wrap justify-center gap-4">
          <Link href="/studio">
            <Button variant="outline" className="bg-white">
              Back to Studio
            </Button>
          </Link>
          <Link href="/ad-library">
            <Button className="bg-emerald-600 hover:bg-emerald-700">
              Browse Ad Library
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}