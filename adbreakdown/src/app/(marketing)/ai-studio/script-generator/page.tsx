'use client'

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { PenTool, Sparkles } from 'lucide-react'

export default function ScriptGeneratorPage() {
  return (
    <div className="flex flex-1 flex-col gap-4">
      {/* Header */}
      <div className="flex items-center gap-4 mb-4">
        <div className="p-2 bg-emerald-100 rounded-lg">
          <PenTool className="h-6 w-6 text-emerald-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">AI Script Generator</h1>
          <p className="text-gray-600">Generate compelling ad scripts based on your brand voice and target audience</p>
        </div>
      </div>

      {/* Coming Soon Card */}
      <Card className="max-w-4xl mx-auto bg-gradient-to-br from-emerald-50 to-teal-50 border-emerald-200/60 shadow-lg">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-emerald-100 rounded-full">
              <PenTool className="h-12 w-12 text-emerald-600" />
            </div>
          </div>
          <CardTitle className="text-2xl text-emerald-900">AI Script Generator</CardTitle>
          <CardDescription className="text-lg text-emerald-700">
            Create compelling ad scripts with AI assistance
          </CardDescription>
          <div className="flex justify-center mt-4">
            <div className="px-4 py-2 bg-emerald-100 text-emerald-800 rounded-full text-sm font-semibold">
              COMING SOON
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-4">
          {/* Coming Soon Message */}
          <div className="text-center p-6 bg-white/60 rounded-lg">
            <Sparkles className="h-8 w-8 text-emerald-600 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-emerald-900 mb-2">
              Intelligent Script Generation
            </h3>
            <p className="text-emerald-700 mb-4">
              Our AI Script Generator will help you create compelling ad scripts tailored to your brand voice, 
              target audience, and campaign objectives. Generate multiple variations, optimize for different 
              platforms, and ensure consistency across all your campaigns.
            </p>
            <p className="text-sm text-emerald-600">
              This feature is currently in development and will be available soon!
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
