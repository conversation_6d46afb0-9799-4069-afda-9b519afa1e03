import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Search } from 'lucide-react'
import Navigation from '@/components/Navigation'

export default function FrameworkNotFound() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-md mx-auto text-center">
          <div className="mb-8">
            <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900">Framework Not Found</h1>
            <p className="text-gray-600 mt-2">
              The framework you&apos;re looking for doesn&apos;t exist or may have been moved.
            </p>
          </div>

          <Card className="text-left">
            <CardHeader>
              <CardTitle>What you can do:</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-3">
                <Link href="/frameworks">
                  <Button className="w-full">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Browse All Frameworks
                  </Button>
                </Link>
                
                <Link href="/featured">
                  <Button variant="outline" className="w-full">
                    See Featured Analysis
                  </Button>
                </Link>
                
                <Link href="/ad-library">
                  <Button variant="outline" className="w-full">
                    Explore Ad Library
                  </Button>
                </Link>
              </div>
              
              <div className="text-sm text-gray-500 pt-4 border-t">
                <p>Popular frameworks:</p>
                <div className="flex flex-wrap gap-2 mt-2">
                  <Link href="/frameworks/aida" className="text-blue-600 hover:underline">AIDA</Link>
                  <span>•</span>
                  <Link href="/frameworks/pas" className="text-blue-600 hover:underline">PAS</Link>
                  <span>•</span>
                  <Link href="/frameworks/emotional-arcs" className="text-blue-600 hover:underline">Emotional Arcs</Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}