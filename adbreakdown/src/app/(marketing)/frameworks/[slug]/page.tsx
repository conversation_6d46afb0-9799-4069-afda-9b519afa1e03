import { notFound } from 'next/navigation'
import { getFrameworkBySlug, getRelatedFrameworks, Framework } from '@/lib/frameworks'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft, ArrowRight, CheckCircle, Lightbulb, BarChart, Youtube, Upload } from 'lucide-react'
import Link from 'next/link'
import Navigation from '@/components/Navigation'
import { generateMetadata as generateSEOMetadata } from '@/lib/seo'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const framework = getFrameworkBySlug(slug)
  if (!framework) {
    return {}
  }
  return generateSEOMetadata({
    title: `${framework.name} Framework - Ad Analysis - Breakdown.Ad`,
    description: `Learn how to apply the ${framework.name} model to improve your ad analysis. See examples, use cases, and expert tips for ${framework.summary}.`,
    keywords: [framework.name, 'ad analysis', 'marketing framework', ...framework.tags],
    canonicalUrl: `/frameworks/${framework.slug}`
  })
}

// Mock data for chart - replace with actual analysis data
const pasTimelineData = [
  { stage: 'Problem', duration: 30, color: 'bg-red-500' },
  { stage: 'Agitate', duration: 40, color: 'bg-yellow-500' },
  { stage: 'Solution', duration: 30, color: 'bg-green-500' },
]

export default async function FrameworkPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const framework = getFrameworkBySlug(slug)

  if (!framework) {
    notFound()
  }

  const relatedFrameworks = getRelatedFrameworks(framework.slug, framework.relatedFrameworks)

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      <main className="max-w-7xl mx-auto px-4 py-16">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link href="/frameworks" className="text-sm font-medium text-gray-600 hover:text-blue-600 flex items-center">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Frameworks
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <header className="mb-12 pb-8 border-b border-gray-200">
              <div className="flex items-center space-x-6 mb-4">
                <span className="text-6xl">{framework.icon}</span>
                <h1 className="text-5xl font-extrabold text-gray-900">{framework.name}</h1>
              </div>
              <p className="text-xl text-gray-600 mt-4">{framework.summary}</p>
              <div className="mt-6 flex items-center gap-2">
                <Badge variant="secondary" className="text-sm capitalize py-1 px-3 rounded-full">{framework.category}</Badge>
                <Badge variant="outline" className="text-sm capitalize py-1 px-3 rounded-full">{framework.complexity}</Badge>
              </div>
            </header>

            <article className="prose prose-lg max-w-none">
              <section id="deep-dive" className="mb-16">
                <h2 className="text-3xl font-bold mb-6">Framework Deep Dive</h2>
                <p className="text-lg text-gray-700 leading-relaxed">{framework.description}</p>
                <Card className="mt-6 bg-blue-50 border-blue-200">
                  <CardHeader>
                    <CardTitle className="flex items-center text-blue-900">
                      <Lightbulb className="w-6 h-6 mr-3" />
                      Psychological Principle
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-blue-800">
                    <p>{framework.psychology || 'This framework leverages loss aversion and the desire for resolution. By first highlighting a problem, it creates a cognitive dissonance that the solution then satisfyingly resolves.'}</p>
                  </CardContent>
                </Card>
              </section>

              <Separator className="my-12" />

              <section id="breakdown" className="mb-16">
                <h2 className="text-3xl font-bold mb-8">Framework Breakdown</h2>
                <div className="space-y-8">
                  {framework.breakdown.map((step, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 h-12 w-12 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold text-xl mr-6">{index + 1}</div>
                      <div>
                        <h3 className="text-2xl font-semibold text-gray-900 mb-2">{step.title}</h3>
                        <p className="text-lg text-gray-700 leading-relaxed">{step.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              <Separator className="my-12" />

              <section id="examples" className="mb-16">
                <h2 className="text-3xl font-bold mb-8">Real-World Examples</h2>
                <Card className="bg-white shadow-sm border-gray-200">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      {framework.exampleAd.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="mb-4">{framework.exampleAd.description}</p>
                    <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden flex">
                      {pasTimelineData.map(data => (
                        <div key={data.stage} className={`${data.color} h-full`} style={{ width: `${data.duration}%` }} title={`${data.stage} (${data.duration}%)`}></div>
                      ))}
                    </div>
                    <div className="flex justify-between text-xs text-gray-500 mt-2">
                      <span>Problem</span>
                      <span>Agitate</span>
                      <span>Solution</span>
                    </div>
                  </CardContent>
                </Card>
              </section>

              <Separator className="my-12" />

              <section id="implementation">
                <h2 className="text-3xl font-bold mb-8">Implementation Guide</h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <span>**Identify the Core Pain Point:** What is the single most significant problem your audience faces?</span>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <span>**Amplify the Consequences:** Use emotional language to describe the negative impact of the problem.</span>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <span>**Position as the Perfect Fix:** Frame your product not just as a solution, but as *the* solution.</span>
                  </div>
                </div>
              </section>
            </article>
          </div>

          {/* Sidebar */}
          <aside>
            <div className="sticky top-24 space-y-8">
              <Card className="bg-white shadow-lg border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart className="w-6 h-6 mr-3 text-blue-600"/>
                    Analyze an Ad with {framework.name}
                  </CardTitle>
                  <CardDescription>See how this framework applies to any ad.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="relative">
                      <Youtube className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <Input placeholder="Enter YouTube URL" className="pl-10" />
                    </div>
                    <Link href="/studio">
                      <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                        Analyze with AI
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              {relatedFrameworks.length > 0 && (
                <Card className="bg-white shadow-lg border-gray-200">
                  <CardHeader>
                    <CardTitle>Related Frameworks</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-4">
                      {relatedFrameworks.map((related: Framework) => (
                        <li key={related.slug}>
                          <Link href={`/frameworks/${related.slug}`} className="group flex items-center justify-between text-gray-700 hover:text-blue-600">
                            <div className="flex items-center">
                              <span className="text-2xl mr-4">{related.icon}</span>
                              <span className="font-medium">{related.name}</span>
                            </div>
                            <ArrowRight className="w-5 h-5 opacity-0 group-hover:opacity-100 transition-all group-hover:translate-x-1" />
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              <Card className="bg-white shadow-lg border-gray-200">
                <CardHeader>
                  <CardTitle>Tags</CardTitle>
                </CardHeader>
                <CardContent className="flex flex-wrap gap-2">
                  {framework.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="py-1 px-3 rounded-full">{tag}</Badge>
                  ))}
                </CardContent>
              </Card>
            </div>
          </aside>
        </div>
      </main>
    </div>
  )
}