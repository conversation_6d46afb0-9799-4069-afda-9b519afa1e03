import Link from 'next/link'

// Static generation - no revalidation needed
export const revalidate = false
import { Button } from '@/components/ui/button'
import { ArrowRight, CheckCircle } from 'lucide-react'
import { frameworks } from '@/lib/frameworks'
import Navigation from '@/components/Navigation'
import FrameworksClient from '@/components/frameworks/FrameworksClient'

export default function FrameworksPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      {/* Hero Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-24 text-center">
          <h1 className="text-5xl font-bold tracking-tight text-gray-900 sm:text-6xl mb-4">
            Master Ad Analysis with Proven Frameworks
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Combine AI insights with time-tested marketing frameworks to decode what makes ads effective.
          </p>
          <Link href="/studio">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
              Analyze Your Ad
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </Link>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-4 pb-24">
        <FrameworksClient frameworks={frameworks} />

        {/* How AI + Frameworks Work Together */}
        <div className="mt-24 bg-white rounded-lg shadow-lg p-12">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">How AI + Frameworks Work Together</h2>
              <p className="text-lg text-gray-600 mb-6">
                breakdown.ad&apos;s AI provides the raw data—sentiment, objects, and transcripts. Frameworks provide the lens to interpret that data, turning insights into a strategic narrative.
              </p>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <CheckCircle className="w-6 h-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold">Objective Analysis</h4>
                    <p className="text-gray-600">AI objectively identifies elements. Frameworks tell you if they&apos;re in the right place.</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-6 h-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold">Deeper Insights</h4>
                    <p className="text-gray-600">Go beyond surface-level metrics. Understand the *why* behind an ad&apos;s performance.</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-6 h-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold">Actionable Recommendations</h4>
                    <p className="text-gray-600">Get concrete suggestions for improvement based on proven models.</p>
                  </div>
                </li>
              </ul>
            </div>
            <div className="bg-gray-100 rounded-lg p-8">
              <h4 className="font-bold text-lg mb-4 text-center">Before vs. After Framework Analysis</h4>
              <div className="flex justify-between items-center text-sm text-gray-500 mb-2">
                <span>Without Framework</span>
                <span>With Framework</span>
              </div>
              <div className="space-y-4">
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="font-semibold text-red-800">“The ad shows a person smiling at the end.”</p>
                </div>
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="font-semibold text-green-800">“The ad uses the PAS model: the smile represents the &apos;Solution&apos; to the &apos;Problem&apos; and &apos;Agitation&apos; shown earlier, creating a powerful emotional resolution.”</p>
                </div>
              </div>
              <Link href="/studio">
                <Button className="w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white">
                  Analyze an Ad Now
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-24 bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-12 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-6">
            Ready to analyze ads like a pro?
          </h3>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
            Use these frameworks with Breakdown.Ad&apos;s AI-powered analysis to gain deeper insights into what makes ads effective.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/sign-up">
              <Button size="lg" className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium px-8 py-4 rounded-xl">
                Start Analyzing
              </Button>
            </Link>
            <Link href="/featured">
              <Button variant="outline" size="lg" className="font-medium px-8 py-4 rounded-xl border-2">
                See Example Analysis
              </Button>
            </Link>
          </div>
        </div>
      </main>
    </div>
  )
}
