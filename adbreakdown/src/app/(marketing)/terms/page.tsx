'use client'

import Navigation from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { FileText, Calendar, Shield, CreditCard, Users, Gavel, AlertTriangle, Globe } from 'lucide-react'
import Link from 'next/link'

const termsData = {
  lastUpdated: "July 5, 2025",
  title: "Terms and Conditions",
  subtitle: "Please read these terms carefully before using breakdown.ad",
  sections: [
    {
      id: "intro",
      icon: FileText,
      title: "Introduction",
      description: "Your agreement to these terms",
      content: [
        "Please read these Terms and Conditions (\"Terms,\" \"Terms and Conditions\") carefully before using the breakdown.ad website and the breakdown.ad AI-powered video ad analysis SaaS platform (the \"Service\") operated by breakdown.ad (\"us,\" \"we,\" or \"our\").",
        "Your access to and use of the Service is conditioned upon your acceptance of and compliance with these Terms. These Terms apply to all visitors, users, and others who wish to access or use the Service.",
        "By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the Terms, then you do not have permission to access the Service."
      ]
    },
    {
      id: "accounts",
      icon: Users,
      title: "Accounts",
      description: "Account creation and security requirements",
      content: [
        "When you create an account with us, you guarantee that you are above the age of 18, and that the information you provide us is accurate, complete, and current at all times.",
        "You are responsible for maintaining the confidentiality of your account and password, including but not limited to the restriction of access to your computer and/or account.",
        "You agree to accept responsibility for any and all activities or actions that occur under your account and/or password, whether your password is with our Service or a third-party service (e.g., Clerk).",
        "You must notify us immediately upon becoming aware of any breach of security or unauthorized use of your account."
      ]
    },
    {
      id: "billing",
      icon: CreditCard,
      title: "Subscriptions and Billing",
      description: "Payment terms and subscription management",
      content: [
        "Some parts of the Service are billed on a subscription basis (\"Subscription(s)\"). You will be billed in advance on a recurring and periodic basis (\"Billing Cycle\").",
        "At the end of each Billing Cycle, your Subscription will automatically renew under the exact same conditions unless you cancel it or breakdown.ad cancels it.",
        "A valid payment method, such as a credit card or PayPal, is required to process the payment for your Subscription.",
        "Should automatic billing fail for any reason, breakdown.ad will issue an electronic invoice indicating that you must proceed manually, within a certain deadline date.",
        "Subscription fees are non-refundable, except as required by law. Unused credits do not roll over and are non-refundable."
      ]
    },
    {
      id: "credits",
      icon: Badge,
      title: "Credit System",
      description: "How our credit-based features work",
      content: [
        "Access to certain features of the Service, including AI-powered analysis and content generation, operates on a credit-based system.",
        "Credits are consumed when you initiate an analysis or generate specific AI-powered content.",
        "Credits are purchased as part of your Subscription plan or as separate add-ons.",
        "Credits have no cash value and are non-transferable.",
        "Unused credits may expire at the end of a Billing Cycle or after a specified period, as detailed in your Subscription plan.",
        "We reserve the right to adjust credit consumption rates for features at any time, with reasonable notice to users."
      ]
    },
    {
      id: "content",
      icon: Shield,
      title: "Content and Data Rights",
      description: "Your content ownership and our usage rights",
      subsections: [
        {
          title: "Your Content",
          points: [
            "You retain any and all of your rights to any Content you submit, post, or display on or through the Service.",
            "By submitting Your Content, you grant us a non-exclusive, worldwide, royalty-free, sublicensable, and transferable license to use, reproduce, distribute, prepare derivative works of, display, and perform Your Content in connection with the Service.",
            "You own the AI-generated content created through your use of the Service, with certain usage rights granted to breakdown.ad for service operation and improvement."
          ]
        },
        {
          title: "Publicly Shared Content",
          points: [
            "If you choose to make an ad analysis public, the analysis report will be publicly accessible on our platform (e.g., on the /ad-library page and potentially featured on the /daily-showcase page).",
            "You are solely responsible for the content you choose to make public and must ensure it does not violate any third-party rights or applicable laws."
          ]
        },
        {
          title: "YouTube API Services",
          points: [
            "By using the Service to analyze YouTube videos, you agree to be bound by the YouTube Terms of Service and Google Privacy Policy.",
            "You are responsible for ensuring that your use of YouTube videos and the data derived from them through our Service complies with all applicable YouTube policies and terms."
          ]
        }
      ]
    },
    {
      id: "prohibited",
      icon: AlertTriangle,
      title: "Prohibited Uses",
      description: "Activities that are not allowed on our platform",
      content: [
        "You may use the Service only for lawful purposes and in accordance with these Terms.",
        "You may not use the Service in any way that violates any applicable national or international law or regulation.",
        "You may not use the Service for the purpose of exploiting, harming, or attempting to exploit or harm minors in any way.",
        "You may not transmit any advertising or promotional material, including any \"junk mail,\" \"chain letter,\" \"spam,\" or any other similar solicitation.",
        "You may not impersonate or attempt to impersonate breakdown.ad, an breakdown.ad employee, another user, or any other person or entity.",
        "You may not use the Service in any manner that could disable, overburden, damage, or impair the Service.",
        "You may not use any robot, spider, or other automatic device, process, or means to access the Service for any purpose.",
        "You may not attempt to gain unauthorized access to, interfere with, damage, or disrupt any parts of the Service."
      ]
    },
    {
      id: "intellectual",
      icon: Globe,
      title: "Intellectual Property",
      description: "Ownership of the service and content",
      content: [
        "The Service and its original content (excluding Content provided by users), features, and functionality are and will remain the exclusive property of breakdown.ad and its licensors.",
        "The Service is protected by copyright, trademark, and other laws of both the United States and foreign countries.",
        "Our trademarks and trade dress may not be used in connection with any product or service without the prior written consent of breakdown.ad."
      ]
    },
    {
      id: "termination",
      icon: Gavel,
      title: "Termination",
      description: "How accounts can be terminated",
      content: [
        "We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever.",
        "If you wish to terminate your account, you may simply discontinue using the Service.",
        "All provisions of the Terms which by their nature should survive termination shall survive termination, including, without limitation, ownership provisions, warranty disclaimers, indemnity, and limitations of liability."
      ]
    }
  ]
}

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-blue-100 rounded-full">
              <FileText className="h-10 w-10 text-blue-600" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {termsData.title}
          </h1>
          <p className="text-lg text-gray-600 mb-2">
            {termsData.subtitle}
          </p>
          <div className="flex items-center justify-center text-sm text-gray-500">
            <Calendar className="h-4 w-4 mr-2" />
            <span>Last updated: {termsData.lastUpdated}</span>
          </div>
        </div>

        {/* Terms Sections Grid */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {termsData.sections.map((section) => {
            const IconComponent = section.icon
            return (
              <Card key={section.id} className="shadow-md hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-blue-50 rounded-lg">
                      <IconComponent className="h-5 w-5 text-blue-600" />
                    </div>
                    <CardTitle className="text-xl">{section.title}</CardTitle>
                  </div>
                  <CardDescription className="text-gray-600">
                    {section.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {section.content && (
                    <div className="space-y-3">
                      {section.content.map((item, index) => (
                        <p key={index} className="text-sm text-gray-700 leading-relaxed">
                          {item}
                        </p>
                      ))}
                    </div>
                  )}
                  
                  {section.subsections && (
                    <div className="space-y-6">
                      {section.subsections.map((subsection, index) => (
                        <div key={index} className="border-l-4 border-blue-200 pl-4">
                          <h4 className="font-semibold text-gray-900 mb-2">{subsection.title}</h4>
                          <div className="space-y-2">
                            {subsection.points.map((point, pointIndex) => (
                              <p key={pointIndex} className="text-sm text-gray-700 leading-relaxed">
                                • {point}
                              </p>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Legal Disclaimers */}
        <Card className="shadow-lg mb-12">
          <CardHeader>
            <CardTitle className="text-2xl text-center text-red-700">Important Legal Disclaimers</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-red-50 p-6 rounded-lg border border-red-200">
                <h3 className="font-semibold text-red-800 mb-3">Disclaimer of Warranty</h3>
                <p className="text-sm text-red-700 leading-relaxed">
                  YOUR USE OF THE SERVICE IS AT YOUR SOLE RISK. THE SERVICE IS PROVIDED ON AN &quot;AS IS&quot; AND &quot;AS AVAILABLE&quot; BASIS. 
                  breakdown.ad does not warrant the accuracy, completeness, or reliability of any AI-generated analysis, insights, or content.
                </p>
              </div>
              <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
                <h3 className="font-semibold text-yellow-800 mb-3">Limitation of Liability</h3>
                <p className="text-sm text-yellow-700 leading-relaxed">
                  breakdown.ad shall not be liable for any indirect, incidental, special, consequential, or punitive damages, 
                  including without limitation, loss of profits, data, use, goodwill, or other intangible losses.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer Actions */}
        <div className="text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/privacy">
              <Button variant="outline" size="lg">
                Privacy Policy
              </Button>
            </Link>
            <Link href="/contact">
              <Button variant="outline" size="lg">
                Contact Us
              </Button>
            </Link>
            <Link href="/">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Back to Home
              </Button>
            </Link>
          </div>

        </div>
      </main>
    </div>
  )
}