import Navigation from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Code, Server, Key, Zap, Shield, Clock, ExternalLink } from 'lucide-react'

export default function APIReferencePage() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />

      <main className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            API Reference
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Integrate breakdown.ad&apos;s AI-powered video analysis into your applications with our comprehensive REST API.
          </p>
          <div className="flex justify-center gap-4">
            <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200">
              <Server className="h-3 w-3 mr-1" />
              REST API
            </Badge>
            <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
              <Key className="h-3 w-3 mr-1" />
              API Key Required
            </Badge>
          </div>
        </div>

        {/* Base URL */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5 text-blue-600" />
                Base URL
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-lg font-mono text-sm">
                https://api.breakdown.ad/v1
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Authentication */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-600" />
                Authentication
              </CardTitle>
              <CardDescription>
                All API requests require authentication via API key
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">API Key Header</h4>
                  <div className="bg-gray-50 p-4 rounded-lg font-mono text-sm">
                    Authorization: Bearer YOUR_API_KEY
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Getting Your API Key</h4>
                  <p className="text-gray-600 mb-4">
                    API keys are available for Pro and Enterprise users. Generate your key from your dashboard settings.
                  </p>
                  <Link href="/studio">
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      Get API Key
                      <ExternalLink className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Rate Limits */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-orange-600" />
                Rate Limits
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">100</div>
                  <div className="text-sm text-gray-600">requests/hour</div>
                  <div className="text-xs text-gray-500 mt-1">Free Tier</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">1,000</div>
                  <div className="text-sm text-gray-600">requests/hour</div>
                  <div className="text-xs text-gray-500 mt-1">Pro Tier</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">10,000</div>
                  <div className="text-sm text-gray-600">requests/hour</div>
                  <div className="text-xs text-gray-500 mt-1">Enterprise</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Endpoints */}
        <div className="space-y-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">API Endpoints</h2>

          {/* Create Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5 text-blue-600" />
                Create Analysis
              </CardTitle>
              <CardDescription>
                Submit a YouTube video URL for AI analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">POST</Badge>
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">/analyses</code>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Request Body</h4>
                  <div className="bg-gray-50 p-4 rounded-lg font-mono text-sm overflow-x-auto">
{`{
  "video_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  "analysis_type": "full", // "basic" | "full" | "premium"
  "webhook_url": "https://your-app.com/webhook" // optional
}`}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Response</h4>
                  <div className="bg-gray-50 p-4 rounded-lg font-mono text-sm overflow-x-auto">
{`{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "slug": "video-analysis-dQw4w9WgXcQ",
  "status": "processing",
  "video_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  "created_at": "2024-01-15T10:30:00Z",
  "estimated_completion": "2024-01-15T10:35:00Z"
}`}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Get Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5 text-green-600" />
                Get Analysis
              </CardTitle>
              <CardDescription>
                Retrieve analysis results by ID or slug
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">GET</Badge>
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">/analyses/{`{id}`}</code>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Response</h4>
                  <div className="bg-gray-50 p-4 rounded-lg font-mono text-sm overflow-x-auto">
{`{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "slug": "video-analysis-dQw4w9WgXcQ",
  "status": "completed",
  "video_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  "title": "Amazing Brand Commercial",
  "inferred_brand": "Amazing Brand",
  "analysis": {
    "overall_sentiment": 0.85,
    "emotions": {
      "joy": 0.7,
      "excitement": 0.6,
      "trust": 0.8
    },
    "key_themes": ["product quality", "customer satisfaction"],
    "target_audience": {
      "demographics": ["25-45", "urban", "middle-income"],
      "interests": ["technology", "lifestyle"]
    },
    "effectiveness_score": 8.2,
    "recommendations": [
      "Consider stronger call-to-action",
      "Enhance emotional appeal in final scene"
    ]
  },
  "created_at": "2024-01-15T10:30:00Z",
  "completed_at": "2024-01-15T10:34:22Z"
}`}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* List Analyses */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5 text-purple-600" />
                List Analyses
              </CardTitle>
              <CardDescription>
                Get a paginated list of your analyses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">GET</Badge>
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">/analyses</code>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Query Parameters</h4>
                  <div className="bg-gray-50 p-4 rounded-lg font-mono text-sm">
{`?limit=20&offset=0&status=completed&sort=created_at&order=desc`}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* SDKs and Libraries */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-600" />
                SDKs and Libraries
              </CardTitle>
              <CardDescription>
                Official SDKs for popular programming languages
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl mb-2">🟨</div>
                  <h4 className="font-semibold">JavaScript/Node.js</h4>
                  <p className="text-sm text-gray-600 mb-3">Official SDK for JavaScript</p>
                  <Button variant="outline" size="sm">
                    npm install @breakdown/sdk
                  </Button>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl mb-2">🐍</div>
                  <h4 className="font-semibold">Python</h4>
                  <p className="text-sm text-gray-600 mb-3">Official SDK for Python</p>
                  <Button variant="outline" size="sm">
                    pip install breakdown-ad
                  </Button>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl mb-2">🦀</div>
                  <h4 className="font-semibold">More Languages</h4>
                  <p className="text-sm text-gray-600 mb-3">Ruby, Go, PHP coming soon</p>
                  <Button variant="outline" size="sm">
                    Request SDK
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Support */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
              <CardDescription>
                Get support for API integration and troubleshooting
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/contact">
                  <Button variant="outline">
                    API Support
                  </Button>
                </Link>
                <Link href="/docs">
                  <Button variant="outline">
                    View Documentation
                  </Button>
                </Link>
                <Link href="mailto:<EMAIL>">
                  <Button variant="outline">
                    Email API Team
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}