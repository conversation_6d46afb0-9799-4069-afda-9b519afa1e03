'use client'

import React, { useState } from 'react'
import VideoPlayer from '@/components/analysis/VideoPlayer'
import CampaignAnalysis from '@/components/analysis/CampaignAnalysis'
import PerformanceScorecard from '@/components/analysis/PerformanceScorecard'
import OverallImpressionTestimonial from '@/components/analysis/OverallImpressionTestimonial'
import GeneratedContentSections from '@/components/analysis/GeneratedContentSections'
import Navigation from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { Share2, Download } from 'lucide-react'

// Import the MarketingAnalysisView from the shared component
import { MarketingAnalysisView } from '@/components/analysis/MarketingAnalysisView'
import ShareAnalysisModal from '@/components/analysis/ShareAnalysisModal'

interface FeaturedPageClientProps {
  currentAnalysis: any
  videoMetadata: any
  displayYoutubeMetadata: any
  youtubeVideoId: string | null
  videoUrl: string | null
  marketingAnalysis: any
  marketingCopy: string
  socialMediaPosts: string
  marketingScorecard: string
  seoKeywords: string
  contentSuggestions: string
  emotionTimeline: any
  enhancedScript: any
  detailedAnalysisData: any
}

const FeaturedPageClient: React.FC<FeaturedPageClientProps> = ({
  currentAnalysis,
  videoMetadata,
  displayYoutubeMetadata,
  youtubeVideoId,
  videoUrl,
  marketingAnalysis,
  marketingCopy,
  socialMediaPosts,
  marketingScorecard,
  seoKeywords,
  contentSuggestions,
  emotionTimeline,
  enhancedScript,
  detailedAnalysisData
}) => {
  const [showShareModal, setShowShareModal] = useState(false)

  // Extract badges data from analysis
  const getBadges = () => {
    const badges = []
    
    // Try to parse marketing analysis for rich data
    let marketingData: any = null
    try {
      if (marketingAnalysis && typeof marketingAnalysis === 'string') {
        let cleanData = marketingAnalysis
          .replace(/```json\s*/g, '')
          .replace(/```\s*/g, '')
          .trim()
        
        const jsonMatch = cleanData.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          marketingData = JSON.parse(jsonMatch[0])
        }
      } else if (marketingAnalysis && typeof marketingAnalysis === 'object') {
        marketingData = marketingAnalysis
      }
    } catch (error) {
      console.log('Could not parse marketing analysis for badges')
    }
    
    // Brand name - prefer from marketing analysis, fallback to current analysis
    const brandName = marketingData?.lightning_round?.brand || currentAnalysis?.inferred_brand
    if (brandName) {
      badges.push({ label: brandName, variant: 'secondary' as const })
    }
    
    // Product category - prefer from marketing analysis, fallback to current analysis
    const productCategory = marketingData?.lightning_round?.product_category || currentAnalysis?.product_category
    if (productCategory) {
      badges.push({ label: productCategory, variant: 'secondary' as const })
    }
    
    // Parent entity - from marketing analysis
    const parentEntity = marketingData?.lightning_round?.parent_entity
    if (parentEntity) {
      badges.push({ label: parentEntity, variant: 'secondary' as const })
    }
    
    // Campaign category - prefer from marketing analysis, fallback to current analysis
    const campaignCategory = marketingData?.lightning_round?.campaign_category || currentAnalysis?.campaign_category
    if (campaignCategory) {
      badges.push({ label: campaignCategory, variant: 'secondary' as const })
    }
    
    // Runtime - from marketing analysis
    const runtime = marketingData?.lightning_round?.runtime
    if (runtime) {
      badges.push({ label: runtime, variant: 'secondary' as const })
    }
    
    // Celebrity - from marketing analysis AI extraction
    const celebrity = marketingData?.lightning_round?.celebrity
    if (celebrity && celebrity.trim() !== '' && celebrity.toLowerCase() !== 'none' && celebrity.toLowerCase() !== 'n/a') {
      // Handle multiple celebrities separated by commas or "and"
      const celebrityList = celebrity.split(/,|\band\b/i).map((name: string) => name.trim()).filter((name: string) => name.length > 0)
      
      celebrityList.forEach((name: string) => {
        badges.push({ label: name, variant: 'secondary' as const })
      })
    }
    
    return badges
  }

  // Share functionality
  const getShareUrl = () => {
    if (!currentAnalysis) return ''
    return `${window.location.origin}/featured`
  }

  const getShareText = () => {
    if (!currentAnalysis) return ''
    const adTitle = currentAnalysis.title || `${currentAnalysis.inferred_brand || 'Brand'} Ad`
    return `Check out this AI analysis of "${adTitle}" on`
  }

  const handleShare = () => {
    setShowShareModal(true)
  }

  const shareHandlers = {
    shareToTwitter: () => {
      const text = encodeURIComponent(getShareText())
      const url = encodeURIComponent(getShareUrl())
      window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank')
    },
    shareToLinkedIn: () => {
      const url = encodeURIComponent(getShareUrl())
      const title = encodeURIComponent(`${currentAnalysis?.inferred_brand || 'Brand'} Ad Analysis - AdBreakdown`)
      window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`, '_blank')
    },
    shareToWhatsApp: () => {
      const text = encodeURIComponent(`${getShareText()} ${getShareUrl()}`)
      window.open(`https://wa.me/?text=${text}`, '_blank')
    },
    shareToInstagram: () => {
      shareHandlers.copyToClipboard()
      alert('Link copied! Open Instagram and paste the link in your story or post.')
    },
    copyToClipboard: async () => {
      try {
        await navigator.clipboard.writeText(getShareUrl())
        alert('Link copied to clipboard!')
      } catch (error) {
        console.error('Clipboard error:', error)
        prompt('Copy this link:', getShareUrl())
      }
    }
  }

  // PDF Download functionality
  const handleDownloadPDF = async () => {
    try {
      // Show loading state
      const button = document.querySelector('[data-pdf-download]') as HTMLButtonElement
      if (button) {
        button.disabled = true
        button.textContent = 'Generating PDF...'
      }

      // Import html2pdf dynamically
      const html2pdf = (await import('html2pdf.js')).default
      
      // Get the main content but exclude navigation and buttons
      const mainContent = document.querySelector('main')
      if (!mainContent) {
        throw new Error('Main content not found')
      }
      
      const clonedContent = mainContent.cloneNode(true) as HTMLElement
      
      // Remove navigation, buttons, and other non-essential elements
      const elementsToRemove = [
        'nav', 'button', '.hidden', '[data-pdf-download]',
        'script', 'style', '.no-pdf'
      ]
      
      elementsToRemove.forEach(selector => {
        const elements = clonedContent.querySelectorAll(selector)
        elements.forEach(el => el.remove())
      })
      
      // Create a formatted PDF container that preserves desktop layout
      const pdfContainer = document.createElement('div')
      pdfContainer.style.width = '1200px' // Keep desktop width
      pdfContainer.style.maxWidth = '1200px'
      pdfContainer.style.margin = '0 auto'
      pdfContainer.style.padding = '40px'
      pdfContainer.style.fontFamily = 'Arial, sans-serif'
      pdfContainer.style.fontSize = '14px'
      pdfContainer.style.lineHeight = '1.5'
      pdfContainer.style.color = '#333'
      pdfContainer.style.backgroundColor = '#fff'
      
      // Keep the desktop layout intact
      clonedContent.style.maxWidth = '100%'
      clonedContent.style.padding = '0'
      clonedContent.style.margin = '0'
      
      // Style headings with standard CSS properties
      const headings = clonedContent.querySelectorAll('h1, h2, h3, h4, h5, h6')
      headings.forEach(heading => {
        const h = heading as HTMLElement
        h.style.color = '#333'
        h.style.marginBottom = '0.5em'
        h.style.marginTop = '1em'
      })
      
      // Style paragraphs
      const paragraphs = clonedContent.querySelectorAll('p')
      paragraphs.forEach(p => {
        const para = p as HTMLElement
        para.style.marginBottom = '0.5em'
      })
      
      // Style cards and containers
      const cards = clonedContent.querySelectorAll('.card, [class*="card"], .border, .rounded')
      cards.forEach(card => {
        const c = card as HTMLElement
        c.style.border = '1px solid #ddd'
        c.style.padding = '1em'
        c.style.marginBottom = '1em'
        c.style.borderRadius = '4px'
      })
      
      pdfContainer.appendChild(clonedContent)
      
      // Append to body temporarily
      document.body.appendChild(pdfContainer)
      
      // Configure PDF options to preserve desktop layout
      const options = {
        margin: 0.5,
        filename: `${currentAnalysis?.title || 'featured-analysis'}.pdf`,
        image: { type: 'jpeg' as const, quality: 0.9 },
        html2canvas: { 
          scale: 1,
          useCORS: true,
          width: 1200, // Desktop width
          windowWidth: 1200
        },
        jsPDF: { 
          unit: 'pt' as const, 
          format: [864, 1152] as [number, number], // Custom format to fit desktop layout (1200px * 0.72 = 864pt)
          orientation: 'portrait' as const,
          compress: true
        }
      }

      // Generate and download PDF
      await html2pdf().set(options).from(pdfContainer).save()
      
      // Remove temporary container
      document.body.removeChild(pdfContainer)
      
      // Reset button state
      if (button) {
        button.disabled = false
        button.textContent = 'Download PDF'
      }
    } catch (error) {
      console.error('PDF generation error:', error)
      alert('Failed to generate PDF. Please try again.')
      
      // Reset button state
      const button = document.querySelector('[data-pdf-download]') as HTMLButtonElement
      if (button) {
        button.disabled = false
        button.textContent = 'Download PDF'
      }
    }
  }

  if (!currentAnalysis) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-[calc(100vh-64px)]">
          <Card className="mb-12 text-center p-8">
            <h3 className="text-xl font-bold text-gray-900 mb-4">No Featured Analysis Available</h3>
            <p className="text-gray-600 mb-6">
              There are currently no analyses featured for daily showcase.
              Check back later or explore our public analyses.
            </p>
            <Link href="/ad-library">
              <Button className="bg-blue-600 hover:bg-blue-700">
                Explore All Analyses
              </Button>
            </Link>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <main className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Featured Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-between mb-4">
            <div></div>
            <h1 className="text-4xl font-bold text-gray-900">Featured Analysis</h1>
            <div className="flex items-center gap-2">
             
            </div>
          </div>
          <p className="text-md text-gray-600 max-w-3xl mx-auto mb-8">
            Hand-picked popular ad with complete insights. Updated periodically with fresh content.
          </p>
        </div>

        {console.log('Rendering featured video/campaign analysis components. currentAnalysis:', currentAnalysis, 'videoMetadata:', videoMetadata, 'youtubeMetadata:', displayYoutubeMetadata, 'videoUrl:', videoUrl, 'youtubeVideoId:', youtubeVideoId)}
        
        {/* Video Title and Description */}
        {currentAnalysis && (videoUrl || youtubeVideoId) && (
          <div className="mb-6">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl font-semibold text-gray-900 truncate overflow-hidden">
                  {displayYoutubeMetadata?.title || videoMetadata.title}
                </h1>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleShare}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Share2 className="h-4 w-4" />
                  Share
                </Button>
                <Button
                  onClick={handleDownloadPDF}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                  data-pdf-download
                >
                  <Download className="h-4 w-4" />
                  Download PDF
                </Button>
              </div>
            </div>
            {/* Badges - Full width below title and buttons */}
            <div className="flex flex-wrap gap-2 mt-4">
              {getBadges().map((badge, index) => (
                <Badge key={index} variant={badge.variant} className="bg-blue-100 text-blue-800 border-blue-200">
                  {badge.label}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Two Column Layout for Video and Campaign Analysis */}
        {currentAnalysis && (videoUrl || youtubeVideoId) && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8 lg:items-stretch">
            <div className="order-1 lg:order-1 lg:col-span-2 flex">
              <VideoPlayer
                youtubeVideoId={youtubeVideoId}
                videoMetadata={videoMetadata}
              />
            </div>
            <div className="order-2 lg:order-2 flex">
              <CampaignAnalysis
                youtubeMetadata={displayYoutubeMetadata}
                videoMetadata={videoMetadata}
              />
            </div>
          </div>
        )}

        {/* Fallback: Show title and analysis even without video */}
        {currentAnalysis && !(videoUrl || youtubeVideoId) && (
          <div className="mb-6">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl font-semibold text-gray-900 truncate overflow-hidden">
                  {videoMetadata.title}
                </h1>
                <p className="mt-2 text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-lg">
                  📺 Video content not available - Showing analysis insights only
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleShare}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Share2 className="h-4 w-4" />
                  Share
                </Button>
                <Button
                  onClick={handleDownloadPDF}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                  data-pdf-download
                >
                  <Download className="h-4 w-4" />
                  Download PDF
                </Button>
              </div>
            </div>
            {/* Badges - Full width below title and buttons */}
            <div className="flex flex-wrap gap-2 mt-4">
              {getBadges().map((badge, index) => (
                <Badge key={index} variant={badge.variant} className="bg-blue-100 text-blue-800 border-blue-200">
                  {badge.label}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Marketing Analysis View */}
        {(currentAnalysis?.status === 'completed' || currentAnalysis?.status === 'generated') && (
          <MarketingAnalysisView 
            analysis={currentAnalysis}
            marketingAnalysis={marketingAnalysis}
            videoMetadata={videoMetadata}
            youtubeMetadata={displayYoutubeMetadata}
            marketingCopy={marketingCopy}
            socialMediaPosts={socialMediaPosts}
            marketingScorecard={marketingScorecard}
            seoKeywords={seoKeywords}
            contentSuggestions={contentSuggestions}
            emotionTimeline={emotionTimeline}
            enhancedScript={enhancedScript}
            detailedAnalysisData={detailedAnalysisData}
          />
        )}
      </main>

      {/* Share Modal */}
      <ShareAnalysisModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        analysis={currentAnalysis}
        shareHandlers={shareHandlers}
      />
    </div>
  )
}

export default FeaturedPageClient