'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Building2, ArrowLeft, ArrowRight, Save, X, Plus, Sparkles, Globe } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface Demographics {
  age_range: string
  gender: string
  income_level: string
  education_level: string
  location: string
  occupation: string
  family_status: string
}

interface Persona {
  id: string
  name: string
  age: number
  occupation: string
  location: string
  bio: string
  goals: string[]
  pain_points: string[]
  preferred_channels: string[]
}

interface BrandFormData {
  brand_name: string
  tagline: string
  logo_url: string
  website_url: string
  industry_category: string
  company_size: string
  founded_year: number | null
  positioning_statement: string
  brand_values: string[]
  mission_statement: string
  vision_statement: string
  primary_colors: string[]
  secondary_colors: string[]
  font_primary: string
  font_secondary: string
  voice_attributes: string[]
  tone_keywords: string[]
  brand_personality: string[]
  communication_style: string
  primary_demographics: Demographics
  secondary_demographics: Demographics
  psychographics: string[]
  customer_personas: Persona[]
  direct_competitors: string[]
  indirect_competitors: string[]
  competitive_advantages: string[]
}

const INDUSTRY_CATEGORIES = [
  'Technology', 'Healthcare', 'Finance', 'Retail', 'Education', 'Entertainment',
  'Food & Beverage', 'Fashion', 'Automotive', 'Real Estate', 'Travel',
  'Manufacturing', 'Consulting', 'Non-profit', 'Government', 'Other'
]

const COMPANY_SIZES = [
  'Startup (1-10 employees)',
  'Small (11-50 employees)',
  'Medium (51-200 employees)',
  'Large (201-1000 employees)',
  'Enterprise (1000+ employees)'
]

const COMMUNICATION_STYLES = [
  'Professional', 'Casual', 'Friendly', 'Authoritative', 'Playful',
  'Inspirational', 'Educational', 'Conversational'
]

const AGE_RANGES = [
  '18-24', '25-34', '35-44', '45-54', '55-64', '65+'
]

const GENDER_OPTIONS = [
  'Male', 'Female', 'Non-binary', 'All genders', 'Prefer not to specify'
]

const INCOME_LEVELS = [
  'Under $25,000', '$25,000-$49,999', '$50,000-$74,999', 
  '$75,000-$99,999', '$100,000-$149,999', '$150,000+'
]

const EDUCATION_LEVELS = [
  'High School', 'Some College', 'Bachelor\'s Degree', 
  'Master\'s Degree', 'Doctorate', 'Trade/Technical School'
]

const FAMILY_STATUS_OPTIONS = [
  'Single', 'Married', 'Married with children', 'Single parent', 
  'Empty nester', 'Retired'
]

const PREFERRED_CHANNELS = [
  'Social Media', 'Email', 'Search Engines', 'Television', 'Radio',
  'Print Media', 'Podcasts', 'YouTube', 'Influencer Marketing',
  'Word of Mouth', 'Direct Mail', 'Events/Conferences'
]

export default function CreateBrandPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [aiExtracting, setAiExtracting] = useState(false)
  const [websiteUrl, setWebsiteUrl] = useState('')
  
  const [formData, setFormData] = useState<BrandFormData>({
    brand_name: '',
    tagline: '',
    logo_url: '',
    website_url: '',
    industry_category: '',
    company_size: '',
    founded_year: null,
    positioning_statement: '',
    brand_values: [],
    mission_statement: '',
    vision_statement: '',
    primary_colors: [],
    secondary_colors: [],
    font_primary: '',
    font_secondary: '',
    voice_attributes: [],
    tone_keywords: [],
    brand_personality: [],
    communication_style: '',
    primary_demographics: {
      age_range: '',
      gender: '',
      income_level: '',
      education_level: '',
      location: '',
      occupation: '',
      family_status: ''
    },
    secondary_demographics: {
      age_range: '',
      gender: '',
      income_level: '',
      education_level: '',
      location: '',
      occupation: '',
      family_status: ''
    },
    psychographics: [],
    customer_personas: [],
    direct_competitors: [],
    indirect_competitors: [],
    competitive_advantages: []
  })

  const steps = [
    {
      title: 'Basic Information',
      description: 'Tell us about your brand'
    },
    {
      title: 'Brand Identity',
      description: 'Define your brand\'s core identity'
    },
    {
      title: 'Visual Identity',
      description: 'Colors, fonts, and visual elements'
    },
    {
      title: 'Brand Voice',
      description: 'How your brand communicates'
    },
    {
      title: 'Target Audience',
      description: 'Who are you speaking to?'
    },
    {
      title: 'Competition',
      description: 'Your competitive landscape'
    }
  ]

  const handleInputChange = (field: keyof BrandFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleArrayAdd = (field: keyof BrandFormData, value: string) => {
    if (value.trim()) {
      setFormData(prev => ({
        ...prev,
        [field]: [...(prev[field] as string[]), value.trim()]
      }))
    }
  }

  const handleArrayRemove = (field: keyof BrandFormData, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field] as string[]).filter((_, i) => i !== index)
    }))
  }

  const handleDemographicsChange = (type: 'primary_demographics' | 'secondary_demographics', field: keyof Demographics, value: string) => {
    setFormData(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        [field]: value
      }
    }))
  }

  const handlePersonaAdd = () => {
    const newPersona: Persona = {
      id: Date.now().toString(),
      name: '',
      age: 30,
      occupation: '',
      location: '',
      bio: '',
      goals: [],
      pain_points: [],
      preferred_channels: []
    }
    setFormData(prev => ({
      ...prev,
      customer_personas: [...prev.customer_personas, newPersona]
    }))
  }

  const handlePersonaRemove = (personaId: string) => {
    setFormData(prev => ({
      ...prev,
      customer_personas: prev.customer_personas.filter(p => p.id !== personaId)
    }))
  }

  const handlePersonaChange = (personaId: string, field: keyof Persona, value: any) => {
    setFormData(prev => ({
      ...prev,
      customer_personas: prev.customer_personas.map(persona =>
        persona.id === personaId ? { ...persona, [field]: value } : persona
      )
    }))
  }

  const handlePersonaArrayAdd = (personaId: string, field: 'goals' | 'pain_points' | 'preferred_channels', value: string) => {
    if (value.trim()) {
      setFormData(prev => ({
        ...prev,
        customer_personas: prev.customer_personas.map(persona =>
          persona.id === personaId
            ? { ...persona, [field]: [...persona[field], value.trim()] }
            : persona
        )
      }))
    }
  }

  const handlePersonaArrayRemove = (personaId: string, field: 'goals' | 'pain_points' | 'preferred_channels', index: number) => {
    setFormData(prev => ({
      ...prev,
      customer_personas: prev.customer_personas.map(persona =>
        persona.id === personaId
          ? { ...persona, [field]: persona[field].filter((_, i) => i !== index) }
          : persona
      )
    }))
  }

  const calculateProgress = () => {
    const totalFields = 28 // Updated total
    let completedFields = 0

    // Count completed fields
    if (formData.brand_name) completedFields++
    if (formData.industry_category) completedFields++
    if (formData.tagline) completedFields++
    if (formData.logo_url) completedFields++
    if (formData.website_url) completedFields++
    if (formData.company_size) completedFields++
    if (formData.founded_year) completedFields++
    if (formData.positioning_statement) completedFields++
    if (formData.mission_statement) completedFields++
    if (formData.vision_statement) completedFields++
    if (formData.font_primary) completedFields++
    if (formData.font_secondary) completedFields++
    if (formData.communication_style) completedFields++
    
    // Array fields
    if (formData.brand_values.length > 0) completedFields++
    if (formData.primary_colors.length > 0) completedFields++
    if (formData.secondary_colors.length > 0) completedFields++
    if (formData.voice_attributes.length > 0) completedFields++
    if (formData.tone_keywords.length > 0) completedFields++
    if (formData.brand_personality.length > 0) completedFields++
    if (formData.psychographics.length > 0) completedFields++
    if (formData.direct_competitors.length > 0) completedFields++
    if (formData.indirect_competitors.length > 0) completedFields++
    if (formData.competitive_advantages.length > 0) completedFields++
    
    // Demographics fields
    const primaryDemoComplete = Object.values(formData.primary_demographics).some(value => value && value.trim() !== '')
    const secondaryDemoComplete = Object.values(formData.secondary_demographics).some(value => value && value.trim() !== '')
    if (primaryDemoComplete) completedFields++
    if (secondaryDemoComplete) completedFields++
    
    // Personas
    if (formData.customer_personas.length > 0) completedFields++

    return Math.round((completedFields / totalFields) * 100)
  }

  const isStepValid = (step: number) => {
    switch (step) {
      case 0:
        return formData.brand_name && formData.industry_category
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
        return true // Optional steps
      default:
        return true
    }
  }

  const handleSubmit = async () => {
    if (!formData.brand_name || !formData.industry_category) {
      setError('Brand name and industry category are required')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/brands', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create brand')
      }

      const data = await response.json()
      router.push(`/studio/brand/${data.brand.id}`)
    } catch (err: any) {
      setError(err.message || 'Failed to create brand')
    } finally {
      setLoading(false)
    }
  }

  const handleAiExtract = async () => {
    if (!websiteUrl.trim()) {
      setError('Please enter a website URL')
      return
    }

    setAiExtracting(true)
    setError('')

    try {
      const response = await fetch('/api/brands/extract', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ website_url: websiteUrl })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to extract brand profile')
      }

      const data = await response.json()
      
      // Auto-fill form with extracted data
      setFormData(prev => ({
        ...prev,
        ...data.brandProfile,
        website_url: websiteUrl // Ensure we keep the original URL
      }))

      // Automatically move to next step after successful extraction
      setCurrentStep(1)
      
    } catch (err: any) {
      setError(err.message || 'Failed to extract brand profile')
    } finally {
      setAiExtracting(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            {/* AI Brand Extraction */}
            <div className="p-6 bg-gradient-to-r from-rose-50 to-pink-50 rounded-lg border border-rose-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-rose-100 rounded-lg">
                  <Sparkles className="h-5 w-5 text-rose-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Extract Brand Profile with AI</h3>
                  <p className="text-sm text-gray-600">Let AI analyze your website and automatically create your brand profile</p>
                </div>
              </div>
              
              <div className="flex gap-3">
                <div className="flex-1">
                  <Input
                    value={websiteUrl}
                    onChange={(e) => setWebsiteUrl(e.target.value)}
                    placeholder="https://your-website.com"
                    disabled={aiExtracting}
                  />
                </div>
                <Button
                  onClick={handleAiExtract}
                  disabled={aiExtracting || !websiteUrl.trim()}
                  className="bg-rose-600 hover:bg-rose-700"
                >
                  {aiExtracting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Extract with AI
                    </>
                  )}
                </Button>
              </div>
              
              <div className="mt-3 text-xs text-gray-500">
                AI will analyze your website content, branding, and public information to create a comprehensive profile
              </div>
            </div>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-3 text-gray-500">or create manually</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand_name">Brand Name *</Label>
              <Input
                id="brand_name"
                value={formData.brand_name}
                onChange={(e) => handleInputChange('brand_name', e.target.value)}
                placeholder="Enter your brand name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="tagline">Tagline</Label>
              <Input
                id="tagline"
                value={formData.tagline}
                onChange={(e) => handleInputChange('tagline', e.target.value)}
                placeholder="Your brand's tagline or slogan"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="industry_category">Industry Category *</Label>
              <Select value={formData.industry_category} onValueChange={(value) => handleInputChange('industry_category', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select your industry" />
                </SelectTrigger>
                <SelectContent>
                  {INDUSTRY_CATEGORIES.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="website_url">Website URL</Label>
              <Input
                id="website_url"
                type="url"
                value={formData.website_url}
                onChange={(e) => handleInputChange('website_url', e.target.value)}
                placeholder="https://your-website.com"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company_size">Company Size</Label>
                <Select value={formData.company_size} onValueChange={(value) => handleInputChange('company_size', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select company size" />
                  </SelectTrigger>
                  <SelectContent>
                    {COMPANY_SIZES.map((size) => (
                      <SelectItem key={size} value={size}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="founded_year">Founded Year</Label>
                <Input
                  id="founded_year"
                  type="number"
                  min="1800"
                  max={new Date().getFullYear()}
                  value={formData.founded_year || ''}
                  onChange={(e) => handleInputChange('founded_year', e.target.value ? parseInt(e.target.value) : null)}
                  placeholder="2020"
                />
              </div>
            </div>
          </div>
        )

      case 1:
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="positioning_statement">Brand Positioning Statement</Label>
              <Textarea
                id="positioning_statement"
                value={formData.positioning_statement}
                onChange={(e) => handleInputChange('positioning_statement', e.target.value)}
                placeholder="How does your brand position itself in the market?"
                rows={3}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="mission_statement">Mission Statement</Label>
              <Textarea
                id="mission_statement"
                value={formData.mission_statement}
                onChange={(e) => handleInputChange('mission_statement', e.target.value)}
                placeholder="What is your brand's mission?"
                rows={3}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="vision_statement">Vision Statement</Label>
              <Textarea
                id="vision_statement"
                value={formData.vision_statement}
                onChange={(e) => handleInputChange('vision_statement', e.target.value)}
                placeholder="What is your brand's vision for the future?"
                rows={3}
              />
            </div>
            
            <ArrayInput
              label="Brand Values"
              values={formData.brand_values}
              onAdd={(value) => handleArrayAdd('brand_values', value)}
              onRemove={(index) => handleArrayRemove('brand_values', index)}
              placeholder="Add a brand value"
            />
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="logo_url">Logo URL</Label>
              <Input
                id="logo_url"
                type="url"
                value={formData.logo_url}
                onChange={(e) => handleInputChange('logo_url', e.target.value)}
                placeholder="https://example.com/logo.png"
              />
            </div>
            
            <ArrayInput
              label="Primary Colors"
              values={formData.primary_colors}
              onAdd={(value) => handleArrayAdd('primary_colors', value)}
              onRemove={(index) => handleArrayRemove('primary_colors', index)}
              placeholder="Add a primary color (e.g., #FF0000)"
            />
            
            <ArrayInput
              label="Secondary Colors"
              values={formData.secondary_colors}
              onAdd={(value) => handleArrayAdd('secondary_colors', value)}
              onRemove={(index) => handleArrayRemove('secondary_colors', index)}
              placeholder="Add a secondary color (e.g., #00FF00)"
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="font_primary">Primary Font</Label>
                <Input
                  id="font_primary"
                  value={formData.font_primary}
                  onChange={(e) => handleInputChange('font_primary', e.target.value)}
                  placeholder="Arial, Helvetica, etc."
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="font_secondary">Secondary Font</Label>
                <Input
                  id="font_secondary"
                  value={formData.font_secondary}
                  onChange={(e) => handleInputChange('font_secondary', e.target.value)}
                  placeholder="Times New Roman, etc."
                />
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="communication_style">Communication Style</Label>
              <Select value={formData.communication_style} onValueChange={(value) => handleInputChange('communication_style', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select communication style" />
                </SelectTrigger>
                <SelectContent>
                  {COMMUNICATION_STYLES.map((style) => (
                    <SelectItem key={style} value={style}>
                      {style}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <ArrayInput
              label="Voice Attributes"
              values={formData.voice_attributes}
              onAdd={(value) => handleArrayAdd('voice_attributes', value)}
              onRemove={(index) => handleArrayRemove('voice_attributes', index)}
              placeholder="Add a voice attribute (e.g., confident, approachable)"
            />
            
            <ArrayInput
              label="Tone Keywords"
              values={formData.tone_keywords}
              onAdd={(value) => handleArrayAdd('tone_keywords', value)}
              onRemove={(index) => handleArrayRemove('tone_keywords', index)}
              placeholder="Add tone keywords (e.g., warm, professional)"
            />
            
            <ArrayInput
              label="Brand Personality"
              values={formData.brand_personality}
              onAdd={(value) => handleArrayAdd('brand_personality', value)}
              onRemove={(index) => handleArrayRemove('brand_personality', index)}
              placeholder="Add personality trait (e.g., innovative, reliable)"
            />
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            {/* Primary Demographics */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Primary Demographics</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Age Range</Label>
                  <Select 
                    value={formData.primary_demographics.age_range} 
                    onValueChange={(value) => handleDemographicsChange('primary_demographics', 'age_range', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select age range" />
                    </SelectTrigger>
                    <SelectContent>
                      {AGE_RANGES.map((range) => (
                        <SelectItem key={range} value={range}>{range}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Gender</Label>
                  <Select 
                    value={formData.primary_demographics.gender} 
                    onValueChange={(value) => handleDemographicsChange('primary_demographics', 'gender', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      {GENDER_OPTIONS.map((option) => (
                        <SelectItem key={option} value={option}>{option}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Income Level</Label>
                  <Select 
                    value={formData.primary_demographics.income_level} 
                    onValueChange={(value) => handleDemographicsChange('primary_demographics', 'income_level', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select income level" />
                    </SelectTrigger>
                    <SelectContent>
                      {INCOME_LEVELS.map((level) => (
                        <SelectItem key={level} value={level}>{level}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Education Level</Label>
                  <Select 
                    value={formData.primary_demographics.education_level} 
                    onValueChange={(value) => handleDemographicsChange('primary_demographics', 'education_level', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select education level" />
                    </SelectTrigger>
                    <SelectContent>
                      {EDUCATION_LEVELS.map((level) => (
                        <SelectItem key={level} value={level}>{level}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Location</Label>
                  <Input
                    value={formData.primary_demographics.location}
                    onChange={(e) => handleDemographicsChange('primary_demographics', 'location', e.target.value)}
                    placeholder="e.g., United States, Urban areas, etc."
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Occupation</Label>
                  <Input
                    value={formData.primary_demographics.occupation}
                    onChange={(e) => handleDemographicsChange('primary_demographics', 'occupation', e.target.value)}
                    placeholder="e.g., Professional, Student, etc."
                  />
                </div>
                
                <div className="space-y-2 md:col-span-2">
                  <Label>Family Status</Label>
                  <Select 
                    value={formData.primary_demographics.family_status} 
                    onValueChange={(value) => handleDemographicsChange('primary_demographics', 'family_status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select family status" />
                    </SelectTrigger>
                    <SelectContent>
                      {FAMILY_STATUS_OPTIONS.map((status) => (
                        <SelectItem key={status} value={status}>{status}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Secondary Demographics */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Secondary Demographics (Optional)</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Age Range</Label>
                  <Select 
                    value={formData.secondary_demographics.age_range} 
                    onValueChange={(value) => handleDemographicsChange('secondary_demographics', 'age_range', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select age range" />
                    </SelectTrigger>
                    <SelectContent>
                      {AGE_RANGES.map((range) => (
                        <SelectItem key={range} value={range}>{range}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Gender</Label>
                  <Select 
                    value={formData.secondary_demographics.gender} 
                    onValueChange={(value) => handleDemographicsChange('secondary_demographics', 'gender', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      {GENDER_OPTIONS.map((option) => (
                        <SelectItem key={option} value={option}>{option}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Income Level</Label>
                  <Select 
                    value={formData.secondary_demographics.income_level} 
                    onValueChange={(value) => handleDemographicsChange('secondary_demographics', 'income_level', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select income level" />
                    </SelectTrigger>
                    <SelectContent>
                      {INCOME_LEVELS.map((level) => (
                        <SelectItem key={level} value={level}>{level}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Education Level</Label>
                  <Select 
                    value={formData.secondary_demographics.education_level} 
                    onValueChange={(value) => handleDemographicsChange('secondary_demographics', 'education_level', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select education level" />
                    </SelectTrigger>
                    <SelectContent>
                      {EDUCATION_LEVELS.map((level) => (
                        <SelectItem key={level} value={level}>{level}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Location</Label>
                  <Input
                    value={formData.secondary_demographics.location}
                    onChange={(e) => handleDemographicsChange('secondary_demographics', 'location', e.target.value)}
                    placeholder="e.g., International, Rural areas, etc."
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Occupation</Label>
                  <Input
                    value={formData.secondary_demographics.occupation}
                    onChange={(e) => handleDemographicsChange('secondary_demographics', 'occupation', e.target.value)}
                    placeholder="e.g., Manager, Entrepreneur, etc."
                  />
                </div>
                
                <div className="space-y-2 md:col-span-2">
                  <Label>Family Status</Label>
                  <Select 
                    value={formData.secondary_demographics.family_status} 
                    onValueChange={(value) => handleDemographicsChange('secondary_demographics', 'family_status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select family status" />
                    </SelectTrigger>
                    <SelectContent>
                      {FAMILY_STATUS_OPTIONS.map((status) => (
                        <SelectItem key={status} value={status}>{status}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Psychographics */}
            <ArrayInput
              label="Psychographics"
              values={formData.psychographics}
              onAdd={(value) => handleArrayAdd('psychographics', value)}
              onRemove={(index) => handleArrayRemove('psychographics', index)}
              placeholder="Add psychographic trait (e.g., tech-savvy, environmentally conscious)"
            />

            {/* Customer Personas */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Customer Personas</h3>
                <Button type="button" variant="outline" onClick={handlePersonaAdd}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Persona
                </Button>
              </div>
              
              {formData.customer_personas.map((persona, index) => (
                <PersonaInput
                  key={persona.id}
                  persona={persona}
                  index={index}
                  onChange={handlePersonaChange}
                  onArrayAdd={handlePersonaArrayAdd}
                  onArrayRemove={handlePersonaArrayRemove}
                  onRemove={handlePersonaRemove}
                />
              ))}
              
              {formData.customer_personas.length === 0 && (
                <div className="text-center p-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <p className="text-gray-600 mb-2">No personas created yet</p>
                  <p className="text-sm text-gray-500">Add customer personas to better understand your target audience</p>
                </div>
              )}
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <ArrayInput
              label="Direct Competitors"
              values={formData.direct_competitors}
              onAdd={(value) => handleArrayAdd('direct_competitors', value)}
              onRemove={(index) => handleArrayRemove('direct_competitors', index)}
              placeholder="Add direct competitor"
            />
            
            <ArrayInput
              label="Indirect Competitors"
              values={formData.indirect_competitors}
              onAdd={(value) => handleArrayAdd('indirect_competitors', value)}
              onRemove={(index) => handleArrayRemove('indirect_competitors', index)}
              placeholder="Add indirect competitor"
            />
            
            <ArrayInput
              label="Competitive Advantages"
              values={formData.competitive_advantages}
              onAdd={(value) => handleArrayAdd('competitive_advantages', value)}
              onRemove={(index) => handleArrayRemove('competitive_advantages', index)}
              placeholder="Add competitive advantage"
            />
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="flex flex-1 flex-col gap-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => router.back()} className="p-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Brand Profile</h1>
          <p className="text-gray-600">Build a comprehensive brand profile to enhance your ad analysis</p>
        </div>
      </div>

      {/* Progress */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Building2 className="h-5 w-5 text-rose-600" />
              <span className="font-medium">
                Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
              </span>
            </div>
            <Badge variant="secondary">
              {calculateProgress()}% Complete
            </Badge>
          </div>
          
          <Progress value={(currentStep / (steps.length - 1)) * 100} className="mb-4" />
          
          <p className="text-sm text-gray-600">{steps[currentStep].description}</p>
        </CardContent>
      </Card>

      {/* Form Content */}
      <Card>
        <CardContent className="p-6">
          {renderStepContent()}
          
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
          disabled={currentStep === 0}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <div className="flex gap-2">
          {currentStep === steps.length - 1 ? (
            <Button
              onClick={handleSubmit}
              disabled={loading || !isStepValid(currentStep)}
              className="bg-rose-600 hover:bg-rose-700"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Brand
                </>
              )}
            </Button>
          ) : (
            <Button
              onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}
              disabled={!isStepValid(currentStep)}
              className="bg-rose-600 hover:bg-rose-700"
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

interface ArrayInputProps {
  label: string
  values: string[]
  onAdd: (value: string) => void
  onRemove: (index: number) => void
  placeholder: string
}

function ArrayInput({ label, values, onAdd, onRemove, placeholder }: ArrayInputProps) {
  const [inputValue, setInputValue] = useState('')

  const handleAdd = () => {
    if (inputValue.trim()) {
      onAdd(inputValue)
      setInputValue('')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAdd()
    }
  }

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <div className="flex gap-2">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          className="flex-1"
        />
        <Button type="button" variant="outline" onClick={handleAdd}>
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      {values.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {values.map((value, index) => (
            <Badge key={index} variant="secondary" className="flex items-center gap-1">
              {value}
              <button
                onClick={() => onRemove(index)}
                className="ml-1 hover:text-red-600"
                type="button"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}

interface PersonaInputProps {
  persona: Persona
  index: number
  onChange: (personaId: string, field: keyof Persona, value: any) => void
  onArrayAdd: (personaId: string, field: 'goals' | 'pain_points' | 'preferred_channels', value: string) => void
  onArrayRemove: (personaId: string, field: 'goals' | 'pain_points' | 'preferred_channels', index: number) => void
  onRemove: (personaId: string) => void
}

function PersonaInput({ persona, index, onChange, onArrayAdd, onArrayRemove, onRemove }: PersonaInputProps) {
  return (
    <Card className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h4 className="font-medium text-gray-900">Persona {index + 1}</h4>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => onRemove(persona.id)}
          className="text-red-600 hover:text-red-700"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label>Name</Label>
            <Input
              value={persona.name}
              onChange={(e) => onChange(persona.id, 'name', e.target.value)}
              placeholder="e.g., Sarah Johnson"
            />
          </div>
          
          <div className="space-y-2">
            <Label>Age</Label>
            <Input
              type="number"
              min="18"
              max="100"
              value={persona.age}
              onChange={(e) => onChange(persona.id, 'age', parseInt(e.target.value) || 30)}
            />
          </div>
          
          <div className="space-y-2">
            <Label>Location</Label>
            <Input
              value={persona.location}
              onChange={(e) => onChange(persona.id, 'location', e.target.value)}
              placeholder="e.g., New York, NY"
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <Label>Occupation</Label>
          <Input
            value={persona.occupation}
            onChange={(e) => onChange(persona.id, 'occupation', e.target.value)}
            placeholder="e.g., Marketing Manager"
          />
        </div>
        
        <div className="space-y-2">
          <Label>Bio</Label>
          <Textarea
            value={persona.bio}
            onChange={(e) => onChange(persona.id, 'bio', e.target.value)}
            placeholder="Brief description of this persona..."
            rows={3}
          />
        </div>
        
        <PersonaArrayInput
          label="Goals"
          values={persona.goals}
          onAdd={(value) => onArrayAdd(persona.id, 'goals', value)}
          onRemove={(index) => onArrayRemove(persona.id, 'goals', index)}
          placeholder="Add a goal"
        />
        
        <PersonaArrayInput
          label="Pain Points"
          values={persona.pain_points}
          onAdd={(value) => onArrayAdd(persona.id, 'pain_points', value)}
          onRemove={(index) => onArrayRemove(persona.id, 'pain_points', index)}
          placeholder="Add a pain point"
        />
        
        <div className="space-y-2">
          <Label>Preferred Channels</Label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {PREFERRED_CHANNELS.map((channel) => (
              <label key={channel} className="flex items-center space-x-2 text-sm">
                <input
                  type="checkbox"
                  checked={persona.preferred_channels.includes(channel)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      onArrayAdd(persona.id, 'preferred_channels', channel)
                    } else {
                      const index = persona.preferred_channels.indexOf(channel)
                      if (index > -1) {
                        onArrayRemove(persona.id, 'preferred_channels', index)
                      }
                    }
                  }}
                  className="rounded border-gray-300"
                />
                <span>{channel}</span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </Card>
  )
}

interface PersonaArrayInputProps {
  label: string
  values: string[]
  onAdd: (value: string) => void
  onRemove: (index: number) => void
  placeholder: string
}

function PersonaArrayInput({ label, values, onAdd, onRemove, placeholder }: PersonaArrayInputProps) {
  const [inputValue, setInputValue] = useState('')

  const handleAdd = () => {
    if (inputValue.trim()) {
      onAdd(inputValue)
      setInputValue('')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAdd()
    }
  }

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <div className="flex gap-2">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          className="flex-1"
        />
        <Button type="button" variant="outline" size="sm" onClick={handleAdd}>
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      {values.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {values.map((value, index) => (
            <Badge key={index} variant="secondary" className="flex items-center gap-1">
              {value}
              <button
                onClick={() => onRemove(index)}
                className="ml-1 hover:text-red-600"
                type="button"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}