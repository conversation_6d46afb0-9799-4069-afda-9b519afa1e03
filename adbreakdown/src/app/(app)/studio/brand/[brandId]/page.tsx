'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Building2, ArrowLeft, Edit, Globe, Calendar, Target, Palette, 
  MessageSquare, Users, TrendingUp, Eye, EyeOff 
} from 'lucide-react'
import { useRouter, useParams } from 'next/navigation'
import Image from 'next/image'

interface Demographics {
  age_range?: string
  gender?: string
  income_level?: string
  education_level?: string
  location?: string
  occupation?: string
  family_status?: string
}

interface Persona {
  id: string
  name: string
  age: number
  occupation: string
  location: string
  bio: string
  goals: string[]
  pain_points: string[]
  preferred_channels: string[]
}

interface BrandProfile {
  id: string
  brand_name: string
  slug: string
  tagline?: string
  logo_url?: string
  website_url?: string
  industry_category: string
  company_size?: string
  founded_year?: number
  positioning_statement?: string
  brand_values?: string[]
  mission_statement?: string
  vision_statement?: string
  primary_colors?: string[]
  secondary_colors?: string[]
  font_primary?: string
  font_secondary?: string
  voice_attributes?: string[]
  tone_keywords?: string[]
  brand_personality?: string[]
  communication_style?: string
  primary_demographics?: Demographics
  secondary_demographics?: Demographics
  psychographics?: string[]
  customer_personas?: Persona[]
  direct_competitors?: string[]
  indirect_competitors?: string[]
  competitive_advantages?: string[]
  completion_percentage: number
  creation_method: string
  created_at: string
  updated_at: string
  last_analysis_date?: string
  assets?: any[]
}

export default function BrandProfilePage() {
  const router = useRouter()
  const params = useParams()
  const brandId = params.brandId as string
  
  const [brand, setBrand] = useState<BrandProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const fetchBrand = useCallback(async () => {
    try {
      const response = await fetch(`/api/brands/${brandId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch brand profile')
      }
      const data = await response.json()
      setBrand(data.brand)
    } catch (err: any) {
      setError(err.message || 'Failed to load brand profile')
    } finally {
      setLoading(false)
    }
  }, [brandId])

  useEffect(() => {
    if (brandId) {
      fetchBrand()
    }
  }, [brandId, fetchBrand])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-100 text-green-800'
    if (percentage >= 50) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  if (loading) {
    return (
      <div className="flex flex-1 flex-col gap-6 max-w-6xl mx-auto">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              {[1, 2, 3].map((i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="h-4 bg-gray-200 rounded mb-4"></div>
                    <div className="h-20 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="space-y-6">
              <Card>
                <CardContent className="p-6">
                  <div className="h-32 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !brand) {
    return (
      <div className="flex flex-1 flex-col gap-6 max-w-6xl mx-auto">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()} className="p-2">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">Brand Profile</h1>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <p className="text-red-600">{error || 'Brand profile not found'}</p>
            <Button 
              variant="outline" 
              onClick={() => router.push('/studio/brand')}
              className="mt-4"
            >
              Back to Brand Center
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex flex-1 flex-col gap-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()} className="p-2">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-4">
            {brand.logo_url ? (
              <div className="relative h-12 w-24">
                <Image
                  src={brand.logo_url}
                  alt={`${brand.brand_name} logo`}
                  fill
                  className="rounded-lg object-contain"
                />
              </div>
            ) : (
              <div className="w-12 h-12 bg-rose-100 rounded-lg flex items-center justify-center">
                <Building2 className="h-6 w-6 text-rose-600" />
              </div>
            )}
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{brand.brand_name}</h1>
              {brand.tagline && (
                <p className="text-gray-600">{brand.tagline}</p>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {brand.website_url && (
            <Button variant="outline" onClick={() => window.open(brand.website_url, '_blank')}>
              <Globe className="h-4 w-4 mr-2" />
              Website
            </Button>
          )}
          <Button 
            onClick={() => router.push(`/studio/brand/${brand.id}/edit`)}
            className="bg-rose-600 hover:bg-rose-700"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Brand Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">Industry</p>
                  <Badge variant="secondary">{brand.industry_category}</Badge>
                </div>
                {brand.company_size && (
                  <div>
                    <p className="text-sm font-medium text-gray-600">Company Size</p>
                    <p className="text-sm">{brand.company_size}</p>
                  </div>
                )}
                {brand.founded_year && (
                  <div>
                    <p className="text-sm font-medium text-gray-600">Founded</p>
                    <p className="text-sm">{brand.founded_year}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-gray-600">Created</p>
                  <p className="text-sm flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    {formatDate(brand.created_at)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Brand Identity */}
          {(brand.positioning_statement || brand.mission_statement || brand.vision_statement || (brand.brand_values && brand.brand_values.length > 0)) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Brand Identity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {brand.positioning_statement && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Positioning Statement</p>
                    <p className="text-sm text-gray-900">{brand.positioning_statement}</p>
                  </div>
                )}
                {brand.mission_statement && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Mission Statement</p>
                    <p className="text-sm text-gray-900">{brand.mission_statement}</p>
                  </div>
                )}
                {brand.vision_statement && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Vision Statement</p>
                    <p className="text-sm text-gray-900">{brand.vision_statement}</p>
                  </div>
                )}
                {brand.brand_values && brand.brand_values.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Brand Values</p>
                    <div className="flex flex-wrap gap-2">
                      {brand.brand_values.map((value, index) => (
                        <Badge key={index} variant="outline">{value}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Visual Identity */}
          {((brand.primary_colors && brand.primary_colors.length > 0) || (brand.secondary_colors && brand.secondary_colors.length > 0) || brand.font_primary || brand.font_secondary) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Visual Identity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {brand.primary_colors && brand.primary_colors.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Primary Colors</p>
                    <div className="flex flex-wrap gap-2">
                      {brand.primary_colors.map((color, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div 
                            className="w-6 h-6 rounded-full border"
                            style={{ backgroundColor: color }}
                          ></div>
                          <span className="text-sm font-mono">{color}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                {brand.secondary_colors && brand.secondary_colors.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Secondary Colors</p>
                    <div className="flex flex-wrap gap-2">
                      {brand.secondary_colors.map((color, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div 
                            className="w-6 h-6 rounded-full border"
                            style={{ backgroundColor: color }}
                          ></div>
                          <span className="text-sm font-mono">{color}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                <div className="grid grid-cols-2 gap-4">
                  {brand.font_primary && (
                    <div>
                      <p className="text-sm font-medium text-gray-600">Primary Font</p>
                      <p className="text-sm font-mono">{brand.font_primary}</p>
                    </div>
                  )}
                  {brand.font_secondary && (
                    <div>
                      <p className="text-sm font-medium text-gray-600">Secondary Font</p>
                      <p className="text-sm font-mono">{brand.font_secondary}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Brand Voice */}
          {(brand.communication_style || (brand.voice_attributes && brand.voice_attributes.length > 0) || (brand.tone_keywords && brand.tone_keywords.length > 0) || (brand.brand_personality && brand.brand_personality.length > 0)) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Brand Voice & Tone
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {brand.communication_style && (
                  <div>
                    <p className="text-sm font-medium text-gray-600">Communication Style</p>
                    <Badge variant="secondary">{brand.communication_style}</Badge>
                  </div>
                )}
                {brand.voice_attributes && brand.voice_attributes.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Voice Attributes</p>
                    <div className="flex flex-wrap gap-2">
                      {brand.voice_attributes.map((attr, index) => (
                        <Badge key={index} variant="outline">{attr}</Badge>
                      ))}
                    </div>
                  </div>
                )}
                {brand.tone_keywords && brand.tone_keywords.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Tone Keywords</p>
                    <div className="flex flex-wrap gap-2">
                      {brand.tone_keywords.map((keyword, index) => (
                        <Badge key={index} variant="outline">{keyword}</Badge>
                      ))}
                    </div>
                  </div>
                )}
                {brand.brand_personality && brand.brand_personality.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Brand Personality</p>
                    <div className="flex flex-wrap gap-2">
                      {brand.brand_personality.map((trait, index) => (
                        <Badge key={index} variant="outline">{trait}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Target Audience */}
          {(brand.primary_demographics || brand.secondary_demographics || (brand.psychographics && brand.psychographics.length > 0) || (brand.customer_personas && brand.customer_personas.length > 0)) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Target Audience
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Primary Demographics */}
                {brand.primary_demographics && Object.values(brand.primary_demographics).some(value => value && value.trim() !== '') && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-3">Primary Demographics</p>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {brand.primary_demographics.age_range && (
                        <div>
                          <p className="text-xs text-gray-500">Age Range</p>
                          <p className="text-sm font-medium">{brand.primary_demographics.age_range}</p>
                        </div>
                      )}
                      {brand.primary_demographics.gender && (
                        <div>
                          <p className="text-xs text-gray-500">Gender</p>
                          <p className="text-sm font-medium">{brand.primary_demographics.gender}</p>
                        </div>
                      )}
                      {brand.primary_demographics.income_level && (
                        <div>
                          <p className="text-xs text-gray-500">Income Level</p>
                          <p className="text-sm font-medium">{brand.primary_demographics.income_level}</p>
                        </div>
                      )}
                      {brand.primary_demographics.education_level && (
                        <div>
                          <p className="text-xs text-gray-500">Education</p>
                          <p className="text-sm font-medium">{brand.primary_demographics.education_level}</p>
                        </div>
                      )}
                      {brand.primary_demographics.location && (
                        <div>
                          <p className="text-xs text-gray-500">Location</p>
                          <p className="text-sm font-medium">{brand.primary_demographics.location}</p>
                        </div>
                      )}
                      {brand.primary_demographics.occupation && (
                        <div>
                          <p className="text-xs text-gray-500">Occupation</p>
                          <p className="text-sm font-medium">{brand.primary_demographics.occupation}</p>
                        </div>
                      )}
                      {brand.primary_demographics.family_status && (
                        <div>
                          <p className="text-xs text-gray-500">Family Status</p>
                          <p className="text-sm font-medium">{brand.primary_demographics.family_status}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Secondary Demographics */}
                {brand.secondary_demographics && Object.values(brand.secondary_demographics).some(value => value && value.trim() !== '') && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-3">Secondary Demographics</p>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {brand.secondary_demographics.age_range && (
                        <div>
                          <p className="text-xs text-gray-500">Age Range</p>
                          <p className="text-sm font-medium">{brand.secondary_demographics.age_range}</p>
                        </div>
                      )}
                      {brand.secondary_demographics.gender && (
                        <div>
                          <p className="text-xs text-gray-500">Gender</p>
                          <p className="text-sm font-medium">{brand.secondary_demographics.gender}</p>
                        </div>
                      )}
                      {brand.secondary_demographics.income_level && (
                        <div>
                          <p className="text-xs text-gray-500">Income Level</p>
                          <p className="text-sm font-medium">{brand.secondary_demographics.income_level}</p>
                        </div>
                      )}
                      {brand.secondary_demographics.education_level && (
                        <div>
                          <p className="text-xs text-gray-500">Education</p>
                          <p className="text-sm font-medium">{brand.secondary_demographics.education_level}</p>
                        </div>
                      )}
                      {brand.secondary_demographics.location && (
                        <div>
                          <p className="text-xs text-gray-500">Location</p>
                          <p className="text-sm font-medium">{brand.secondary_demographics.location}</p>
                        </div>
                      )}
                      {brand.secondary_demographics.occupation && (
                        <div>
                          <p className="text-xs text-gray-500">Occupation</p>
                          <p className="text-sm font-medium">{brand.secondary_demographics.occupation}</p>
                        </div>
                      )}
                      {brand.secondary_demographics.family_status && (
                        <div>
                          <p className="text-xs text-gray-500">Family Status</p>
                          <p className="text-sm font-medium">{brand.secondary_demographics.family_status}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Psychographics */}
                {brand.psychographics && brand.psychographics.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Psychographics</p>
                    <div className="flex flex-wrap gap-2">
                      {brand.psychographics.map((trait, index) => (
                        <Badge key={index} variant="outline">{trait}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Customer Personas */}
                {brand.customer_personas && brand.customer_personas.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-3">Customer Personas</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {brand.customer_personas.map((persona, index) => (
                        <Card key={persona.id || index} className="p-4 bg-gray-50">
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium text-gray-900">{persona.name || `Persona ${index + 1}`}</h4>
                              {persona.age && (
                                <Badge variant="secondary" className="text-xs">Age {persona.age}</Badge>
                              )}
                            </div>
                            
                            {(persona.occupation || persona.location) && (
                              <div className="text-sm text-gray-600">
                                {persona.occupation && <p>{persona.occupation}</p>}
                                {persona.location && <p>{persona.location}</p>}
                              </div>
                            )}
                            
                            {persona.bio && (
                              <p className="text-sm text-gray-700">{persona.bio}</p>
                            )}
                            
                            {persona.goals && persona.goals.length > 0 && (
                              <div>
                                <p className="text-xs font-medium text-gray-500 mb-1">Goals</p>
                                <div className="flex flex-wrap gap-1">
                                  {persona.goals.map((goal, goalIndex) => (
                                    <Badge key={goalIndex} variant="outline" className="text-xs">
                                      {goal}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {persona.pain_points && persona.pain_points.length > 0 && (
                              <div>
                                <p className="text-xs font-medium text-gray-500 mb-1">Pain Points</p>
                                <div className="flex flex-wrap gap-1">
                                  {persona.pain_points.map((pain, painIndex) => (
                                    <Badge key={painIndex} variant="secondary" className="text-xs bg-red-100 text-red-700">
                                      {pain}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {persona.preferred_channels && persona.preferred_channels.length > 0 && (
                              <div>
                                <p className="text-xs font-medium text-gray-500 mb-1">Preferred Channels</p>
                                <div className="flex flex-wrap gap-1">
                                  {persona.preferred_channels.slice(0, 3).map((channel, channelIndex) => (
                                    <Badge key={channelIndex} variant="outline" className="text-xs">
                                      {channel}
                                    </Badge>
                                  ))}
                                  {persona.preferred_channels.length > 3 && (
                                    <Badge variant="outline" className="text-xs">
                                      +{persona.preferred_channels.length - 3} more
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Competition */}
          {((brand.direct_competitors && brand.direct_competitors.length > 0) || (brand.indirect_competitors && brand.indirect_competitors.length > 0) || (brand.competitive_advantages && brand.competitive_advantages.length > 0)) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Competitive Landscape
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {brand.direct_competitors && brand.direct_competitors.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Direct Competitors</p>
                    <div className="flex flex-wrap gap-2">
                      {brand.direct_competitors.map((competitor, index) => (
                        <Badge key={index} variant="secondary">{competitor}</Badge>
                      ))}
                    </div>
                  </div>
                )}
                {brand.indirect_competitors && brand.indirect_competitors.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Indirect Competitors</p>
                    <div className="flex flex-wrap gap-2">
                      {brand.indirect_competitors.map((competitor, index) => (
                        <Badge key={index} variant="outline">{competitor}</Badge>
                      ))}
                    </div>
                  </div>
                )}
                {brand.competitive_advantages && brand.competitive_advantages.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Competitive Advantages</p>
                    <div className="space-y-2">
                      {brand.competitive_advantages.map((advantage, index) => (
                        <div key={index} className="text-sm p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                          {advantage}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Profile Completion */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Profile Completion</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="relative w-20 h-20 mx-auto mb-4">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      className="text-gray-200"
                      stroke="currentColor"
                      strokeWidth="3"
                      fill="none"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <path
                      className="text-rose-600"
                      stroke="currentColor"
                      strokeWidth="3"
                      strokeDasharray={`${brand.completion_percentage}, 100`}
                      strokeLinecap="round"
                      fill="none"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xl font-bold text-gray-900">{brand.completion_percentage}%</span>
                  </div>
                </div>
                <Badge className={`${getCompletionColor(brand.completion_percentage)} mb-4`}>
                  {brand.completion_percentage >= 80 ? 'Complete' : 
                   brand.completion_percentage >= 50 ? 'In Progress' : 'Getting Started'}
                </Badge>
                <p className="text-sm text-gray-600 mb-4">
                  Complete your brand profile to get better ad analysis recommendations
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => router.push(`/studio/brand/${brand.id}/edit`)}
                >
                  Complete Profile
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Creation Method</span>
                <Badge variant="outline" className="text-xs">
                  {brand.creation_method === 'manual' ? 'Manual' : brand.creation_method}
                </Badge>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Last Updated</span>
                <span className="text-sm">{formatDate(brand.updated_at)}</span>
              </div>
              {brand.last_analysis_date && (
                <>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Last Analysis</span>
                    <span className="text-sm">{formatDate(brand.last_analysis_date)}</span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push('/studio/optimise')}
              >
                <Eye className="h-4 w-4 mr-2" />
                Analyze with this Brand
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                disabled
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Brand Analysis Report
                <Badge variant="secondary" className="ml-2 text-xs">Soon</Badge>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}