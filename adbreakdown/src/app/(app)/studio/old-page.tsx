'use client'

import { useState, useEffect, Suspense } from 'react'
import { User<PERSON>utton } from '@clerk/nextjs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { BarChart3, PlayCircle, ChevronRight, Users, TrendingUp, Crown, AlertTriangle } from 'lucide-react'
import Link from 'next/link'
import AnalysisInputCard from '@/components/studio/AnalysisInputCard'
import AnalysisCard from '@/components/studio/AnalysisCard'
import AdTileGrid from '@/components/studio/AdTileGrid'
import PageHeader from '@/components/PageHeader'
import AdminSubmissionForm from '@/components/studio/AdminSubmissionForm'
import Navigation from '@/components/Navigation'
import { useRouter, useSearchParams } from 'next/navigation'
// Note: Using conditional rendering instead of Dialog component for now

interface Analysis {
  id: string
  slug?: string | null
  youtube_url: string
  status: string
  title: string
  inferred_brand: string
  duration: number
  duration_seconds: number
  thumbnail_url: string
  video_thumbnail_url?: string
  overall_sentiment: number
  created_at: string
  analysis_completed_at?: string
  youtube_video_id: string
  is_public: boolean
  view_count?: number
  like_count?: number
  featured?: boolean
  author_name?: string
}

interface DashboardStats {
  totalAnalyses: number
  completedAnalyses: number
  avgSentiment: number
  creditsRemaining: number
}

// Component that uses useSearchParams - needs to be wrapped in Suspense
function SearchParamsHandler({ handleAnalyzeVideo }: { handleAnalyzeVideo: (url: string) => Promise<void> }) {
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const analyzeOnLoad = searchParams.get('analyze')
    const youtubeUrlForAnalysis = localStorage.getItem('youtubeUrlForAnalysis')

    if (analyzeOnLoad === 'true' && youtubeUrlForAnalysis) {
      handleAnalyzeVideo(youtubeUrlForAnalysis)
      localStorage.removeItem('youtubeUrlForAnalysis')
      router.replace('/studio', undefined) // Clean up URL
    }
  }, [searchParams, router, handleAnalyzeVideo])

  return null
}

export default function Dashboard() {
  const { isAuthenticated, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(false)
  const [analyses, setAnalyses] = useState<Analysis[]>([])
  const [analysesLoading, setAnalysesLoading] = useState(true)
  const [stats, setStats] = useState<DashboardStats>({
    totalAnalyses: 0,
    completedAnalyses: 0,
    avgSentiment: 0,
    creditsRemaining: 0
  })
  const [hasMore, setHasMore] = useState(false)
  const [page, setPage] = useState(1)
  const [viewMode, setViewMode] = useState<'list' | 'tiles'>('list')
  const [showAdminSubmission, setShowAdminSubmission] = useState(false)
  const [adminSubmissionData, setAdminSubmissionData] = useState<any>(null)
  const ANALYSES_PER_PAGE = 10

  useEffect(() => {
    const fetchAnalyses = async () => {
      try {
        setAnalysesLoading(true)
        const response = await fetch(`/api/analyses?page=1&limit=${ANALYSES_PER_PAGE}`)
        if (response.ok) {
          const data = await response.json()
          setAnalyses(data.analyses || [])
          setHasMore(data.analyses?.length === ANALYSES_PER_PAGE)
          
          // Calculate stats
          const completed = data.analyses?.filter((a: Analysis) => a.status === 'completed') || []
          const avgSentiment = completed.length > 0 
            ? completed.reduce((sum: number, a: Analysis) => sum + (a.overall_sentiment || 0), 0) / completed.length
            : 0

          setStats({
            totalAnalyses: data.analyses?.length || 0,
            completedAnalyses: completed.length,
            avgSentiment,
            creditsRemaining: data.credits_remaining || 0
          })
        } else {
          console.error('Failed to fetch analyses')
        }
      } catch (err) {
        console.error('Error fetching analyses:', err)
      } finally {
        setAnalysesLoading(false)
      }
    }

    if (isAuthenticated && !authLoading) {
      fetchAnalyses()
    }
  }, [isAuthenticated, authLoading])

  const handleAnalyzeVideo = async (youtubeUrl: string) => {
    try {
      setLoading(true)
      
      // Debug: Check auth state and environment
      console.log('🔐 Debug - Starting analysis:', { 
        isAuthenticated, 
        authLoading, 
        url: youtubeUrl,
        timestamp: new Date().toISOString(),
        cookies: document.cookie.split(';').filter(c => c.includes('clerk')).map(c => c.trim().substring(0, 50))
      })
      
      // Step 1: Create analysis with enhanced validation
      console.log('📝 Step 1: Creating analysis...')
      const createResponse = await fetch('/api/analyses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ youtubeUrl: youtubeUrl })
      })
      
      const responseData = await createResponse.json()
      console.log('📄 Step 1 Response:', { status: createResponse.status, data: responseData })
      
      // Handle video too long error
      if (createResponse.status === 413 && responseData.error === 'video_too_long') {
        setLoading(false)
        setAdminSubmissionData({
          url: youtubeUrl,
          title: responseData.videoData?.metadata?.title || 'Unknown Title',
          channelTitle: responseData.videoData?.metadata?.channelTitle || 'Unknown Channel',
          duration: responseData.videoData?.duration?.formatted || 'Unknown Duration',
          thumbnailUrl: responseData.videoData?.metadata?.thumbnailUrl || ''
        })
        setShowAdminSubmission(true)
        return
      }
      
      if (!createResponse.ok) {
        throw new Error(responseData.error || 'Failed to create analysis')
      }
      
      const { analysis_id, slug, isPublic, message } = responseData
      
      // Handle existing public analysis
      if (isPublic) {
        setLoading(false)
        console.log('Found existing public analysis:', message)
        window.location.href = `/ad/${analysis_id}`
        return
      }
      
      // Handle existing user analysis
      if (message?.includes('already exists')) {
        setLoading(false)
        console.log('Analysis already exists:', message)
        window.location.href = `/ad/${analysis_id}`
        return
      }
      
      // Step 2: Trigger initial analysis for new analysis
      console.log('🚀 Step 2: Triggering analysis for ID:', analysis_id)
      const triggerResponse = await fetch(`/api/analyses/${analysis_id}/trigger-initial-analysis`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ youtubeUrl })
      })
      
      const triggerData = await triggerResponse.json()
      console.log('⚡ Step 2 Response:', { status: triggerResponse.status, data: triggerData })
      
      if (triggerResponse.status === 402) {
        // Enhanced credit error with specific details
        const creditError = triggerData.error || 'Insufficient credits'
        const remaining = triggerData.credits_remaining || 0
        const required = triggerData.required || 1
        throw new Error(`Insufficient credits. You need ${required} credits but only have ${remaining} remaining. Please upgrade your plan.`)
      }
      
      if (!triggerResponse.ok) {
        throw new Error(triggerData.error || 'Failed to trigger analysis')
      }
      
      // Navigate to new analysis
      window.location.href = `/ad/${analysis_id}`
      
    } catch (err: any) {
      setLoading(false)
      throw err
    }
  }


  const loadMoreAnalyses = async () => {
    try {
      const nextPage = page + 1
      const response = await fetch(`/api/analyses?page=${nextPage}&limit=${ANALYSES_PER_PAGE}`)
      if (response.ok) {
        const data = await response.json()
        setAnalyses(prev => [...prev, ...data.analyses])
        setHasMore(data.analyses.length === ANALYSES_PER_PAGE)
        setPage(nextPage)
      }
    } catch (err) {
      console.error('Error loading more analyses:', err)
    }
  }

  if (authLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <p className="mb-4">Please sign in to access your dashboard.</p>
          <Link href="/sign-in">
            <Button>Sign In</Button>
          </Link>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      {/* Search params handler wrapped in Suspense */}
      <Suspense fallback={null}>
        <SearchParamsHandler handleAnalyzeVideo={handleAnalyzeVideo} />
      </Suspense>

      <main className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Dashboard
          </h1>
          <p className="text-md text-gray-600 max-w-3xl mx-auto mb-8">
            Analyze your video ads with AI-powered insights. Track performance, understand audience sentiment, and optimize your advertising strategy.
          </p>
        </div>
        
        {/* Analysis Input Section */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
          <div className="lg:col-span-3">
            <AnalysisInputCard onAnalyze={handleAnalyzeVideo} loading={loading} creditsRemaining={stats.creditsRemaining} />
          </div>
          <div className="lg:col-span-1">
            <Link href="/billing">
              <Card className="cursor-pointer hover:shadow-md transition-shadow h-full">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600 flex items-center gap-2">
                    Credits Left
                    <Crown className="h-4 w-4 text-purple-600" />
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">{stats.creditsRemaining}</div>
                  <div className="text-xs text-gray-500 mt-1">Click to manage</div>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>


        <div className="grid gap-8">
          
          {/* Recent Analyses */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Analyses</CardTitle>
                  <CardDescription>
                    Your video ad analyses and their current status
                  </CardDescription>
                </div>
                {analyses.length > 0 && (
                  <div className="flex items-center gap-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <Users className="h-4 w-4 mr-1" />
                      {analyses.length} total
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant={viewMode === 'list' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setViewMode('list')}
                      >
                        List
                      </Button>
                      <Button
                        variant={viewMode === 'tiles' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setViewMode('tiles')}
                      >
                        Tiles
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {analysesLoading ? (
                <div className="text-center py-12">
                  <div className="animate-spin h-8 w-8 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading analyses...</p>
                </div>
              ) : analyses.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <PlayCircle className="h-12 w-12 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No analyses yet</h3>
                  <p className="text-gray-600 mb-4">
                    Get started by analyzing your first video ad above
                  </p>
                  <div className="inline-flex items-center text-sm text-blue-600">
                    <TrendingUp className="h-4 w-4 mr-1" />
                    Unlock AI-powered insights
                  </div>
                </div>
              ) : viewMode === 'list' ? (
                <div className="space-y-4">
                  {analyses.map((analysis) => (
                    <AnalysisCard key={analysis.id} analysis={analysis} />
                  ))}
                  
                  {hasMore && (
                    <div className="text-center pt-4">
                      <Button 
                        variant="outline" 
                        onClick={loadMoreAnalyses}
                        className="w-full sm:w-auto"
                      >
                        Load More Analyses
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div>
                  <AdTileGrid 
                    analyses={analyses.map(analysis => ({
                      ...analysis,
                      duration_seconds: analysis.duration || analysis.duration_seconds || 0,
                      youtube_video_id: analysis.youtube_url?.split('v=')[1]?.split('&')[0] || '',
                      is_public: false // Dashboard analyses are private by default
                    }))} 
                    loading={false}
                    showFeatured={false}
                    columns={3}
                  />
                  
                  {hasMore && (
                    <div className="text-center pt-6">
                      <Button 
                        variant="outline" 
                        onClick={loadMoreAnalyses}
                        className="w-full sm:w-auto"
                      >
                        Load More Analyses
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Admin Submission Modal */}
      {showAdminSubmission && adminSubmissionData && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl max-h-[90vh] overflow-y-auto m-4">
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">Video Analysis Request</h2>
              <AdminSubmissionForm
                videoData={adminSubmissionData}
                onClose={() => setShowAdminSubmission(false)}
                onSubmitted={() => {
                  console.log('Request submitted successfully!')
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}