'use client'

import { useEffect, useState, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Shield, Clock, CheckCircle, AlertCircle, Sparkles, Download, Trash2 } from 'lucide-react'
import ProcessingStatusModal from '@/components/analysis/ProcessingStatusModal'
import { AnalysisSections } from '@/components/ui/sectioned-markdown'
import { JsonAnalysisDisplay } from '@/components/optimise/json-analysis-display'
import { usePDFDownload } from '@/hooks/usePDFDownload'
import Link from 'next/link'
import Image from 'next/image'
import StrategicAdvisorButton from '@/components/chat/StrategicAdvisorButton'

interface Report {
  id: string;
  report_type_id: string;
  status: string;
  content: { raw_content: string } | string | any;
  generated_at: string;
  report_types: {
    name: string;
  };
}

interface PrivateAnalysis {
  id: string
  slug: string
  title: string
  inferred_brand: string
  youtube_url: string
  youtube_video_id: string
  status: string
  analysis_type: string
  created_at: string
  updated_at: string
  thumbnail_url?: string
  duration_seconds?: number
  marketing_analysis?: string | any
  optimization_recommendations?: string | any
  pre_launch_score?: number
  analysis_completed_at?: string
  sentiment_analysis?: any
  target_audience?: any
  creative_elements?: any
  competitive_insights?: any
  performance_predictions?: any
  reports?: Report[]; // Add reports field
}


export default function PrivateAnalysisPage() {
  const params = useParams()
  const router = useRouter()
  const { isAuthenticated, loading: authLoading } = useAuth()
  const [analysis, setAnalysis] = useState<PrivateAnalysis | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [generatingReport, setGeneratingReport] = useState<string | null>(null)
  const [reportTypes, setReportTypes] = useState<Array<{name: string, title: string, description: string, credit_cost: number}>>([])
  const [reportTypesLoading, setReportTypesLoading] = useState(true)
  const [videoUrl, setVideoUrl] = useState<string | null>(null)
  const [videoUrlLoading, setVideoUrlLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const { downloadPDF, isGenerating } = usePDFDownload()

  const fetchVideoUrl = useCallback(async (analysisSlug: string) => {
    try {
      setVideoUrlLoading(true)
      const response = await fetch(`/api/analyses/private/${analysisSlug}/video-url`)
      if (!response.ok) {
        throw new Error('Failed to fetch video URL')
      }
      const data = await response.json()
      setVideoUrl(data.signedUrl)
    } catch (error) {
      console.error('Error fetching video URL:', error)
      // Fallback to the original youtube_url if signed URL fails
      if (analysis?.youtube_url) {
        setVideoUrl(analysis.youtube_url)
      }
    } finally {
      setVideoUrlLoading(false)
    }
  }, [analysis?.youtube_url])

  const fetchReportTypes = async () => {
    try {
      setReportTypesLoading(true)
      const response = await fetch('/api/report-types')
      if (!response.ok) {
        console.warn('Report types API unavailable, using fallback')
        throw new Error('API unavailable')
      }
      const data = await response.json()
      setReportTypes(data.reportTypes)
      console.log('✅ Successfully loaded report types from API')
    } catch (error) {
      console.warn('Using fallback report types:', error instanceof Error ? error.message : 'Unknown error')
      // Fallback to hardcoded report types if API fails
      setReportTypes([
        { name: 'press_release', title: 'Press Release', description: 'Generate professional press release content.', credit_cost: 1 },
        { name: 'social_media_posts', title: 'Social Media Posts', description: 'Create engaging posts for various platforms.', credit_cost: 1 },
        { name: 'marketing_scorecard', title: 'Marketing Scorecard', description: 'Get a scorecard based on marketing analysis.', credit_cost: 1 },
        { name: 'seo_keywords', title: 'SEO Keywords', description: 'Identify relevant SEO keywords for your video.', credit_cost: 1 },
        { name: 'content_suggestions', title: 'Content Suggestions', description: 'Receive suggestions to improve video content.', credit_cost: 1 },
      ])
    } finally {
      setReportTypesLoading(false)
    }
  }

  const generateReport = async (reportTypeName: string) => {
    if (!analysis) return
    setGeneratingReport(reportTypeName)
    try {
      const response = await fetch(`/api/analyses/private/${analysis.slug}/generate-llm-feature`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ report_type_name: reportTypeName })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate report')
      }

      const data = await response.json()
      console.log(`${reportTypeName} generated:`, data)
      // Re-fetch analysis to update the reports array
      const updatedAnalysisResponse = await fetch(`/api/analyses/private/${analysis.slug}`)
      if (updatedAnalysisResponse.ok) {
        const updatedData = await updatedAnalysisResponse.json()
        setAnalysis(updatedData.analysis)
      }
    } catch (err: any) {
      console.error(`Error generating ${reportTypeName}:`, err)
      alert(`Failed to generate ${reportTypeName}: ${err.message}`)
    } finally {
      setGeneratingReport(null)
    }
  }

  const handleDownloadReport = async () => {
    if (!analysis) return
    
    try {
      await downloadPDF({
        filename: `${analysis.title.replace(/[^a-zA-Z0-9]/g, '-')}-analysis-report.pdf`,
        containerSelector: 'main',
        excludeSelectors: [
          'nav', 'button', '.hidden', '[data-pdf-download]',
          'script', 'style', '.no-pdf', '.download-btn'
        ]
      })
    } catch (error) {
      console.error('Download failed:', error)
      alert('Failed to download PDF. Please try again.')
    }
  }

  const handleDeleteAnalysis = async () => {
    if (!analysis) return

    const confirmDelete = window.confirm(
      `Are you sure you want to delete "${analysis.title}"?\n\n` +
      'This action cannot be undone. ' +
      (analysis.analysis_type === 'file_upload' 
        ? 'The uploaded video file will also be permanently deleted from storage.' 
        : 'This will remove the analysis from your account.')
    )

    if (!confirmDelete) return

    try {
      setIsDeleting(true)
      
      const response = await fetch(`/api/analyses/private/${analysis.slug}/delete`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete analysis')
      }

      const result = await response.json()
      console.log('✅ Analysis deleted successfully:', result)

      // Redirect to studio after successful deletion
      router.push('/studio/optimise?deleted=true')
      
    } catch (error: any) {
      console.error('❌ Error deleting analysis:', error)
      alert(`Failed to delete analysis: ${error.message}`)
    } finally {
      setIsDeleting(false)
    }
  }

  useEffect(() => {
    const fetchAnalysis = async () => {
      if (!isAuthenticated || !params.slug) return

      try {
        setLoading(true)
        const response = await fetch(`/api/analyses/private/${params.slug}`)
        
        if (response.status === 404) {
          setError('Private analysis not found or you do not have access to it.')
          return
        }
        
        if (!response.ok) {
          throw new Error('Failed to fetch analysis')
        }

        const data = await response.json()
        setAnalysis(data.analysis)
        
        // Fetch video URL if this is a file upload
        if (data.analysis.analysis_type === 'file_upload') {
          await fetchVideoUrl(data.analysis.slug)
        }
      } catch (err: any) {
        console.error('Error fetching private analysis:', err)
        setError(err.message || 'Failed to load analysis')
      } finally {
        setLoading(false)
      }
    }

    if (!authLoading) {
      fetchAnalysis()
      fetchReportTypes()
    }
  }, [isAuthenticated, authLoading, params.slug, fetchVideoUrl])

  // Auto-refresh for pending/processing analyses
  useEffect(() => {
    if (!analysis || !['pending', 'processing'].includes(analysis.status)) return

    const interval = setInterval(async () => {
      try {
        console.log('🔄 Checking analysis status...', analysis.status)
        const response = await fetch(`/api/analyses/private/${params.slug}`)
        if (response.ok) {
          const data = await response.json()
          if (data.analysis.status !== analysis.status) {
            console.log('📊 Status updated:', analysis.status, '→', data.analysis.status)
            setAnalysis(data.analysis)
            
            // If status changed to completed or failed, stop polling
            if (!['pending', 'processing'].includes(data.analysis.status)) {
              console.log('✅ Analysis completed, stopping polling')
            }
          }
        }
      } catch (error) {
        console.error('Error polling analysis status:', error)
      }
    }, 3000) // Poll every 3 seconds

    return () => clearInterval(interval)
  }, [analysis, params.slug])

  if (authLoading || loading || reportTypesLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p>Loading private analysis...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center">Authentication Required</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-4">Please sign in to access private analyses.</p>
            <Link href="/sign-in">
              <Button>Sign In</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4 max-w-4xl">


          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <AlertCircle className="h-6 w-6 text-red-600" />
                <CardTitle className="text-red-900">Analysis Not Found</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-red-700 mb-4">{error}</p>
              <Link href="/studio">
                <Button>Return to Studio</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!analysis) {
    return null
  }

  console.log('Current analysis object:', analysis)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />
      case 'processing':
        return <div className="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full" />
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Analysis Complete'
      case 'pending':
        return 'Analysis Pending'
      case 'processing':
        return 'Processing...'
      default:
        return status
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <ProcessingStatusModal
        isOpen={analysis?.status === 'pending' || analysis?.status === 'processing'}
      />
      <main className="container mx-auto px-4 max-w-7xl">
        {/* Analysis Details */}
        <Card className="mb-4">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                {getStatusIcon(analysis.status)}
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {analysis.title}
                  </CardTitle>
                  <CardDescription>{analysis.inferred_brand}</CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button 
                  onClick={handleDeleteAnalysis}
                  disabled={isDeleting}
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                  title={isDeleting ? 'Deleting...' : 'Delete analysis'}
                >
                  {isDeleting ? (
                    <div className="animate-spin h-3 w-3 border border-red-500 border-t-transparent rounded-full" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                </Button>
                <Button 
                  onClick={handleDownloadReport}
                  disabled={isGenerating}
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-blue-500 hover:text-blue-700 hover:bg-blue-50 download-btn"
                  title={isGenerating ? 'Generating PDF...' : 'Download report'}
                >
                  {isGenerating ? (
                    <div className="animate-spin h-3 w-3 border border-blue-500 border-t-transparent rounded-full" />
                  ) : (
                    <Download className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Video Player Section - 2/3 width */}
              <div className="lg:col-span-2">
                <div className="w-full aspect-video">
                  {analysis.analysis_type === 'file_upload' ? (
                    // Show video player for uploaded files
                    <div className="w-full h-full">
                      {videoUrlLoading ? (
                        <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
                          <div className="text-center">
                            <div className="animate-spin h-6 w-6 border-b-2 border-purple-600 mx-auto mb-2"></div>
                            <p className="text-sm text-gray-600">Loading video...</p>
                          </div>
                        </div>
                      ) : videoUrl ? (
                        <video 
                          controls 
                          className="w-full h-full rounded-lg shadow-sm object-cover"
                          preload="metadata"
                        >
                          <source src={videoUrl} type="video/mp4" />
                          <source src={videoUrl} type="video/webm" />
                          <source src={videoUrl} type="video/ogg" />
                          Your browser does not support the video tag.
                        </video>
                      ) : (
                        <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
                          <p className="text-gray-600">Video not available</p>
                        </div>
                      )}
                    </div>
                  ) : analysis.youtube_video_id ? (
                    // Show YouTube embed for YouTube videos
                    <div className="w-full h-full">
                      <iframe
                        src={`https://www.youtube.com/embed/${analysis.youtube_video_id}?rel=0&modestbranding=1&showinfo=0&controls=1&disablekb=1&fs=1&iv_load_policy=3&cc_load_policy=0&end_screen=0`}
                        title={analysis.title}
                        className="w-full h-full rounded-lg shadow-sm"
                        style={{ border: 0 }}
                        allowFullScreen
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      ></iframe>
                    </div>
                  ) : analysis.thumbnail_url ? (
                    // Fallback to thumbnail if no video ID
                    <div className="w-full h-full relative">
                      <Image 
                        src={analysis.thumbnail_url} 
                        alt={analysis.title}
                        fill
                        className="object-cover rounded-lg shadow-sm"
                      />
                    </div>
                  ) : (
                    <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
                      <p className="text-gray-600">Video not available</p>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Video Info Section - 1/3 width */}
              <div className="lg:col-span-1">
                <Card className="h-full">

                  <CardContent className="flex flex-col h-full">
                    <div className="space-y-2 mt-4 flex-1">
                    {/* Brand with AI-extracted metadata if available */}
                    <div className="flex items-center text-sm min-w-0">
                      <span className="text-gray-600 mr-2 flex-shrink-0">Brand:</span>
                      <span className="truncate" title={(() => {
                        // Try to get AI-extracted brand first
                        try {
                          if (analysis.marketing_analysis && typeof analysis.marketing_analysis === 'string') {
                            const jsonMatch = analysis.marketing_analysis.match(/\{[\s\S]*\}/)
                            if (jsonMatch) {
                              const parsedResponse = JSON.parse(jsonMatch[0])
                              const aiBrand = parsedResponse.ad_context?.metadata?.brand
                              if (aiBrand && typeof aiBrand === 'string' && aiBrand.trim() !== '') {
                                return aiBrand.trim()
                              }
                            }
                          }
                        } catch (error) {
                          console.log('Could not parse AI brand:', error)
                        }
                        // Fallback to database brand
                        return analysis.inferred_brand || 'Not specified'
                      })()}>
                        {(() => {
                          // Try to get AI-extracted brand first
                          try {
                            if (analysis.marketing_analysis && typeof analysis.marketing_analysis === 'string') {
                              const jsonMatch = analysis.marketing_analysis.match(/\{[\s\S]*\}/)
                              if (jsonMatch) {
                                const parsedResponse = JSON.parse(jsonMatch[0])
                                const aiBrand = parsedResponse.ad_context?.metadata?.brand
                                if (aiBrand && typeof aiBrand === 'string' && aiBrand.trim() !== '') {
                                  return aiBrand.trim()
                                }
                              }
                            }
                          } catch (error) {
                            console.log('Could not parse AI brand:', error)
                          }
                          // Fallback to database brand
                          return analysis.inferred_brand || 'Not specified'
                        })()}
                      </span>
                    </div>
                    
                    <div className="flex items-center text-sm min-w-0">
                      <span className="text-gray-600 mr-2 flex-shrink-0">Duration:</span>
                      <span className="truncate" title={analysis.duration_seconds && analysis.duration_seconds > 0 
                        ? `${Math.floor(analysis.duration_seconds / 60)}:${(analysis.duration_seconds % 60).toString().padStart(2, '0')}`
                        : 'Not available'
                      }>
                        {analysis.duration_seconds && analysis.duration_seconds > 0 
                          ? `${Math.floor(analysis.duration_seconds / 60)}:${(analysis.duration_seconds % 60).toString().padStart(2, '0')}`
                          : 'Not available'
                        }
                      </span>
                    </div>
                    
                    <div className="flex items-center text-sm min-w-0">
                      <span className="text-gray-600 mr-2 flex-shrink-0">Source:</span>
                      <span className="capitalize truncate" title={analysis.analysis_type === 'file_upload' ? 'File Upload' : 'YouTube'}>
                        {analysis.analysis_type === 'file_upload' ? 'File Upload' : 'YouTube'}
                      </span>
                    </div>
                    
                    <div className="flex items-center text-sm min-w-0">
                      <span className="text-gray-600 mr-2 flex-shrink-0">
                        {analysis.status === 'completed' ? 'Completed:' : 'Status:'}
                      </span>
                      <span className="truncate" title={analysis.status === 'completed' && analysis.analysis_completed_at 
                        ? new Date(analysis.analysis_completed_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })
                        : getStatusText(analysis.status)
                      }>
                        {analysis.status === 'completed' && analysis.analysis_completed_at 
                          ? new Date(analysis.analysis_completed_at).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'numeric',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })
                          : getStatusText(analysis.status)
                        }
                      </span>
                    </div>

                    {/* Additional AI-extracted metadata fields */}
                    {(() => {
                      try {
                        if (analysis.marketing_analysis && typeof analysis.marketing_analysis === 'string') {
                          const jsonMatch = analysis.marketing_analysis.match(/\{[\s\S]*\}/)
                          if (jsonMatch) {
                            const parsedResponse = JSON.parse(jsonMatch[0])
                            const metadata = parsedResponse.ad_context?.metadata
                            
                            if (metadata) {
                              return (
                                <>
                                  {metadata.product_name && metadata.product_name.toLowerCase() !== 'unknown' && metadata.product_name.toLowerCase() !== 'null' && (
                                    <div className="flex items-center text-sm min-w-0">
                                      <span className="text-gray-600 mr-2 flex-shrink-0">Product Name:</span>
                                      <span className="truncate" title={metadata.product_name}>{metadata.product_name}</span>
                                    </div>
                                  )}
                                  {metadata.sku_name && metadata.sku_name.toLowerCase() !== 'unknown' && metadata.sku_name.toLowerCase() !== 'null' && (
                                    <div className="flex items-center text-sm min-w-0">
                                      <span className="text-gray-600 mr-2 flex-shrink-0">SKU:</span>
                                      <span className="truncate" title={metadata.sku_name}>{metadata.sku_name}</span>
                                    </div>
                                  )}
                                  {metadata.product_category && metadata.product_category.toLowerCase() !== 'unknown' && metadata.product_category.toLowerCase() !== 'null' && (
                                    <div className="flex items-center text-sm min-w-0">
                                      <span className="text-gray-600 mr-2 flex-shrink-0">Category:</span>
                                      <span className="truncate" title={metadata.product_category}>{metadata.product_category}</span>
                                    </div>
                                  )}
                                  {metadata.campaign_category && metadata.campaign_category.toLowerCase() !== 'unknown' && metadata.campaign_category.toLowerCase() !== 'null' && (
                                    <div className="flex items-center text-sm min-w-0">
                                      <span className="text-gray-600 mr-2 flex-shrink-0">Campaign:</span>
                                      <span className="truncate" title={metadata.campaign_category}>{metadata.campaign_category}</span>
                                    </div>
                                  )}
                                  {metadata.parent_entity && metadata.parent_entity.toLowerCase() !== 'unknown' && metadata.parent_entity.toLowerCase() !== 'null' && (
                                    <div className="flex items-center text-sm min-w-0">
                                      <span className="text-gray-600 mr-2 flex-shrink-0">Entity:</span>
                                      <span className="truncate" title={metadata.parent_entity}>{metadata.parent_entity}</span>
                                    </div>
                                  )}
                                  {metadata.celebrity && metadata.celebrity.toLowerCase() !== 'unknown' && metadata.celebrity.toLowerCase() !== 'null' && (
                                    <div className="flex items-center text-sm min-w-0">
                                      <span className="text-gray-600 mr-2 flex-shrink-0">Celebrity:</span>
                                      <span className="truncate" title={metadata.celebrity}>{metadata.celebrity}</span>
                                    </div>
                                  )}
                                </>
                              )
                            }
                          }
                        }
                      } catch (error) {
                        console.log('Could not parse AI metadata:', error)
                      }
                      return null
                    })()}
                    
                    {analysis.analysis_type !== 'file_upload' && analysis.youtube_url && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Source URL</label>
                        <div className="mt-1">
                          <Link 
                            href={analysis.youtube_url} 
                            target="_blank" 
                            className="text-blue-600 hover:text-blue-800 text-xs break-all"
                          >
                            {analysis.youtube_url.length > 40 
                              ? `${analysis.youtube_url.substring(0, 40)}...` 
                              : analysis.youtube_url}
                          </Link>
                        </div>
                      </div>
                    )}
                    </div>

                    {/* Securely stored file - bottom of card */}
                    {analysis.analysis_type === 'file_upload' && (
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <div className="text-sm text-gray-700 bg-gray-50 p-2 rounded border">
                          <div className="flex items-center gap-2">
                            <Shield className="h-4 w-4 text-purple-600" />
                            <span>Securely stored file</span>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            Private upload - access controlled
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Analysis Results */}
        <div className="space-y-6 mb-8 col-span-full">

          {analysis.status === 'completed' && (
            <div className="col-span-full">
              {/* Strategic Advisor Section */}
              <Card className="mb-6 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      Analysis Complete - Ready for Strategic Optimization
                    </div>
                    <StrategicAdvisorButton 
                      analysisData={analysis} 
                      variant="inline"
                      className="ml-2"
                    />
                  </CardTitle>
                  <CardDescription>
                    Get personalized recommendations from our AI marketing strategist to maximize your ad performance
                  </CardDescription>
                </CardHeader>
              </Card>

              {/* Analysis Content - JSON or Markdown */}
              {analysis.marketing_analysis ? (
                <div className="max-w-none">
                  {(() => {
                    const content = typeof analysis.marketing_analysis === 'string' 
                      ? analysis.marketing_analysis 
                      : JSON.stringify(analysis.marketing_analysis, null, 2);
                    
                    // Extract JSON from content if it's wrapped in other text
                    const extractJson = (text: string) => {
                      try {
                        // First try parsing the entire content
                        JSON.parse(text);
                        return text;
                      } catch {
                        // Look for JSON object within the text
                        const jsonMatch = text.match(/\{[\s\S]*\}/);
                        if (jsonMatch) {
                          try {
                            JSON.parse(jsonMatch[0]);
                            return jsonMatch[0];
                          } catch {
                            return null;
                          }
                        }
                        return null;
                      }
                    };
                    
                    const jsonContent = extractJson(content);
                    
                    if (jsonContent) {
                      return (
                        <JsonAnalysisDisplay 
                          content={jsonContent}
                          className="analysis-cards"
                        />
                      );
                    } else {
                      // Fall back to markdown if not valid JSON
                      return (
                        <AnalysisSections 
                          content={content}
                          className="analysis-sections"
                        />
                      );
                    }
                  })()}
                </div>
              ) : (
                <Card className="bg-gray-50">
                  <CardContent className="text-center py-8">
                    <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">Analysis completed but no results found</p>
                    <p className="text-sm text-gray-500">The analysis may have encountered an issue during processing.</p>
                  </CardContent>
                </Card>
              )}

              {/* Generated Content Sections */}
              <div className="mt-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Additional Reports</h2>
                <div className="space-y-4">
                  {reportTypes.map((reportType) => {
                    const generatedReport = analysis.reports?.find(r => r.report_types?.name === reportType.name)
                    const isGenerating = generatingReport === reportType.name

                    return (
                      <Card key={reportType.name} className="w-full">
                        <CardHeader>
                          <CardTitle className="flex items-center justify-between">
                            {reportType.title}
                            <Button 
                              onClick={() => generateReport(reportType.name)}
                              disabled={isGenerating || analysis.status !== 'completed'}
                              size="sm"
                            >
                              {isGenerating ? (
                                <div className="animate-spin h-4 w-4 border-b-2 border-white rounded-full mr-2" />
                              ) : (
                                <Sparkles className="h-4 w-4 mr-2" />
                              )}
                              {generatedReport ? 'Regenerate' : 'Generate'}
                            </Button>
                          </CardTitle>
                          <CardDescription>{reportType.description}</CardDescription>
                        </CardHeader>
                        <CardContent>
                          {generatedReport ? (
                            <div className="max-w-none">
                              {(() => {
                                console.log('Debug: generatedReport.content (raw)', generatedReport.content)
                                console.log('Debug: typeof generatedReport.content', typeof generatedReport.content)
                                
                                try {
                                  // If content is a string, try to parse it as JSON
                                  let parsedContent = generatedReport.content
                                  if (typeof generatedReport.content === 'string') {
                                    parsedContent = JSON.parse(generatedReport.content)
                                    console.log('Debug: Parsed content:', parsedContent)
                                  }
                                  
                                  // Now check for raw_content in the parsed object
                                  if (parsedContent?.raw_content && typeof parsedContent.raw_content === 'string') {
                                    console.log('Debug: Found raw_content:', parsedContent.raw_content.substring(0, 100) + '...')
                                    return (
                                      <AnalysisSections 
                                        content={parsedContent.raw_content}
                                        className="analysis-sections"
                                      />
                                    )
                                  } else {
                                    console.log('Debug: No raw_content found or not a string')
                                    return (
                                      <div>
                                        <p className="text-amber-600 mb-4 text-sm font-medium">
                                          Content parsed but no raw_content found. Structure: {typeof parsedContent}
                                        </p>
                                        <pre className="text-sm text-gray-700 overflow-x-auto bg-gray-50 p-4 rounded border max-h-96">
                                          {JSON.stringify(parsedContent, null, 2)}
                                        </pre>
                                      </div>
                                    )
                                  }
                                } catch (parseError) {
                                  console.error('Debug: JSON parse error:', parseError)
                                  return (
                                    <div>
                                      <p className="text-red-600 mb-4 text-sm font-medium">
                                        Failed to parse content as JSON. Showing raw content:
                                      </p>
                                      <pre className="text-sm text-gray-700 overflow-x-auto bg-gray-50 p-4 rounded border max-h-96">
                                        {typeof generatedReport.content === 'string' ? generatedReport.content : JSON.stringify(generatedReport.content, null, 2)}
                                      </pre>
                                    </div>
                                  )
                                }
                              })()}
                            </div>
                          ) : (
                            <p className="text-gray-500">No {reportType.title.toLowerCase()} generated yet. Click &apos;Generate&apos; to create.</p>
                          )}
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </div>
            </div>
          )}


          {analysis.status === 'failed' && (
            <Card className="bg-gray-50">
              <CardHeader>
                <CardTitle className="text-red-800 flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  Analysis Failed
                </CardTitle>
                <CardDescription className="text-red-700">
                  There was an error processing your private analysis. Please try creating a new analysis.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 p-4 rounded-lg">
                  <p className="text-sm text-red-700 mb-3">
                    This could be due to video access restrictions, processing errors, or temporary service issues.
                  </p>
                  <Link href="/studio">
                    <Button variant="outline" size="sm">
                      Create New Analysis
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
      
      {/* Floating Strategic Advisor - Only show when analysis is completed */}
      {analysis?.status === 'completed' && (
        <StrategicAdvisorButton 
          analysisData={analysis} 
          variant="floating"
        />
      )}
    </div>
  )
}