import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'

interface CheckoutRequest {
  variant_id: string
  user_email: string
  user_name: string
  custom_data: {
    user_id: string
    plan: string
  }
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: CheckoutRequest = await req.json()
    const { variant_id, user_email, user_name, custom_data } = body

    if (!variant_id || !user_email) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Create checkout session with Lemon Squeezy
    const checkoutData = {
      data: {
        type: 'checkouts',
        attributes: {
          checkout_data: {
            email: user_email,
            name: user_name,
            custom: custom_data
          },
          product_options: {
            enabled_variants: [variant_id],
            redirect_url: `${process.env.NEXT_PUBLIC_APP_URL}/billing?success=true`,
            receipt_button_text: 'Go to Dashboard',
            receipt_link_url: `${process.env.NEXT_PUBLIC_APP_URL}/studio`
          },
          checkout_options: {
            embed: false,
            media: true,
            logo: true,
            desc: true,
            discount: true,
            dark: false,
            subscription_preview: true,
            button_color: '#3B82F6'
          }
        },
        relationships: {
          store: {
            data: {
              type: 'stores',
              id: process.env.LEMON_SQUEEZY_STORE_ID
            }
          },
          variant: {
            data: {
              type: 'variants',
              id: variant_id
            }
          }
        }
      }
    }

    const response = await fetch('https://api.lemonsqueezy.com/v1/checkouts', {
      method: 'POST',
      headers: {
        'Accept': 'application/vnd.api+json',
        'Content-Type': 'application/vnd.api+json',
        'Authorization': `Bearer ${process.env.LEMON_SQUEEZY_API_KEY}`
      },
      body: JSON.stringify(checkoutData)
    })

    const checkoutResponse = await response.json()

    if (!response.ok) {
      console.error('Lemon Squeezy checkout error:', checkoutResponse)
      return NextResponse.json(
        { error: 'Failed to create checkout session' },
        { status: 500 }
      )
    }

    const checkoutUrl = checkoutResponse.data.attributes.url

    return NextResponse.json({
      success: true,
      checkout_url: checkoutUrl
    })

  } catch (error) {
    console.error('Error creating checkout session:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}