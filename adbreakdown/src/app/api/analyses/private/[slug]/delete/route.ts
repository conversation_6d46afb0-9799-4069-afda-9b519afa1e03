import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { GCSService } from '@/lib/gcs'

export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { slug } = await context.params
    if (!slug) {
      return NextResponse.json(
        { error: 'Analysis slug is required' },
        { status: 400 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get the analysis to delete (ensure it belongs to the user)
    const { data: analysis, error: fetchError } = await supabase
      .from('private_analyses')
      .select(`
        id,
        slug,
        youtube_url,
        title,
        analysis_type,
        user_id
      `)
      .eq('slug', slug)
      .eq('user_id', user.id)
      .single()

    if (fetchError || !analysis) {
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      )
    }

    console.log('🗑️ Deleting private analysis:', {
      id: analysis.id,
      slug: analysis.slug,
      title: analysis.title,
      analysisType: analysis.analysis_type,
      youtubeUrl: analysis.youtube_url
    })

    // Step 1: Delete associated GCS file if it's a file upload
    let gcsDeleted = true
    if (analysis.analysis_type === 'file_upload' && analysis.youtube_url.startsWith('gs://')) {
      console.log('🗑️ Attempting to delete GCS file:', analysis.youtube_url)
      gcsDeleted = await GCSService.deleteFile(analysis.youtube_url)
      
      if (gcsDeleted) {
        console.log('✅ GCS file deleted successfully')
      } else {
        console.warn('⚠️ GCS file deletion failed, continuing with analysis deletion')
      }
    }

    // Step 2: Delete the analysis record from database
    const { error: deleteError } = await supabase
      .from('private_analyses')
      .delete()
      .eq('id', analysis.id)
      .eq('user_id', user.id) // Extra safety check

    if (deleteError) {
      console.error('❌ Failed to delete analysis from database:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete analysis' },
        { status: 500 }
      )
    }

    console.log('✅ Private analysis deleted successfully:', analysis.id)

    // Return success response with cleanup status
    return NextResponse.json({
      success: true,
      message: 'Analysis deleted successfully',
      details: {
        analysisId: analysis.id,
        title: analysis.title,
        gcsFileDeleted: gcsDeleted,
        wasFileUpload: analysis.analysis_type === 'file_upload'
      }
    })

  } catch (error) {
    console.error('❌ Error deleting private analysis:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}