import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// GET /api/analyses/private/[slug] - Get private analysis by slug
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get the private analysis by slug and ensure it belongs to the user
    const { data: analysis, error: analysisError } = await supabase
      .from('private_analyses')
      .select(`
        id,
        slug,
        youtube_url,
        youtube_video_id,
        title,
        inferred_brand,
        duration_seconds,
        thumbnail_url,
        status,
        analysis_type,
        created_at,
        updated_at,
        analysis_completed_at,
        marketing_analysis,
        optimization_recommendations,
        pre_launch_score,
        sentiment_analysis,
        target_audience,
        creative_elements,
        competitive_insights,
        performance_predictions
      `)
      .eq('slug', slug)
      .eq('user_id', user.id)
      .single()

    if (analysisError || !analysis) {
      console.log('Private analysis not found:', { slug, userId: user.id, error: analysisError })
      return NextResponse.json(
        { error: 'Private analysis not found or you do not have access to it' },
        { status: 404 }
      )
    }

    // Private analyses table only contains private analyses, so no need to check

    console.log('✅ Retrieved private analysis:', analysis.id, 'for user:', user.id)

    // Fetch associated analysis reports
    console.log('🔍 Attempting to fetch reports for private_analysis_id:', analysis.id)
    const { data: reports, error: reportsError } = await supabase
      .from('analysis_reports')
      .select(`
        id,
        report_type_id,
        status,
        content,
        generated_at,
        report_types (
          name
        )
      `)
      .eq('private_analysis_id', analysis.id) // Filter by private_analysis_id
      .eq('status', 'generated') // Only fetch generated reports

    if (reportsError) {
      console.error('❌ Error fetching private analysis reports:', reportsError)
      // Continue without reports rather than failing the whole request
    } else {
      console.log('✅ Successfully fetched reports:', reports.length, 'reports found.', reports)
    }

    return NextResponse.json({
      success: true,
      analysis: { ...analysis, reports: reports || [] } // Combine analysis and reports
    })

  } catch (error) {
    console.error('❌ Error fetching private analysis:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}