import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { 
  parseYouTubeUrl, 
  generateUniqueSlug, 
  isValidYouTubeUrl 
} from '@/lib/slug-utils'
import { validateYouTubeUrlWithAuth } from '@/lib/youtube-validator-private'
import { CacheService } from '@/lib/cache'
import { PRE_LAUNCH_ANALYSIS_PROMPT } from '@/lib/prompts/preLaunchAnalysisPrompt'
import { VertexAI } from '@google-cloud/vertexai'
import { Storage } from '@google-cloud/storage'
import { v4 as uuidv4 } from 'uuid'

// Initialize Google Cloud Storage
let storage: Storage;

try {
  // Parse the GCS private key (it's stored as full service account JSON in env var)
  const gcsCredentials = JSON.parse(process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY || '{}');
  
  storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
    credentials: gcsCredentials, // Use the full credentials object
  });
  
  console.log('✅ GCS Storage initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize GCS storage:', error);
  // Fallback initialization without credentials (will fail at runtime but allows server to start)
  storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
  });
}

const bucketName = process.env.NEXT_PUBLIC_GCS_BUCKET_NAME;

// Processing function for private analysis
async function triggerPrivateAnalysisProcessing(analysis: any, youtubeUrl: string, supabase: any) {
  
  try {
    console.log('🤖 Starting LLM processing for private analysis:', analysis.id)
    
    // Update status to 'processing' when we start
    await supabase
      .from('private_analyses')
      .update({ status: 'processing' })
      .eq('id', analysis.id)

    // Initialize Vertex AI with authentication (copied from working trigger-vertex-analysis route)
    const projectId = process.env.GOOGLE_PROJECT_ID
    const serviceAccountKey = process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY
    
    console.log('🔧 Google Cloud Authentication Check (Private):', {
      hasProjectId: !!projectId,
      hasServiceAccountKey: !!serviceAccountKey,
      serviceAccountKeyLength: serviceAccountKey?.length || 0,
      nodeEnv: process.env.NODE_ENV
    })
    
    if (!projectId) {
      console.error('❌ Missing GOOGLE_PROJECT_ID environment variable')
      throw new Error('Google Cloud Project ID not configured')
    }

    let vertex_ai: VertexAI
    
    // Check if we have service account key for production
    if (serviceAccountKey) {
      try {
        console.log('🔑 Attempting to parse service account key...')
        let credentials;

        // The service account key might be a raw JSON string or a Base64 encoded string.
        // We'll try parsing it as raw JSON first.
        try {
          credentials = JSON.parse(serviceAccountKey);
          console.log('✅ Successfully parsed raw JSON from service account key.');
        } catch (rawParseError) {
          // If raw parsing fails, assume it's Base64 encoded, which is a common and robust way to store JSON in env vars.
          console.log('⚠️ Raw JSON parse failed, attempting Base64 decoding...');
          const decodedKey = Buffer.from(serviceAccountKey, 'base64').toString('utf-8');
          credentials = JSON.parse(decodedKey);
          console.log('✅ Successfully parsed Base64 decoded service account key.');
        }
        
        vertex_ai = new VertexAI({
          project: projectId,
          location: 'us-central1',
          googleAuthOptions: {
            credentials
          }
        })
        console.log('🔐 Successfully initialized Vertex AI with service account key authentication')
      } catch (error: any) {
        console.error('❌ Failed to parse service account key (tried raw and Base64):', {
          parseError: error.message,
          keyPreview: serviceAccountKey.substring(0, 100) + '...'
        })
        throw new Error(`Invalid service account credentials: ${error.message}`)
      }
    } else {
      console.log('⚠️ No service account key found, using default authentication (this will fail in production)')
      // Use default authentication (works in dev with gcloud auth)
      vertex_ai = new VertexAI({
        project: projectId,
        location: 'us-central1',
      })
      console.log('🔐 Initialized Vertex AI with default authentication (dev only)')
    }

    // Enhanced generation config with seed for consistency and reduced temperature
    const generationConfig = {
      temperature: 0.2,        // Reduced for more focused analysis
      topP: 0.95,
      maxOutputTokens: 65535,
      seed: 42,                // Added for deterministic results
      // Note: responseMimeType cannot be used with search grounding
      // responseMimeType: 'application/json',
    };

    // Safety settings (matching working route - all off)
    const safetySettings = [
      {
        category: 'HARM_CATEGORY_HATE_SPEECH' as any,
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT' as any, 
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT' as any,
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT' as any,
        threshold: 'BLOCK_NONE' as any
      }
    ];

    // Configure Google Search grounding tool (matching working route format)
    const tools = [
      {
        googleSearch: {}
      }
    ] as any[];

    const model = vertex_ai.getGenerativeModel({ 
      model: 'gemini-2.5-pro',
      generationConfig,
      safetySettings,
      tools
    })

    console.log('📝 Using PRE_LAUNCH_ANALYSIS_PROMPT for private analysis')
    const promptContent = PRE_LAUNCH_ANALYSIS_PROMPT
    
    console.log('📊 PRIVATE ANALYSIS CONFIG:', {
      model: 'gemini-2.5-pro',
      temperature: 0.7,
      topP: 0.95,
      maxTokens: 65535,
      seed: 42,
      safetySettings: 'All disabled',
      googleSearch: 'Enabled',
      promptLength: promptContent.length,
      analysisMethod: 'Vertex AI Private Analysis with Google Search Grounding'
    })
    
    const systemInstruction = {
      role: "system",
      parts: [{ text: promptContent }],
    };

    // Handle both YouTube URLs and GCS URIs
    const videoPart = {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: youtubeUrl, // This will be either YouTube URL or GCS URI
      },
    };

    // Vertex AI requires a text part alongside a file part in multimodal requests.
    const textPart = {
      text: `Please analyze the provided video advertisement using the pre-launch analysis framework. Focus on providing actionable recommendations for optimizing this ad before it launches publicly.

**Video Information:**
- Title: ${analysis.title || 'Unknown Title'}
- Brand/Channel: ${analysis.inferred_brand || 'Unknown Brand'}
- Duration: ${analysis.duration_seconds || 0} seconds
- YouTube URL: ${analysis.youtube_url}
- Video ID: ${analysis.youtube_video_id}`
    };

    console.log('🚀 Sending request to Vertex AI for private analysis')

    // Generate the analysis using proper multimodal request format
    const result = await model.generateContent({
      contents: [{ role: "user", parts: [textPart, videoPart] }],
      systemInstruction
      // generationConfig and safetySettings are already configured in the model
    });

    const response = result.response;

    // Extract text response (matching working route pattern)
    if (!response.candidates?.length || !response.candidates[0].content?.parts[0]?.text) {
      console.error("❌ Invalid response structure from Vertex AI:", JSON.stringify(response, null, 2));
      throw new Error("Received an invalid or empty text response from Vertex AI.");
    }

    const analysisText = response.candidates[0].content.parts[0].text;

    console.log('✅ Received analysis from Vertex AI, length:', analysisText.length)

    // Try to extract metadata from the JSON response
    let extractedBrand = null
    let extractedDuration = null
    
    try {
      const jsonMatch = analysisText.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsedResponse = JSON.parse(jsonMatch[0])
        if (parsedResponse.ad_context?.metadata) {
          const metadata = parsedResponse.ad_context.metadata
          
          // Extract brand
          if (metadata.brand && typeof metadata.brand === 'string' && metadata.brand.trim() !== '') {
            extractedBrand = metadata.brand.trim()
            console.log('📊 Extracted brand from LLM metadata:', extractedBrand)
          }
          
          // Extract duration - convert from string to number if needed
          if (metadata.duration) {
            // Handle duration in seconds or MM:SS format
            let durationStr = metadata.duration.toString().trim()
            if (durationStr.includes(':')) {
              // Convert MM:SS to seconds
              const parts = durationStr.split(':')
              const minutes = parseInt(parts[0] || '0')
              const seconds = parseInt(parts[1] || '0')
              if (!isNaN(minutes) && !isNaN(seconds)) {
                extractedDuration = (minutes * 60) + seconds
              }
            } else {
              // Try parsing as number of seconds
              const duration = parseInt(durationStr)
              if (!isNaN(duration) && duration > 0) {
                extractedDuration = duration
              }
            }
            if (extractedDuration) {
              console.log('⏱️ Extracted duration from LLM metadata:', extractedDuration, 'seconds')
            }
          }
        }
      }
    } catch (parseError) {
      console.log('⚠️ Could not extract metadata from LLM response:', parseError)
    }

    // Prepare update data
    const updateData: any = {
      marketing_analysis: analysisText,
      status: 'completed',
      analysis_completed_at: new Date().toISOString()
    }
    
    // Update brand if extracted from LLM and current is generic or empty
    if (extractedBrand && (!analysis.inferred_brand || analysis.inferred_brand === 'Unknown Brand' || analysis.inferred_brand === 'Unknown')) {
      updateData.inferred_brand = extractedBrand
      console.log('🏷️ Updating brand from LLM metadata:', extractedBrand)
    }
    
    // Update duration if extracted from LLM and current is 0 or missing
    if (extractedDuration && (!analysis.duration_seconds || analysis.duration_seconds === 0)) {
      updateData.duration_seconds = extractedDuration
      console.log('📏 Updating duration from LLM metadata:', extractedDuration, 'seconds')
    }

    // Update the private analysis record with results
    const { error: updateError } = await supabase
      .from('private_analyses')
      .update(updateData)
      .eq('id', analysis.id)

    if (updateError) {
      throw new Error(`Failed to save analysis results: ${updateError.message}`)
    }

    console.log('✅ Private analysis completed successfully:', analysis.id)

  } catch (error) {
    console.error('❌ Error in private analysis processing:', error)
    
    // Mark as failed
    await supabase
      .from('private_analyses')
      .update({ 
        status: 'failed'
      })
      .eq('id', analysis.id)
    
    throw error
  }
}

// POST /api/analyses/private - Create private analysis
export async function POST(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Use service role client for user operations (bypasses RLS)
    const supabase = createServerSupabaseClient()

    // Get user from database
    console.log('🔍 Looking for user with Clerk ID:', userId)
    let { data: user, error: userError } = await supabase
      .from('users')
      .select('id, clerk_id')
      .eq('clerk_id', userId)
      .single()
      
    console.log('🔍 User query result:', { user, userError })

    if (userError || !user) {
      console.log('🔧 User not found in database, creating user for Clerk ID:', userId)
      
      // Try to create user automatically
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          clerk_id: userId,
          email: '<EMAIL>',
          first_name: 'User',
          last_name: 'Auto'
        })
        .select()
        .single()

      if (createError) {
        console.error('❌ Error creating user:', createError)
        return NextResponse.json(
          { error: 'Failed to create user account' },
          { status: 500 }
        )
      }

      user = newUser
      console.log('✅ Created new user:', user)
    }

    // Skip credit check for now (credits column doesn't exist)
    console.log('💳 Skipping credit check - credits system not implemented yet')

    // Check if this is a file upload, GCS URI, or YouTube URL request
    const contentType = req.headers.get('content-type')
    let isFileUpload = false
    let isGcsUpload = false
    let youtubeUrl = ''
    let gcsUri = ''
    let uploadData: { brand?: string; title?: string; file?: File } = {}

    if (contentType?.includes('application/json')) {
      // Handle JSON request (could be YouTube URL or pre-uploaded GCS URI)
      const jsonData = await req.json()
      
      if (jsonData.gcsUri) {
        // Handle pre-uploaded GCS URI
        isGcsUpload = true
        gcsUri = jsonData.gcsUri
        uploadData = {
          brand: jsonData.brand,
          title: jsonData.title
        }
        console.log('📁 Processing pre-uploaded GCS URI:', { gcsUri, brand: uploadData.brand, title: uploadData.title })
      } else {
        // Handle YouTube URL
        youtubeUrl = jsonData.youtubeUrl
        console.log('🎥 Processing YouTube URL:', youtubeUrl)
      }
    } else if (contentType?.includes('multipart/form-data')) {
      // Handle file upload
      isFileUpload = true
      
      if (!bucketName) {
        return NextResponse.json({ error: 'NEXT_PUBLIC_GCS_BUCKET_NAME is not configured' }, { status: 500 });
      }

      // Validate GCS credentials are available
      if (!process.env.GOOGLE_PROJECT_ID || !process.env.VERTEX_AI_SERVICE_ACCOUNT_EMAIL || !process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY) {
        console.error('❌ Missing GCS credentials in environment variables');
        return NextResponse.json({ error: 'Storage credentials not configured' }, { status: 500 });
      }

      const formData = await req.formData()
      const brand = formData.get('brand') as string
      const title = formData.get('title') as string
      const file = formData.get('file') as File

      if (!brand || !title || !file) {
        return NextResponse.json(
          { error: 'Brand, title, and file are required for file upload' },
          { status: 400 }
        )
      }

      // Validate file type
      const allowedTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov', 'video/wmv']
      if (!allowedTypes.includes(file.type)) {
        return NextResponse.json(
          { error: 'Invalid file type. Please upload a valid video file (MP4, WebM, OGG, AVI, MOV, WMV)' },
          { status: 400 }
        )
      }

      // Validate file size (100MB limit)
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (file.size > maxSize) {
        return NextResponse.json({ error: 'File size must be under 100MB' }, { status: 400 })
      }

      uploadData = { brand, title, file }
    }

    let validation: any = {}
    
    if (isGcsUpload) {
      // Handle pre-uploaded GCS URI - create validation data
      const filename = gcsUri.split('/').pop() || 'uploaded-file'
      // Extract UUID pattern if present, otherwise use a hash of the filename
      const uuidMatch = filename.match(
        /^([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i
      )
      const shortVideoId = uuidMatch
        ? uuidMatch[1].substring(0, 18)
        : filename.substring(0, 18).padEnd(18, '0')
      
      validation = {
        isValid: true,
        videoId: shortVideoId,
        normalizedUrl: gcsUri,
        metadata: {
          title: uploadData.title || filename,
          channelTitle: uploadData.brand || 'Unknown Brand',
          thumbnailUrl: null
        },
        duration: { seconds: 0 } // We don't know duration until processing
      }
      
      console.log('✅ Pre-uploaded GCS file validation:', validation)
    } else if (isFileUpload) {
      // Handle file upload to GCS
      console.log('📁 Processing file upload for private analysis')
      
      try {
        // Generate unique filename and upload to GCS
        const uniqueFileName = `${uuidv4()}-${uploadData.file!.name}`
        const bucket = storage.bucket(bucketName!)
        
        // Check if bucket exists and create if it doesn't
        try {
          const [exists] = await bucket.exists()
          if (!exists) {
            console.log('📦 Creating GCS bucket:', bucketName)
            await bucket.create({
              location: 'US-CENTRAL1', // Free tier region
              storageClass: 'STANDARD',
            })
          }
        } catch (bucketError: any) {
          console.warn('⚠️ Could not check/create bucket:', bucketError.message)
          // Continue anyway - maybe we have access but not bucket creation permissions
        }
        
        const gcsFile = bucket.file(uniqueFileName)
        
        console.log('📤 Starting GCS upload:', {
          fileName: uniqueFileName,
          size: uploadData.file!.size,
          type: uploadData.file!.type,
          bucket: bucketName,
          serviceAccount: process.env.VERTEX_AI_SERVICE_ACCOUNT_EMAIL
        });
        
        // Convert File to Buffer
        const arrayBuffer = await uploadData.file!.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)

        // Upload with retry logic for IAM propagation delays
        let uploadSuccess = false;
        let lastError: any = null;
        const maxRetries = 3;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            console.log(`📤 Upload attempt ${attempt}/${maxRetries}`);
            
            await gcsFile.save(buffer, {
              metadata: {
                contentType: uploadData.file!.type,
              },
            });
            
            uploadSuccess = true;
            console.log('✅ GCS upload successful');
            break;
            
          } catch (uploadError: any) {
            lastError = uploadError;
            
            if (uploadError.code === 403 && attempt < maxRetries) {
              console.log(`⏳ Permission denied (attempt ${attempt}), retrying in 2 seconds...`);
              await new Promise(resolve => setTimeout(resolve, 2000));
            } else {
              console.error('❌ GCS upload error details:', {
                attempt,
                error: uploadError.message,
                code: uploadError.code,
                status: uploadError.status,
                serviceAccount: process.env.VERTEX_AI_SERVICE_ACCOUNT_EMAIL,
                bucket: bucketName,
                projectId: process.env.GOOGLE_PROJECT_ID
              });
            }
          }
        }
        
        if (!uploadSuccess) {
          throw lastError;
        }

        gcsUri = `gs://${bucketName}/${uniqueFileName}`
        
        // Set validation data for file upload
        // Extract just the UUID part for shorter video ID (max 20 chars for DB)
        const shortVideoId = uniqueFileName.split('-')[0].substring(0, 18) // Just the first UUID part
        
        validation = {
          isValid: true,
          videoId: shortVideoId,
          normalizedUrl: gcsUri,
          metadata: {
            title: uploadData.title,
            channelTitle: uploadData.brand,
            thumbnailUrl: null
          },
          duration: { seconds: 0 } // We don't know duration until processing
        }
        
        console.log('✅ File uploaded to GCS:', gcsUri)
      } catch (uploadError: any) {
        console.error('❌ GCS upload failed:', uploadError)
        return NextResponse.json({
          error: `File upload failed: ${uploadError.message || 'Unknown error'}`
        }, { status: 500 })
      }
    } else if (youtubeUrl) {
      // Handle YouTube URL validation
      if (!isValidYouTubeUrl(youtubeUrl)) {
        return NextResponse.json(
          { error: 'Invalid YouTube URL format' },
          { status: 400 }
        )
      }

      // Get user's OAuth tokens for YouTube access
      let authToken = null
      try {
        const { getUserYouTubeToken } = await import('@/lib/youtube-validator-private')
        authToken = await getUserYouTubeToken(user?.id)
        console.log('🔑 Retrieved OAuth token for private analysis:', authToken ? 'Found' : 'Not found')
      } catch (tokenError) {
        console.warn('⚠️ Could not retrieve OAuth token:', tokenError)
        // Continue without token - will use API key
      }
      
      // Validate YouTube URL with OAuth token (if available)
      console.log('🔍 Validating YouTube URL:', youtubeUrl)
      validation = await validateYouTubeUrlWithAuth(youtubeUrl, authToken)
      console.log('✅ Validation result:', { isValid: validation.isValid, error: validation.error })
      
      if (!validation.isValid) {
        if (validation.error?.includes('OAuth')) {
          return NextResponse.json(
            { 
              error: 'Authentication required',
              message: 'Please connect your Google account to analyze this video.',
              requiresAuth: true
            },
            { status: 401 }
          )
        }
        
        if (validation.error?.includes('duration')) {
          return NextResponse.json(
            { 
              error: validation.error,
              metadata: validation.metadata,
              duration: validation.duration,
              videoId: validation.videoId
            },
            { status: 413 } // Request Entity Too Large
          )
        }

        return NextResponse.json(
          { error: validation.error },
          { status: 400 }
        )
      }
    } else {
      // No valid input provided
      return NextResponse.json(
        { error: 'Either YouTube URL or GCS URI is required' },
        { status: 400 }
      )
    }

    // Check if user already has a private analysis for this video (only for YouTube)
    if (youtubeUrl && !isFileUpload && !isGcsUpload) {
      const { data: existingAnalysis } = await supabase
        .from('private_analyses')
        .select('id, slug')
        .eq('youtube_video_id', validation.videoId)
        .eq('user_id', user?.id)
        .single()

      if (existingAnalysis) {
        return NextResponse.json(
          {
            message: 'Private analysis already exists for this video',
            analysis: {
              id: existingAnalysis.id,
              slug: existingAnalysis.slug
            },
            redirect: `/studio/private/${existingAnalysis.slug}`
          },
          { status: 200 }
        )
      }
    }

    // Generate unique slug (ensure it fits database constraints)
    const slugBase = (isFileUpload || isGcsUpload)
      ? `${uploadData.brand!.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${uploadData.title!.toLowerCase().replace(/[^a-z0-9]/g, '-')}`
      : `${validation.metadata?.channelTitle || 'unknown'}-${validation.metadata?.title || 'private-analysis'}`.toLowerCase().replace(/[^a-z0-9]/g, '-')
    
    // Limit slug to reasonable length (assume 100 char limit to be safe)
    const timestamp = Date.now().toString()
    const maxSlugLength = 90 // Leave room for timestamp
    const truncatedSlugBase = slugBase.length > maxSlugLength ? slugBase.substring(0, maxSlugLength) : slugBase
    const slug = `${truncatedSlugBase}-${timestamp}`

    // Apply defensive truncation to prevent database constraint violations
    const safeYoutubeUrl = (validation.normalizedUrl || youtubeUrl).substring(0, 500) // Limit to 500 chars
    const safeTitle = (validation.metadata?.title || 'Private Analysis').substring(0, 200) // Limit to 200 chars
    const safeBrand = (validation.metadata?.channelTitle || 'Unknown').substring(0, 100) // Limit to 100 chars

    console.log('📝 Creating private analysis with data:', {
      user_id: user?.id,
      youtube_url: safeYoutubeUrl,
      youtube_video_id: validation.videoId,
      slug,
      title: safeTitle,
      analysis_type: isFileUpload ? 'file_upload' : 'youtube_url'
    })

    // Create private analysis record in dedicated table
    const { data: analysis, error: insertError } = await supabase
      .from('private_analyses')
      .insert({
        user_id: user?.id,
        youtube_url: safeYoutubeUrl,
        youtube_video_id: validation.videoId,
        slug,
        title: safeTitle,
        inferred_brand: safeBrand,
        duration_seconds: validation.duration?.seconds || 0,
        thumbnail_url: validation.metadata?.thumbnailUrl,
        status: 'pending',
        analysis_type: (isFileUpload || isGcsUpload) ? 'file_upload' : 'youtube_url'
      })
      .select()
      .single()

    if (insertError) {
      console.error('❌ Error creating private analysis:', {
        error: insertError,
        message: insertError.message,
        code: insertError.code,
        details: insertError.details,
        hint: insertError.hint,
        insertData: {
          user_id: user?.id,
          youtube_url: safeYoutubeUrl,
          youtube_video_id: validation.videoId,
          slug,
          title: safeTitle,
          inferred_brand: safeBrand,
          duration_seconds: validation.duration?.seconds || 0,
          thumbnail_url: validation.metadata?.thumbnailUrl,
          status: 'pending',
          analysis_type: (isFileUpload || isGcsUpload) ? 'file_upload' : 'youtube_url'
        }
      })
      return NextResponse.json(
        { 
          error: 'Failed to create private analysis', 
          details: insertError.message || 'Database error'
        },
        { status: 500 }
      )
    }

    // Skip credit deduction for now (credits system not implemented)
    console.log('💳 Skipping credit deduction - credits system not implemented yet')

    console.log('✅ Created private analysis:', analysis)

    // Clear relevant caches (skip if cache not available)
    try {
      await Promise.all([
        CacheService.delete(`user-analyses-${user?.id}`),
        CacheService.delete(`user-stats-${user?.id}`)
      ])
    } catch (cacheError) {
      console.log('⚠️ Cache not available, skipping cache clear:', cacheError instanceof Error ? cacheError.message : 'Unknown error')
    }

    // Return success response immediately to redirect user
    const responseData = {
      success: true,
      analysis: {
        id: analysis.id,
        slug: analysis.slug,
        title: analysis.title,
        status: 'pending' // Status will be updated when processing starts
      },
      redirect: `/studio/private/${analysis.slug}`,
      message: 'Private analysis created successfully'
    }
    
    // Step 2: Trigger private analysis processing asynchronously (non-blocking)
    console.log('🚀 Step 2: Triggering private analysis processing for ID:', analysis.id)
    // Use setTimeout to make this truly async and non-blocking
    setTimeout(async () => {
      try {
        // Use the appropriate URL for processing (GCS URI for uploads, YouTube URL for YouTube videos)
        const processUrl = (isFileUpload || isGcsUpload) ? gcsUri : youtubeUrl
        await triggerPrivateAnalysisProcessing(analysis, processUrl, supabase)
        console.log('✅ Private analysis processing completed successfully')
      } catch (triggerError) {
        console.warn('⚠️ Error in private analysis processing:', triggerError)
        // Mark as failed 
        try {
          const { error: updateError } = await supabase
            .from('private_analyses')
            .update({ 
              status: 'failed'
            })
            .eq('id', analysis.id)
          
          if (updateError) {
            console.error('Failed to mark as failed:', updateError)
          }
        } catch (dbError) {
          console.error('Database error while marking as failed:', dbError)
        }
      }
    }, 100) // Small delay to ensure response is sent first

    return NextResponse.json(responseData)

  } catch (error) {
    console.error('❌ Error in private analysis creation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/analyses/private - Get user's private analyses
export async function GET(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get user's private analyses
    const { data: analyses, error: analysesError } = await supabase
      .from('private_analyses')
      .select(`
        id,
        slug,
        youtube_url,
        youtube_video_id,
        title,
        inferred_brand,
        status,
        duration_seconds,
        thumbnail_url,
        pre_launch_score,
        created_at,
        analysis_completed_at,
        analysis_type
      `)
      .eq('user_id', user?.id)
      .order('created_at', { ascending: false })

    if (analysesError) {
      console.error('❌ Error fetching private analyses:', analysesError)
      return NextResponse.json(
        { error: 'Failed to fetch private analyses' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      analyses: analyses || [],
      total: analyses?.length || 0
    })

  } catch (error) {
    console.error('❌ Error in private analyses fetch:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '100mb'
    }
  }
}