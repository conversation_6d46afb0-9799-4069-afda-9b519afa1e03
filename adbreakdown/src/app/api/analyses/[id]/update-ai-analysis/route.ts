import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { generateUniqueSlug, extractYouTubeVideoId } from '@/lib/slug-utils'
import { CacheService } from '@/lib/cache'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params
    const { id } = await params
    
    const supabase = createServerSupabaseClient()
    const { initialAnalysis, marketingAnalysis } = await request.json()
    
    console.log('🔨 Updating analysis:', id)
    console.log('🔨 Initial analysis data:', initialAnalysis)

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)

    // Get the current analysis to extract YouTube URL and existing slug
    const { data: analysisData, error: fetchError } = await supabase
      .from('ad_analyses')
      .select('id, youtube_url, youtube_video_id, slug')
      .eq(isUUID ? 'id' : 'slug', id)
      .single()

    if (fetchError) {
      console.error('❌ Error fetching analysis:', fetchError)
      throw fetchError
    }

    // Extract video ID if not already stored
    let videoId = analysisData.youtube_video_id
    if (!videoId) {
      videoId = extractYouTubeVideoId(analysisData.youtube_url)
    }

    // Handle different update scenarios based on what data we have
    let updateData: any = {
      status: 'completed',
      marketing_analysis: marketingAnalysis,
      youtube_video_id: videoId,
      analysis_completed_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // If we have initial analysis data (step 1), include all the detailed fields
    if (initialAnalysis) {
      // Generate new slug with brand and title information
      const newSlug = await generateUniqueSlug(
        initialAnalysis.inferredBrandName,
        initialAnalysis.title,
        videoId || 'unknown',
        supabase
      )

      console.log('🏷️ Generated new slug:', newSlug)

      updateData = {
        ...updateData,
        slug: newSlug,
        title: initialAnalysis.title,
        duration_seconds: parseDuration(initialAnalysis.duration),
        duration_formatted: initialAnalysis.duration,
        thumbnail_url: initialAnalysis.thumbnail_url || generateYouTubeThumbnail(analysisData.youtube_url),
        overall_sentiment: initialAnalysis.overallSentiment,
        inferred_brand: initialAnalysis.inferredBrandName,
        transcript: initialAnalysis.transcript,
        summary: initialAnalysis.summary,
        video_info: initialAnalysis.transcript + "\n\n**Summary:**\n" + initialAnalysis.summary,
        emotions: initialAnalysis.emotions,
        key_themes: initialAnalysis.keyThemes,
        music_mood: initialAnalysis.musicMood,
        voice_tone: initialAnalysis.voiceTone,
        // Store additional AI-generated data for authentic display
        visual_appeal: initialAnalysis.visualAppeal,
        audio_quality: initialAnalysis.audioQuality,
        color_palette: initialAnalysis.colorPalette,
        sentiment_timeline: initialAnalysis.sentimentTimeline,
        target_demographics: initialAnalysis.targetingRecommendations?.demographics,
        target_interests: initialAnalysis.targetingRecommendations?.interests,
        target_behaviors: initialAnalysis.targetingRecommendations?.behaviors,
        // NEW: Populate dedicated metadata columns for filtering
        brand: initialAnalysis.inferredBrandName,
        product_category: initialAnalysis.productCategory,
        parent_entity: initialAnalysis.parentCompany,
        campaign_category: initialAnalysis.campaignCategory,
        geography: initialAnalysis.geography,
        agency: initialAnalysis.agency,
        launch_date: initialAnalysis.launchDate ? new Date(initialAnalysis.launchDate).toISOString() : null,
      }
    } else {
      // If no initial analysis (skipping step 1), extract data from marketing analysis
      console.log('🧠 Extracting fields from marketing analysis...')
      
      let extractedBrand = 'Unknown Brand'
      let extractedTitle = `Video Analysis ${videoId || 'Unknown'}`
      
      // Initialize marketingData outside try-catch so it's accessible later
      let marketingData: any = null
      
      try {
        // Try to parse marketing analysis to extract brand and other info
        if (typeof marketingAnalysis === 'string') {
          // Clean up the string by removing markdown code blocks
          let cleanedString = marketingAnalysis.trim()
          if (cleanedString.startsWith('```json')) {
            cleanedString = cleanedString.replace(/^```json\s*/, '').replace(/\s*```$/, '')
          } else if (cleanedString.startsWith('```')) {
            cleanedString = cleanedString.replace(/^```\s*/, '').replace(/\s*```$/, '')
          }
          cleanedString = cleanedString.replace(/^`+|`+$/g, '').trim()
          
          // Find the first { and last } to extract the JSON object (same logic as frontend)
          const firstBrace = cleanedString.indexOf('{')
          const lastBrace = cleanedString.lastIndexOf('}')
          
          if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
            cleanedString = cleanedString.substring(firstBrace, lastBrace + 1)
          }
          
          if (cleanedString) {
            marketingData = JSON.parse(cleanedString)
          }
        } else if (typeof marketingAnalysis === 'object') {
          marketingData = marketingAnalysis
        }

        // Extract brand name from marketing analysis
        if (marketingData?.lightning_round?.brand) {
          extractedBrand = marketingData.lightning_round.brand
        } else if (marketingData?.metadata?.brand) {
          extractedBrand = marketingData.metadata.brand
        } else if (marketingData?.brand) {
          extractedBrand = marketingData.brand
        }

        // Create a meaningful title from brand and product category
        if (marketingData?.lightning_round?.product_category) {
          extractedTitle = `${extractedBrand} - ${marketingData.lightning_round.product_category} Ad`
        } else if (marketingData?.metadata?.ad_title) {
          extractedTitle = marketingData.metadata.ad_title
        } else if (extractedBrand !== 'Unknown Brand') {
          extractedTitle = `${extractedBrand} Video Ad Analysis`
        }

        console.log('🏷️ Extracted brand:', extractedBrand)
        console.log('🏷️ Extracted title:', extractedTitle)
        
      } catch (error) {
        console.warn('⚠️ Could not parse marketing analysis for field extraction:', error)
        // marketingData remains null, which is fine - we'll use fallback values
      }

      // Generate slug with extracted data
      const basicSlug = await generateUniqueSlug(
        extractedBrand,
        extractedTitle,
        videoId || 'unknown',
        supabase
      )

      console.log('🏷️ Generated slug from extracted data:', basicSlug)

      updateData = {
        ...updateData,
        slug: basicSlug,
        title: marketingData?.metadata?.ad_title || extractedTitle, // Use ad_title from metadata if available
        thumbnail_url: generateYouTubeThumbnail(analysisData.youtube_url),
        inferred_brand: marketingData?.metadata?.brand || extractedBrand, // Use brand from metadata if available
        // Set minimal placeholder data for fields that are expected
        transcript: 'Step 1 analysis skipped - Data extracted from marketing analysis',
        summary: 'Direct marketing analysis performed with field extraction',
        video_info: 'Step 1 analysis skipped - Direct marketing analysis performed with AI field extraction',
        // NEW: Populate dedicated metadata columns from extracted marketing data
        brand: marketingData?.metadata?.brand || extractedBrand,
        product_category: marketingData?.metadata?.product_category,
        parent_entity: marketingData?.metadata?.parent_entity,
        campaign_category: marketingData?.metadata?.campaign_category,
        geography: marketingData?.metadata?.geography,
        agency: marketingData?.metadata?.agency,
        launch_date: marketingData?.metadata?.launch_date ? new Date(marketingData.metadata.launch_date).toISOString() : null,
      }
    }
    
    // Update analysis status to completed with the appropriate data
    const { error: updateError } = await supabase
      .from('ad_analyses')
      .update(updateData)
      .eq('id', analysisData.id)

    if (updateError) {
      console.error('❌ Error updating analysis:', updateError)
      throw updateError
    }

    // Invalidate all caches after updating analysis
    await CacheService.invalidateAnalysis(analysisData.id)
    await CacheService.invalidatePattern(`private:analysis:${id}:*`)
    await CacheService.invalidatePattern(`private:analysis:${analysisData.id}:*`)
    await CacheService.invalidatePattern(`public:analysis:${id}`)
    await CacheService.invalidatePattern(`public:analysis:${analysisData.id}`)
    
    console.log('✅ Analysis updated successfully and caches invalidated')
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('❌ Update AI analysis error:', error)
    return NextResponse.json(
      { error: 'Failed to update AI analysis' },
      { status: 500 }
    )
  }
}


function parseDuration(duration: string): number {
  const [minutes, seconds] = duration.split(':').map(Number)
  return (minutes * 60) + seconds
}

function generateYouTubeThumbnail(youtubeUrl: string): string {
  try {
    // Extract video ID from YouTube URL
    const videoId = youtubeUrl.split('v=')[1]?.split('&')[0]
    if (videoId) {
      return `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`
    }
  } catch (error) {
    console.error('Error generating YouTube thumbnail:', error)
  }
  return 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail'
}