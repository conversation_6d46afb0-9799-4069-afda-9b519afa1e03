import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import CacheService from '@/lib/cache'

// GET /api/analyses/{id}/basic - Get basic analysis info for above-the-fold content (supports public access)
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    
    // Try cache first for public analyses
    const cacheKey = `basic:${id}`
    const cached = await CacheService.getPublicAnalysis(cacheKey)
    if (cached) {
      console.log('💨 Serving cached basic analysis:', id)
      return NextResponse.json(cached)
    }
    
    const supabase = createServerSupabaseClient()
    
    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    console.log(`🔍 [Basic] Looking up analysis: ${id} (${isUUID ? 'UUID' : 'slug'})`)
    
    // First, check if analysis exists at all (public or private)
    const { data: anyAnalysis, error: anyError } = await supabase
      .from('ad_analyses')
      .select('id, slug, is_public, user_id')
      .eq(isUUID ? 'id' : 'slug', id)
      .single()

    // If analysis doesn't exist at all
    if (!anyAnalysis || anyError) {
      console.log(`❌ [Basic] Analysis not found: ${id}`, anyError)
      return NextResponse.json(
        { error: `Analysis not found for ${isUUID ? 'ID' : 'slug'}: ${id}` },
        { status: 404 }
      )
    }
    
    console.log(`✅ [Basic] Found analysis: ${anyAnalysis.id} (public: ${anyAnalysis.is_public})`)
    
    // If it's public, serve without authentication
    if (anyAnalysis.is_public) {
      const { data: publicAnalysis, error: publicError } = await supabase
        .from('ad_analyses')
        .select(`
          id,
          slug,
          status,
          title,
          inferred_brand,
          duration_formatted,
          thumbnail_url,
          youtube_url,
          created_at,
          updated_at,
          is_public
        `)
        .eq('id', anyAnalysis.id)
        .single()
        
      if (publicAnalysis && !publicError) {
        console.log('📖 [Basic] Serving public analysis:', publicAnalysis.id)
        const response = {
          ...publicAnalysis,
          video_url: publicAnalysis.youtube_url,
          video_title: publicAnalysis.title,
          video_thumbnail_url: publicAnalysis.thumbnail_url
        }
        
        // Cache the public analysis for fast future access
        await CacheService.setPublicAnalysis(cacheKey, response)
        
        return NextResponse.json(response)
      }
    }

    // If not public, require authentication for private analysis
    const { userId } = await auth()
    if (!userId) {
      console.log(`🔒 [Basic] Private analysis requires authentication: ${id}`)
      return NextResponse.json(
        { error: 'This analysis is private. Please sign in to view it.' },
        { status: 401 }
      )
    }

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    // Check if user owns this private analysis
    if (anyAnalysis.user_id !== user.id) {
      console.log(`🚫 [Basic] Access denied: Analysis ${id} belongs to different user`)
      return NextResponse.json(
        { error: 'Access denied. This analysis belongs to another user.' },
        { status: 403 }
      )
    }
    
    // Get the full private analysis data
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select(`
        id,
        slug,
        status,
        title,
        inferred_brand,
        duration_formatted,
        thumbnail_url,
        youtube_url,
        created_at,
        updated_at,
        is_public
      `)
      .eq('id', anyAnalysis.id)
      .single()

    if (analysisError || !analysis) {
      console.log(`❌ [Basic] Error fetching full analysis data: ${id}`, analysisError)
      return NextResponse.json(
        { error: 'Failed to load analysis data' },
        { status: 500 }
      )
    }

    console.log('✅ [Basic] Serving private analysis:', analysis.id)
    return NextResponse.json({
      ...analysis,
      video_url: analysis.youtube_url,
      video_title: analysis.title,
      video_thumbnail_url: analysis.thumbnail_url
    })
  } catch (error) {
    console.error('Error in GET /api/analyses/[id]/basic:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}