import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { CacheService } from '@/lib/cache'

interface RouteParams {
  params: {
    id: string
  }
}

// Helper function to serve analysis data (both public and private)
async function serveAnalysisData(supabase: any, analysis: any, isPublic: boolean = false, currentUserId?: string) {
  // Get analysis reports with report type information
  const { data: reports, error: reportsError } = await supabase
    .from('analysis_reports')
    .select(`
      id,
      report_type_id,
      status,
      content,
      generated_at,
      created_at,
      report_types (
        name,
        description
      )
    `)
    .eq('analysis_id', analysis.id)

  if (reportsError) {
    console.error('Error fetching reports:', reportsError)
    // Continue without reports rather than failing
  }

  // Get emotion timeline data if it exists
  const { data: emotionTimelineData, error: emotionError } = await supabase
    .from('sentiment_analyses')
    .select(`
      analysis_id,
      primary_audience_emotion,
      analysis_summary,
      analysis_timestamp,
      emotion_timeline_events (
        event_id,
        start_time_seconds,
        end_time_seconds,
        audience_emotion,
        intensity,
        event_description,
        actor_emotion
      )
    `)
    .eq('ad_analysis_id', analysis.id)
    .single()

  if (emotionError && emotionError.code !== 'PGRST116') {
    console.error('Error fetching emotion timeline data:', emotionError)
  }

  // Add emotion timeline to reports array if it exists
  let allReports = reports || []
  if (emotionTimelineData && emotionTimelineData.emotion_timeline_events?.length > 0) {
    const emotionTimelineReport = {
      id: `emotion-timeline-${emotionTimelineData.analysis_id}`,
      report_type_id: 'a8f8b8e0-9b1f-4f8c-8e2a-1b3b4c5d6e7f', // emotion_timeline report type ID
      status: 'generated',
      content: {
        analysisSummary: emotionTimelineData.analysis_summary,
        primaryAudienceEmotion: emotionTimelineData.primary_audience_emotion,
        timeline: emotionTimelineData.emotion_timeline_events,
        sentiment_analysis_id: emotionTimelineData.analysis_id
      },
      generated_at: emotionTimelineData.analysis_timestamp,
      created_at: emotionTimelineData.analysis_timestamp,
      report_types: {
        name: 'emotion_timeline',
        description: 'Generates a detailed, timeline-based analysis of audience and actor emotions.'
      } as any
    }
    allReports.push(emotionTimelineReport)
  }

  console.log(isPublic ? '📖 Serving public analysis:' : '✅ Returning private analysis:', {
    analysisId: analysis.id,
    status: analysis.status,
    hasReports: allReports?.length || 0,
    hasEmotionTimeline: !!emotionTimelineData,
    isPublic
  })

  // Ensure required fields have default values for frontend compatibility
  const analysisResponse = {
    ...analysis,
    video_url: analysis.youtube_url, // Frontend expects video_url, DB has youtube_url
    video_title: analysis.title || 'Video Analysis',
    video_thumbnail_url: analysis.thumbnail_url || 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail',
    video_duration: analysis.duration_seconds || 0,
    reports: allReports,
    is_public: isPublic, // Include public status in response
    is_owner: currentUserId ? analysis.user_id === currentUserId : false // Check if current user owns this analysis
  }

  return NextResponse.json(analysisResponse)
}

// GET /api/analyses/{id} - Get analysis details with reports (supports public access)
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Await params
    const { id } = await params
    
    // Use service role client for database operations
    const supabase = createServerSupabaseClient()
    
    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    console.log(`🔍 Looking up analysis: ${id} (${isUUID ? 'UUID' : 'slug'})`)
    
    // First, check if analysis exists at all (public or private)
    const { data: anyAnalysis, error: anyError } = await supabase
      .from('ad_analyses')
      .select('id, slug, is_public, user_id')
      .eq(isUUID ? 'id' : 'slug', id)
      .single()

    // If analysis doesn't exist at all
    if (!anyAnalysis || anyError) {
      console.log(`❌ Analysis not found: ${id}`, anyError)
      return NextResponse.json(
        { error: `Analysis not found for ${isUUID ? 'ID' : 'slug'}: ${id}` },
        { status: 404 }
      )
    }
    
    console.log(`✅ Found analysis: ${anyAnalysis.id} (public: ${anyAnalysis.is_public})`)
    
    // If it's public, serve without authentication
    if (anyAnalysis.is_public) {
      const { data: publicAnalysis, error: publicError } = await supabase
        .from('ad_analyses')
        .select('*')
        .eq('id', anyAnalysis.id)
        .single()
        
      if (publicAnalysis && !publicError) {
        console.log('📖 Serving public analysis:', publicAnalysis.id)
        // For public analyses, still try to get current user to check ownership
        const { userId: currentUserId } = await auth()
        let currentUserDbId = null
        if (currentUserId) {
          const { data: currentUser } = await supabase
            .from('users')
            .select('id')
            .eq('clerk_id', currentUserId)
            .single()
          currentUserDbId = currentUser?.id
        }
        return await serveAnalysisData(supabase, publicAnalysis, true, currentUserDbId)
      }
    }

    // If not public, require authentication for private analysis
    const { userId } = await auth()
    if (!userId) {
      console.log(`🔒 Private analysis requires authentication: ${id}`)
      return NextResponse.json(
        { error: 'This analysis is private. Please sign in to view it.' },
        { status: 401 }
      )
    }

    // Get user from database
    console.log('🔍 Looking for user with Clerk ID:', userId)
    let { data: user, error: userError } = await supabase
      .from('users')
      .select('id, clerk_id')
      .eq('clerk_id', userId)
      .single()
      
    console.log('🔍 User query result:', { user, userError })

    if (userError || !user) {
      console.log('🔧 User not found in database for Clerk ID:', userId)
      console.log('🔧 Attempting to create user automatically...')
      
      // Try to create user automatically as fallback
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          clerk_id: userId,
          email: '<EMAIL>', // Default for auto-created users
          first_name: 'User',
          last_name: 'Auto'
        })
        .select()
        .single()

      if (createError || !newUser) {
        console.error('❌ Failed to create user:', createError)
        return NextResponse.json(
          { error: 'User account setup failed. Please contact support.' },
          { status: 500 }
        )
      }

      // Create profile for the new user
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          user_id: newUser.id,
          credits_remaining: 10,
          subscription_status: 'free'
        })

      if (profileError) {
        console.error('❌ Failed to create profile:', profileError)
        // Continue anyway - user exists, just profile missing
      }

      console.log('✅ Auto-created user:', newUser.id)
      user = newUser
    } else {
      console.log('✅ Found existing user:', user.id)
    }

    // Cache disabled for real-time updates
    // const privateCacheKey = `analysis:${id}:${user!.id}`
    // const cachedAnalysis = await CacheService.get(privateCacheKey, { prefix: 'private' })

    // if (cachedAnalysis) {
    //   console.log('📊 Serving cached private analysis:', id)
    //   return NextResponse.json(cachedAnalysis)
    // }

    // Check if user owns this private analysis
    if (anyAnalysis.user_id !== user!.id) {
      console.log(`🚫 Access denied: Analysis ${id} belongs to different user`)
      return NextResponse.json(
        { error: 'Access denied. This analysis belongs to another user.' },
        { status: 403 }
      )
    }
    
    // Get the full private analysis data
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select('*')
      .eq('id', anyAnalysis.id)
      .single()

    if (analysisError || !analysis) {
      console.log(`❌ Error fetching full analysis data: ${id}`, analysisError)
      return NextResponse.json(
        { error: 'Failed to load analysis data' },
        { status: 500 }
      )
    }

    return await serveAnalysisData(supabase, analysis, false, user!.id)
  } catch (error) {
    console.error('Error in GET /api/analyses/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/analyses/{id} - Delete analysis and related data
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Await params
    const { id } = await params
    
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Use service role client for user operations (bypasses RLS)
    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, clerk_id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)

    // First check if analysis exists at all
    const { data: anyAnalysis, error: anyError } = await supabase
      .from('ad_analyses')
      .select('id, user_id')
      .eq(isUUID ? 'id' : 'slug', id)
      .single()

    if (anyError || !anyAnalysis) {
      return NextResponse.json(
        { error: `Analysis not found for ${isUUID ? 'ID' : 'slug'}: ${id}` },
        { status: 404 }
      )
    }
    
    // Verify ownership
    if (anyAnalysis.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Access denied. This analysis belongs to another user.' },
        { status: 403 }
      )
    }
    
    const analysis = anyAnalysis

    // Delete related reports first (due to foreign key constraints)
    const { error: reportsDeleteError } = await supabase
      .from('analysis_reports')
      .delete()
      .eq('analysis_id', analysis.id)

    if (reportsDeleteError) {
      console.error('Error deleting reports:', reportsDeleteError)
      return NextResponse.json(
        { error: 'Failed to delete analysis reports' },
        { status: 500 }
      )
    }

    // Delete the analysis
    const { error: deleteError } = await supabase
      .from('ad_analyses')
      .delete()
      .eq('id', analysis.id)
      .eq('user_id', user.id)

    if (deleteError) {
      console.error('Error deleting analysis:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete analysis' },
        { status: 500 }
      )
    }

    // Cache disabled - no invalidation needed
    // await CacheService.invalidateAnalysis(analysis.id)
    // await CacheService.invalidatePattern(`dashboard:user:${user.id}:analyses:*`)
    // await CacheService.delete(`analysis:${id}:${user.id}`, { prefix: 'private' })
    
    console.log('✅ Successfully deleted analysis:', id)
    return NextResponse.json({ message: 'Analysis deleted successfully' })

  } catch (error) {
    console.error('Error in DELETE /api/analyses/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
