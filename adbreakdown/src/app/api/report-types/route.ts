import { NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET() {
  try {
    const supabase = createServerSupabaseClient()
    
    const { data: reportTypes, error } = await supabase
      .from('report_types')
      .select('name, description, credit_cost')
      .order('name')
    
    if (error) {
      console.error('Error fetching report types:', error)
      return NextResponse.json(
        { error: 'Failed to fetch report types' },
        { status: 500 }
      )
    }
    
    // Transform data to include title (formatted name)
    const transformedReportTypes = reportTypes.map(rt => ({
      name: rt.name,
      title: formatTitle(rt.name),
      description: rt.description || `Generate ${formatTitle(rt.name).toLowerCase()} content`,
      credit_cost: rt.credit_cost || 1
    }))
    
    return NextResponse.json({
      reportTypes: transformedReportTypes
    })
  } catch (error) {
    console.error('Error in report-types API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to format report type names into readable titles
function formatTitle(name: string): string {
  return name
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}