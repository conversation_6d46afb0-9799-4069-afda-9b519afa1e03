import { NextRequest, NextResponse } from 'next/server'
import { createGoogleGenerativeAI } from '@ai-sdk/google'
import { streamText } from 'ai'

export const runtime = 'edge'

// Define a simple type for chat messages for clarity
interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
};

export async function POST(request: NextRequest) {
  try {
    const { messages, analysisData } = await request.json();

    if (!messages || !analysisData) {
      return NextResponse.json(
        { error: 'Messages and analysis data are required' },
        { status: 400 }
      );
    }

    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      console.error('Gemini API key not configured on the server.');
      return NextResponse.json(
        { error: 'Gemini API key not configured' },
        { status: 500 }
      );
    }

    // Create Google provider instance with custom API key
    const google = createGoogleGenerativeAI({
      apiKey: apiKey,
    });

    const systemPrompt = `You are a senior marketing strategist and advertising advisor helping clients optimize their video advertisements. You have access to comprehensive private analysis data for their specific ad campaign, and you can search for current market insights, competitor strategies, and industry best practices.

**Your Role & Personality:**
- Act as a strategic marketing consultant with 15+ years of experience
- Be direct, actionable, and data-driven in your recommendations  
- Focus on ROI, conversion optimization, and competitive positioning
- Provide specific, measurable improvement suggestions
- Reference industry benchmarks and current trends when relevant

**Current Private Analysis Data:**
${JSON.stringify(analysisData, null, 2)}

**Strategic Focus Areas:**
1. **Performance Optimization**: Analyze current creative elements, messaging, and calls-to-action for improvement opportunities
2. **Audience Targeting**: Refine target audience based on content analysis and market research
3. **Competitive Positioning**: Identify gaps in competitor strategies and positioning opportunities
4. **Creative Strategy**: Suggest improvements to visual elements, messaging, and emotional appeal
5. **Campaign Scaling**: Recommend strategies for scaling successful elements across channels
6. **A/B Testing**: Propose specific test variations for optimization

**Instructions:**
- Always ground your strategic recommendations in the provided analysis data
- For market insights, competitor research, or industry trends, use your search capabilities
- Provide specific, actionable recommendations with clear rationale
- Include estimated impact levels (high/medium/low) for your suggestions
- Reference specific metrics, benchmarks, or data points when possible
- Be concise but comprehensive - focus on strategies that drive results

Remember: Your goal is to help transform this ad from good to exceptional through strategic improvements.`;

    // Convert messages to the format expected by streamText
    const conversationMessages = [
      { role: 'system' as const, content: systemPrompt },
      ...messages.map((msg: ChatMessage) => ({
        role: msg.role,
        content: msg.content,
      })),
    ];

    const result = streamText({
      model: google('gemini-2.5-flash'),
      messages: conversationMessages,
      temperature: 0.7,
      maxTokens: 2048,
    });

    return result.toDataStreamResponse();

  } catch (error) {
    console.error('Private analysis chat API error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to process chat message';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}