import { NextRequest } from 'next/server'

export const runtime = 'edge'

// Define a simple type for chat messages for clarity
interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
};

export async function POST(request: NextRequest) {
  try {
    const { messages, adContext } = await request.json();

    if (!messages || !adContext) {
      return new Response(
        JSON.stringify({ error: 'Messages and ad context are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      console.error('Gemini API key not configured on the server.');
      return new Response(
        JSON.stringify({ error: 'Gemini API key not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Build conversation history with system context
    const systemMessage = `You are an AI marketing expert helping users understand and improve their video advertisement campaigns. You have access to a comprehensive analysis of their specific ad, plus you can search the web for broader marketing insights, competitor data, and industry trends.

**Current Ad Analysis Context:**
${JSON.stringify(adContext, null, 2)}

**Instructions:**
- Use the provided ad analysis as your primary context for questions about this specific ad.
- For broader questions about marketing, competitors, or industry trends, use your search capabilities to find current information.
- Provide actionable, expert-level marketing advice.
- Be conversational and helpful.
- When searching, focus on recent marketing trends, competitor strategies, and industry best practices.`;

    // Combine system message with conversation history
    const conversationHistory = [
      { role: "user", parts: [{ text: systemMessage }] },
      { role: "model", parts: [{ text: "I understand. I'm ready to help you with marketing insights about your ad campaign. I have access to your detailed analysis and can search for additional marketing intelligence when needed. What would you like to know?" }] }
    ];

    // Add user messages to conversation history
    messages.forEach((msg: ChatMessage) => {
      if (msg.role === 'user') {
        conversationHistory.push({
          role: "user",
          parts: [{ text: msg.content }]
        });
      } else if (msg.role === 'assistant') {
        conversationHistory.push({
          role: "model",
          parts: [{ text: msg.content }]
        });
      }
    });

    // Get the latest user message for the current request
    const lastUserMessage = messages.filter((msg: ChatMessage) => msg.role === 'user').pop();
    if (!lastUserMessage) {
      return new Response(
        JSON.stringify({ error: 'No user message found' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Prepare the payload with Google Search grounding
    const payload = {
      contents: conversationHistory,
      tools: [
        {
          googleSearchRetrieval: {
            dynamicRetrievalConfig: {
              mode: 'MODE_DYNAMIC',
              dynamicThreshold: 0.7
            }
          }
        }
      ],
      generationConfig: {
        temperature: 0.7,
        topP: 0.95,
        maxOutputTokens: 2048,
      }
    };

    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent?key=${apiKey}`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Gemini API Error:', errorData);
      return new Response(
        JSON.stringify({ error: `API Error: ${errorData.error?.message || response.statusText}` }),
        { status: response.status, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const result = await response.json();
    
    if (!result.candidates || result.candidates.length === 0 ||
        !result.candidates[0].content || !result.candidates[0].content.parts ||
        result.candidates[0].content.parts.length === 0) {
      return new Response(
        JSON.stringify({ error: "Could not get valid response from AI" }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const assistantMessage = result.candidates[0].content.parts[0].text;

    // Return streaming response compatible with the useChat hook
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        // Send the response in the format expected by useChat
        const data = `0:"${assistantMessage.replace(/"/g, '\\"').replace(/\n/g, '\\n')}"\n`;
        controller.enqueue(encoder.encode(data));
        controller.close();
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'X-Vercel-AI-Data-Stream': 'v1'
      }
    });

  } catch (error) {
    console.error('Chat API error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to process chat message';
    return new Response(
      JSON.stringify({ error: errorMessage }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
