import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';
import { NextRequest, NextResponse } from 'next/server';

// Type definitions for better type safety
interface Message {
  role: 'user' | 'assistant';
  content: string;
  id?: string;
  timestamp?: Date;
}

interface ChatRequest {
  messages: Message[];
  context?: string[];
}

// Environment variable validation
function validateEnvironmentVariables(): { isValid: boolean; error?: string } {
  if (!process.env.GEMINI_API_KEY) {
    return { isValid: false, error: 'GEMINI_API_KEY is not configured' };
  }
  
  if (process.env.GEMINI_API_KEY.length < 10) {
    return { isValid: false, error: 'GEMINI_API_KEY appears to be invalid' };
  }
  
  return { isValid: true };
}

// Input validation functions
function validateMessages(messages: any): messages is Message[] {
  if (!Array.isArray(messages)) {
    return false;
  }
  
  if (messages.length === 0) {
    return false;
  }
  
  return messages.every(msg => 
    msg && 
    typeof msg === 'object' &&
    typeof msg.role === 'string' &&
    (msg.role === 'user' || msg.role === 'assistant') &&
    typeof msg.content === 'string' &&
    msg.content.trim().length > 0
  );
}

function validateContext(context: any): context is string[] {
  if (context === undefined || context === null) {
    return true; // Context is optional
  }
  
  if (!Array.isArray(context)) {
    return false;
  }
  
  return context.every(item => typeof item === 'string');
}

function sanitizeInput(input: string): string {
  return input.trim().substring(0, 10000); // Limit input length
}

// Initialize Gemini AI with validation
let genAI: GoogleGenerativeAI;
try {
  const envCheck = validateEnvironmentVariables();
  if (!envCheck.isValid) {
    throw new Error(envCheck.error);
  }
  genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
} catch (error) {
  console.error('Failed to initialize Gemini AI:', error);
}

export async function POST(request: NextRequest) {
  try {
    // Check if Gemini AI is properly initialized
    if (!genAI) {
      console.error('Gemini AI not initialized - check environment variables');
      return NextResponse.json(
        { error: 'Service configuration error', code: 'CONFIG_ERROR' },
        { status: 500 }
      );
    }

    // Parse and validate request body
    let body: any;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body', code: 'INVALID_JSON' },
        { status: 400 }
      );
    }

    const { messages, context }: ChatRequest = body;

    // Validate required fields
    if (!messages) {
      return NextResponse.json(
        { error: 'Messages field is required', code: 'MISSING_MESSAGES' },
        { status: 400 }
      );
    }

    // Validate messages array
    if (!validateMessages(messages)) {
      return NextResponse.json(
        { error: 'Invalid messages format. Expected array of messages with role and content', code: 'INVALID_MESSAGES' },
        { status: 400 }
      );
    }

    // Validate context array if provided
    if (!validateContext(context)) {
      return NextResponse.json(
        { error: 'Invalid context format. Expected array of strings', code: 'INVALID_CONTEXT' },
        { status: 400 }
      );
    }

    // Check message limits
    if (messages.length > 100) {
      return NextResponse.json(
        { error: 'Too many messages. Maximum 100 messages allowed', code: 'TOO_MANY_MESSAGES' },
        { status: 400 }
      );
    }

    // Get and validate the latest message
    const latestMessage = messages[messages.length - 1];
    if (latestMessage.role !== 'user') {
      return NextResponse.json(
        { error: 'Last message must be from user', code: 'INVALID_LAST_MESSAGE' },
        { status: 400 }
      );
    }

    const sanitizedContent = sanitizeInput(latestMessage.content);
    if (sanitizedContent.length === 0) {
      return NextResponse.json(
        { error: 'Message content cannot be empty', code: 'EMPTY_MESSAGE' },
        { status: 400 }
      );
    }

    // Initialize model with proper error handling
    let model;
    try {
      model = genAI.getGenerativeModel({ 
        model: 'gemini-1.5-flash',
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        },
        tools: [{ googleSearchRetrieval: {} }], // Enable Google Search grounding
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ],
      });
    } catch (error) {
      console.error('Failed to create model:', error);
      return NextResponse.json(
        { error: 'Failed to initialize AI model', code: 'MODEL_INIT_ERROR' },
        { status: 500 }
      );
    }

    // System prompt for campaign creation assistant
    let systemPrompt = `You are an expert campaign creation assistant specializing in video advertising and marketing. Your role is to help users create compelling campaign briefs and scripts.

Key responsibilities:
1. Help develop comprehensive campaign briefs including objectives, target audience, key messages, and success metrics
2. Create engaging video ad scripts with clear hooks, value propositions, and calls-to-action
3. Provide strategic recommendations based on marketing best practices
4. Ask clarifying questions to better understand the brand, product, and goals
5. Structure outputs in clear, actionable formats

When creating campaign briefs, include:
- Campaign objective and goals
- Target audience demographics and psychographics  
- Key messages and value propositions
- Creative direction and tone
- Success metrics and KPIs
- Timeline and budget considerations

When writing scripts, include:
- Hook (first 3-5 seconds)
- Problem/need identification
- Solution presentation
- Benefits and proof points
- Call-to-action
- Estimated duration and visual cues

Always ask follow-up questions to gather more context when needed.`;

    // Add validated context if provided
    if (context && context.length > 0) {
      const sanitizedContext = context
        .filter(item => typeof item === 'string' && item.trim().length > 0)
        .map(item => sanitizeInput(item))
        .slice(0, 10); // Limit context items

      if (sanitizedContext.length > 0) {
        systemPrompt += `\n\nIMPORTANT CONTEXT FOR THIS CONVERSATION:
${sanitizedContext.join('\n')}

Please use this context information in all your responses and recommendations. If brand context is provided, ensure your suggestions align with the brand's identity and values. If format context is provided, tailor your recommendations to the specific format requirements and constraints.`;
      }
    }

    // Prepare conversation history with type safety
    const conversationHistory = messages
      .slice(0, -1) // Exclude the last message
      .filter((msg): msg is Message => validateMessages([msg]))
      .map((msg: Message) => ({
        role: msg.role === 'user' ? 'user' as const : 'model' as const,
        parts: [{ text: sanitizeInput(msg.content) }]
      }));

    // Start conversation with system prompt
    let chat;
    try {
      chat = model.startChat({
        history: [
          {
            role: 'user',
            parts: [{ text: systemPrompt }]
          },
          {
            role: 'model',
            parts: [{ text: context && context.length > 0 
              ? 'I understand. I have your brand and format context, and I\'m ready to help you create compelling campaign briefs and video ad scripts tailored to your specific needs. What campaign are you working on?' 
              : 'I understand. I\'m ready to help you create compelling campaign briefs and video ad scripts. What campaign are you working on?' }]
          },
          ...conversationHistory
        ]
      });
    } catch (error) {
      console.error('Failed to start chat:', error);
      return NextResponse.json(
        { error: 'Failed to initialize conversation', code: 'CHAT_INIT_ERROR' },
        { status: 500 }
      );
    }
    
    // Send message and get streaming response
    let result;
    try {
      result = await chat.sendMessageStream(sanitizedContent);
    } catch (error: any) {
      console.error('Failed to send message:', error);
      
      // Handle specific Gemini API errors
      if (error?.message?.includes('safety')) {
        return NextResponse.json(
          { error: 'Message blocked by safety filters', code: 'SAFETY_FILTER' },
          { status: 400 }
        );
      }
      
      if (error?.message?.includes('quota')) {
        return NextResponse.json(
          { error: 'API quota exceeded', code: 'QUOTA_EXCEEDED' },
          { status: 429 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to generate response', code: 'GENERATION_ERROR' },
        { status: 500 }
      );
    }

    // Create a readable stream with proper error handling
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        
        try {
          for await (const chunk of result.stream) {
            try {
              const text = chunk.text();
              if (text) {
                const data = JSON.stringify({ content: text });
                controller.enqueue(encoder.encode(`data: ${data}\n\n`));
              }
            } catch (chunkError) {
              console.error('Error processing chunk:', chunkError);
              // Continue processing other chunks
            }
          }
          
          // Send end signal
          controller.enqueue(encoder.encode('data: [DONE]\n\n'));
          controller.close();
        } catch (streamError) {
          console.error('Streaming error:', streamError);
          const errorData = JSON.stringify({ error: 'Stream interrupted' });
          controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
          controller.close();
        }
      },
      
      cancel() {
        console.log('Stream cancelled by client');
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Unexpected error in chat API:', error);
    
    // Return specific error information in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        ...(isDevelopment && { details: error instanceof Error ? error.message : 'Unknown error' })
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed', code: 'METHOD_NOT_ALLOWED' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed', code: 'METHOD_NOT_ALLOWED' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed', code: 'METHOD_NOT_ALLOWED' },
    { status: 405 }
  );
}