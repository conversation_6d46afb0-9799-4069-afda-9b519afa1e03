import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'

export async function POST(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    let body: any
    try {
      body = await req.json()
      console.log('📥 Request body received:', body)
    } catch (jsonError) {
      console.log('❌ JSON parsing failed:', jsonError)
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      )
    }
    
    const { website_url } = body

    if (!website_url || !website_url.trim()) {
      console.log('❌ Website URL missing or empty:', website_url)
      return NextResponse.json(
        { error: 'Website URL is required' },
        { status: 400 }
      )
    }

    // Validate and normalize URL format
    let validUrl: string
    try {
      // Add protocol if missing
      let normalizedUrl = website_url.trim()
      if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
        normalizedUrl = 'https://' + normalizedUrl
      }
      
      const url = new URL(normalizedUrl)
      validUrl = url.toString()
      console.log('✅ Valid URL parsed:', validUrl)
    } catch (urlError) {
      console.log('❌ URL validation failed:', urlError, 'for URL:', website_url)
      return NextResponse.json(
        { error: 'Invalid URL format. Please enter a valid website URL (e.g., example.com or https://example.com)' },
        { status: 400 }
      )
    }

    console.log('🤖 Starting AI brand extraction for:', validUrl)

    // Check if Gemini API key is available
    if (!process.env.GEMINI_API_KEY) {
      console.error('❌ GEMINI_API_KEY environment variable is missing')
      return NextResponse.json(
        { error: 'AI service configuration error' },
        { status: 503 }
      )
    }

    // Create the AI prompt for brand analysis
    const prompt = `You are an expert brand strategist and market research analyst. Your task is to create a comprehensive brand profile for the company associated with the website: ${validUrl}

First, thoroughly browse the provided website to understand the brand's products, services, messaging, and visual identity. Then, perform additional web searches to gather public information about the company's history, industry, and market perception.

Based on your research, generate a brand profile by filling out the following fields. Structure your entire response as a single JSON object. For fields that require an array, use a JSON array. For fields that require a JSONB object, create a nested JSON object. If you cannot find specific information for a field after a thorough search, use null as the value.

JSON Object Structure:

{
  "brand_name": "string",
  "slug": "string (lowercase, hyphenated version of brand_name)",
  "tagline": "string",
  "logo_url": "string (direct URL to the primary logo image)",
  "website_url": "${validUrl}",
  "industry_category": "string (e.g., 'Fintech', 'E-commerce', 'SaaS')",
  "company_size": "string (e.g., '1-10 employees', '11-50 employees', '501-1000 employees')",
  "founded_year": "integer",
  "positioning_statement": "string (A concise statement identifying the target audience, market, brand promise, and evidence of that promise)",
  "brand_values": ["string", "string", "..."],
  "mission_statement": "string",
  "vision_statement": "string",
  "primary_colors": ["HEX code", "HEX code", "..."],
  "secondary_colors": ["HEX code", "HEX code", "..."],
  "font_primary": "string (Name of the primary font)",
  "font_secondary": "string (Name of the secondary font, if any)",
  "voice_attributes": ["string (e.g., 'Authoritative', 'Friendly', 'Witty')", "string", "..."],
  "tone_keywords": ["string (e.g., 'Direct', 'Inspirational', 'Playful')", "string", "..."],
  "brand_personality": ["string (e.g., 'The Sage', 'The Jester', 'The Ruler')", "string", "..."],
  "communication_style": "string (e.g., 'Formal and professional', 'Casual and conversational')",
  "primary_demographics": {
    "age_range": "e.g., 25-40",
    "gender": "e.g., 'Any', 'Male', 'Female'",
    "income_level": "e.g., 'Middle to High', '$75,000+'",
    "education_level": "e.g., 'Bachelor's Degree', 'High School'",
    "location": "e.g., 'Urban India', 'Global'",
    "occupation": "e.g., 'Professional', 'Student'",
    "family_status": "e.g., 'Single', 'Married with children'"
  },
  "psychographics": ["string (e.g., 'Tech-savvy early adopters', 'Value security and convenience', 'Seek premium experiences')", "string", "..."],
  "direct_competitors": ["string (Brand name)", "string", "..."],
  "indirect_competitors": ["string (Brand name)", "string", "..."],
  "competitive_advantages": ["string (e.g., 'Innovative product features', 'Superior customer service', 'Strong brand reputation')", "string", "..."]
}

Please begin your analysis now.`

    // Use Gemini API to analyze the website and extract brand information
    const geminiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent?key=${process.env.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 4096,
        }
      })
    })

    if (!geminiResponse.ok) {
      console.error('❌ Gemini API error:', geminiResponse.status, geminiResponse.statusText)
      return NextResponse.json(
        { error: 'AI analysis service temporarily unavailable' },
        { status: 503 }
      )
    }

    const geminiData = await geminiResponse.json()
    console.log('🤖 Gemini response received')

    // Extract the JSON response from Gemini's text
    const aiText = geminiData.candidates[0].content.parts[0].text
    console.log('🤖 AI Response:', aiText.substring(0, 500) + '...')

    // Try to parse the JSON response
    let brandProfile: any
    try {
      // Find JSON in the response (sometimes Gemini wraps it in markdown)
      const jsonMatch = aiText.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response')
      }
      
      brandProfile = JSON.parse(jsonMatch[0])
      console.log('✅ Successfully parsed AI response')
    } catch (parseError) {
      console.error('❌ Error parsing AI response:', parseError)
      console.error('Full AI text:', aiText)
      return NextResponse.json(
        { error: 'Failed to parse AI analysis results' },
        { status: 500 }
      )
    }

    // Validate and clean the extracted data
    const cleanedProfile = {
      brand_name: brandProfile.brand_name || '',
      tagline: brandProfile.tagline || '',
      logo_url: brandProfile.logo_url || '',
      website_url: validUrl,
      industry_category: brandProfile.industry_category || '',
      company_size: brandProfile.company_size || '',
      founded_year: brandProfile.founded_year || null,
      positioning_statement: brandProfile.positioning_statement || '',
      brand_values: Array.isArray(brandProfile.brand_values) ? brandProfile.brand_values : [],
      mission_statement: brandProfile.mission_statement || '',
      vision_statement: brandProfile.vision_statement || '',
      primary_colors: Array.isArray(brandProfile.primary_colors) ? brandProfile.primary_colors : [],
      secondary_colors: Array.isArray(brandProfile.secondary_colors) ? brandProfile.secondary_colors : [],
      font_primary: brandProfile.font_primary || '',
      font_secondary: brandProfile.font_secondary || '',
      voice_attributes: Array.isArray(brandProfile.voice_attributes) ? brandProfile.voice_attributes : [],
      tone_keywords: Array.isArray(brandProfile.tone_keywords) ? brandProfile.tone_keywords : [],
      brand_personality: Array.isArray(brandProfile.brand_personality) ? brandProfile.brand_personality : [],
      communication_style: brandProfile.communication_style || '',
      primary_demographics: brandProfile.primary_demographics || {
        age_range: '',
        gender: '',
        income_level: '',
        education_level: '',
        location: '',
        occupation: '',
        family_status: ''
      },
      secondary_demographics: {
        age_range: '',
        gender: '',
        income_level: '',
        education_level: '',
        location: '',
        occupation: '',
        family_status: ''
      },
      psychographics: Array.isArray(brandProfile.psychographics) ? brandProfile.psychographics : [],
      customer_personas: [], // AI doesn't generate detailed personas, leave empty for user input
      direct_competitors: Array.isArray(brandProfile.direct_competitors) ? brandProfile.direct_competitors : [],
      indirect_competitors: Array.isArray(brandProfile.indirect_competitors) ? brandProfile.indirect_competitors : [],
      competitive_advantages: Array.isArray(brandProfile.competitive_advantages) ? brandProfile.competitive_advantages : []
    }

    console.log('✅ Brand profile extracted successfully for:', cleanedProfile.brand_name)

    return NextResponse.json({
      success: true,
      brandProfile: cleanedProfile,
      message: 'Brand profile extracted successfully'
    })

  } catch (error) {
    console.error('❌ Error in AI brand extraction:', error)
    return NextResponse.json(
      { error: 'Failed to extract brand profile' },
      { status: 500 }
    )
  }
}