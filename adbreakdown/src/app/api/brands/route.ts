import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// GET /api/brands - List user's brand profiles
export async function GET(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    console.log('🔍 Looking for user with Clerk ID:', userId)
    let { data: user, error: userError } = await supabase
      .from('users')
      .select('id, clerk_id')
      .eq('clerk_id', userId)
      .single()
      
    console.log('🔍 User query result:', { user, userError })

    if (userError || !user) {
      console.log('🔧 User not found in database, creating user for Clerk ID:', userId)
      
      // Try to create user automatically
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          clerk_id: userId,
          email: '<EMAIL>',
          first_name: 'User',
          last_name: 'Auto'
        })
        .select()
        .single()

      if (createError) {
        console.error('❌ Error creating user:', createError)
        return NextResponse.json(
          { error: 'Failed to create user account' },
          { status: 500 }
        )
      }

      user = newUser
      console.log('✅ Created new user:', user)
    }

    // Ensure user exists after creation/fetch
    if (!user) {
      return NextResponse.json(
        { error: 'Failed to get or create user account' },
        { status: 500 }
      )
    }

    // Get user's brand profiles
    const { data: brands, error: brandsError } = await supabase
      .from('brand_profiles')
      .select(`
        id,
        brand_name,
        slug,
        tagline,
        logo_url,
        website_url,
        industry_category,
        completion_percentage,
        creation_method,
        created_at,
        updated_at,
        last_analysis_date
      `)
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false })

    if (brandsError) {
      console.error('❌ Error fetching brand profiles:', {
        error: brandsError,
        message: brandsError.message,
        code: brandsError.code,
        details: brandsError.details,
        hint: brandsError.hint
      })
      
      // Check if table doesn't exist
      if (brandsError.code === '42P01') {
        return NextResponse.json(
          { 
            error: 'Brand profiles table not found. Please run database migrations first.',
            details: 'Run the brand profile migrations (V20, V21, V22) in your database.'
          },
          { status: 500 }
        )
      }
      
      return NextResponse.json(
        { 
          error: 'Failed to fetch brand profiles',
          details: brandsError.message 
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      brands: brands || [],
      total: brands?.length || 0
    })

  } catch (error) {
    console.error('❌ Error in brand profiles fetch:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/brands - Create new brand profile
export async function POST(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    console.log('🔍 Looking for user with Clerk ID:', userId)
    let { data: user, error: userError } = await supabase
      .from('users')
      .select('id, clerk_id')
      .eq('clerk_id', userId)
      .single()
      
    console.log('🔍 User query result:', { user, userError })

    if (userError || !user) {
      console.log('🔧 User not found in database, creating user for Clerk ID:', userId)
      
      // Try to create user automatically
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          clerk_id: userId,
          email: '<EMAIL>',
          first_name: 'User',
          last_name: 'Auto'
        })
        .select()
        .single()

      if (createError) {
        console.error('❌ Error creating user:', createError)
        return NextResponse.json(
          { error: 'Failed to create user account' },
          { status: 500 }
        )
      }

      user = newUser
      console.log('✅ Created new user:', user)
    }

    // Ensure user exists after creation/fetch
    if (!user) {
      return NextResponse.json(
        { error: 'Failed to get or create user account' },
        { status: 500 }
      )
    }

    // Parse request body
    const body = await req.json()
    const {
      brand_name,
      tagline,
      logo_url,
      website_url,
      industry_category,
      company_size,
      founded_year,
      positioning_statement,
      brand_values,
      mission_statement,
      vision_statement,
      primary_colors,
      secondary_colors,
      font_primary,
      font_secondary,
      voice_attributes,
      tone_keywords,
      brand_personality,
      communication_style,
      primary_demographics,
      secondary_demographics,
      psychographics,
      customer_personas,
      direct_competitors,
      indirect_competitors,
      competitive_advantages,
      creation_method = 'manual'
    } = body

    // Validate required fields
    if (!brand_name || !industry_category) {
      return NextResponse.json(
        { error: 'Brand name and industry category are required' },
        { status: 400 }
      )
    }

    // Generate unique slug
    const baseSlug = brand_name.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/--+/g, '-')
      .trim()

    let slug = baseSlug
    let counter = 0
    
    // Ensure slug uniqueness for this user
    while (true) {
      const { data: existingBrand } = await supabase
        .from('brand_profiles')
        .select('id')
        .eq('user_id', user.id)
        .eq('slug', slug)
        .single()

      if (!existingBrand) break
      
      counter++
      slug = `${baseSlug}-${counter}`
    }

    // Calculate completion percentage
    const totalFields = 25 // Total number of brand profile fields
    let completedFields = 2 // brand_name and industry_category are required

    const fieldsToCheck = [
      tagline, logo_url, website_url, company_size, founded_year,
      positioning_statement, mission_statement, vision_statement,
      font_primary, font_secondary, communication_style
    ]
    
    const arrayFieldsToCheck = [
      brand_values, primary_colors, secondary_colors, voice_attributes,
      tone_keywords, brand_personality, psychographics, direct_competitors,
      indirect_competitors, competitive_advantages
    ]

    const jsonFieldsToCheck = [primary_demographics, secondary_demographics, customer_personas]

    fieldsToCheck.forEach(field => {
      if (field && typeof field === 'string' && field.trim() !== '') completedFields++
    })

    arrayFieldsToCheck.forEach(field => {
      if (field && Array.isArray(field) && field.length > 0) completedFields++
    })

    jsonFieldsToCheck.forEach(field => {
      if (field && typeof field === 'object' && Object.keys(field).length > 0) completedFields++
    })

    const completion_percentage = Math.round((completedFields / totalFields) * 100)

    // Create brand profile
    const { data: brand, error: insertError } = await supabase
      .from('brand_profiles')
      .insert({
        user_id: user.id,
        brand_name: brand_name.trim(),
        slug,
        tagline: tagline?.trim() || null,
        logo_url: logo_url?.trim() || null,
        website_url: website_url?.trim() || null,
        industry_category: industry_category.trim(),
        company_size: company_size?.trim() || null,
        founded_year: founded_year || null,
        positioning_statement: positioning_statement?.trim() || null,
        brand_values: brand_values || null,
        mission_statement: mission_statement?.trim() || null,
        vision_statement: vision_statement?.trim() || null,
        primary_colors: primary_colors || null,
        secondary_colors: secondary_colors || null,
        font_primary: font_primary?.trim() || null,
        font_secondary: font_secondary?.trim() || null,
        voice_attributes: voice_attributes || null,
        tone_keywords: tone_keywords || null,
        brand_personality: brand_personality || null,
        communication_style: communication_style?.trim() || null,
        primary_demographics: primary_demographics || null,
        secondary_demographics: secondary_demographics || null,
        psychographics: psychographics || null,
        customer_personas: customer_personas || null,
        direct_competitors: direct_competitors || null,
        indirect_competitors: indirect_competitors || null,
        competitive_advantages: competitive_advantages || null,
        creation_method,
        completion_percentage
      })
      .select()
      .single()

    if (insertError) {
      console.error('❌ Error creating brand profile:', {
        error: insertError,
        message: insertError.message,
        code: insertError.code,
        details: insertError.details,
        hint: insertError.hint
      })
      
      // Check if table doesn't exist
      if (insertError.code === '42P01') {
        return NextResponse.json(
          { 
            error: 'Brand profiles table not found. Please run database migrations first.',
            details: 'Run the brand profile migrations (V20, V21, V22) in your database.'
          },
          { status: 500 }
        )
      }
      
      if (insertError.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { error: 'A brand with this name already exists' },
          { status: 409 }
        )
      }
      
      return NextResponse.json(
        { 
          error: 'Failed to create brand profile',
          details: insertError.message 
        },
        { status: 500 }
      )
    }

    console.log('✅ Created brand profile:', brand.id)

    return NextResponse.json({
      success: true,
      brand: {
        id: brand.id,
        brand_name: brand.brand_name,
        slug: brand.slug,
        completion_percentage: brand.completion_percentage
      },
      message: 'Brand profile created successfully'
    })

  } catch (error) {
    console.error('❌ Error in brand profile creation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}