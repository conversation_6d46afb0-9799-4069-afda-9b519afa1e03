import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// GET /api/brands/[brandId] - Get specific brand profile
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ brandId: string }> }
) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { brandId } = await context.params
    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get brand profile with all fields
    const { data: brand, error: brandError } = await supabase
      .from('brand_profiles')
      .select('*')
      .eq('id', brandId)
      .eq('user_id', user.id)
      .single()

    if (brandError || !brand) {
      return NextResponse.json(
        { error: 'Brand profile not found' },
        { status: 404 }
      )
    }

    // Get brand assets
    const { data: assets, error: assetsError } = await supabase
      .from('brand_assets')
      .select('*')
      .eq('brand_profile_id', brandId)
      .order('created_at', { ascending: false })

    if (assetsError) {
      console.warn('⚠️ Error fetching brand assets:', assetsError)
    }

    return NextResponse.json({
      brand: {
        ...brand,
        assets: assets || []
      }
    })

  } catch (error) {
    console.error('❌ Error in brand profile fetch:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/brands/[brandId] - Update brand profile
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ brandId: string }> }
) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { brandId } = await context.params
    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Verify brand ownership
    const { data: existingBrand, error: verifyError } = await supabase
      .from('brand_profiles')
      .select('id, brand_name, slug')
      .eq('id', brandId)
      .eq('user_id', user.id)
      .single()

    if (verifyError || !existingBrand) {
      return NextResponse.json(
        { error: 'Brand profile not found' },
        { status: 404 }
      )
    }

    // Parse request body
    const body = await req.json()
    const {
      brand_name,
      tagline,
      logo_url,
      website_url,
      industry_category,
      company_size,
      founded_year,
      positioning_statement,
      brand_values,
      mission_statement,
      vision_statement,
      primary_colors,
      secondary_colors,
      font_primary,
      font_secondary,
      voice_attributes,
      tone_keywords,
      brand_personality,
      communication_style,
      primary_demographics,
      secondary_demographics,
      psychographics,
      customer_personas,
      direct_competitors,
      indirect_competitors,
      competitive_advantages
    } = body

    // Validate required fields
    if (!brand_name || !industry_category) {
      return NextResponse.json(
        { error: 'Brand name and industry category are required' },
        { status: 400 }
      )
    }

    // Generate new slug if brand name changed
    let slug = existingBrand.slug
    if (brand_name.trim() !== existingBrand.brand_name) {
      const baseSlug = brand_name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/--+/g, '-')
        .trim()

      slug = baseSlug
      let counter = 0
      
      // Ensure slug uniqueness for this user (excluding current brand)
      while (true) {
        const { data: conflictingBrand } = await supabase
          .from('brand_profiles')
          .select('id')
          .eq('user_id', user.id)
          .eq('slug', slug)
          .neq('id', brandId)
          .single()

        if (!conflictingBrand) break
        
        counter++
        slug = `${baseSlug}-${counter}`
      }
    }

    // Calculate completion percentage
    const totalFields = 25
    let completedFields = 2 // brand_name and industry_category are required

    const fieldsToCheck = [
      tagline, logo_url, website_url, company_size, founded_year,
      positioning_statement, mission_statement, vision_statement,
      font_primary, font_secondary, communication_style
    ]
    
    const arrayFieldsToCheck = [
      brand_values, primary_colors, secondary_colors, voice_attributes,
      tone_keywords, brand_personality, psychographics, direct_competitors,
      indirect_competitors, competitive_advantages
    ]

    const jsonFieldsToCheck = [primary_demographics, secondary_demographics, customer_personas]

    fieldsToCheck.forEach(field => {
      if (field && field.trim() !== '') completedFields++
    })

    arrayFieldsToCheck.forEach(field => {
      if (field && Array.isArray(field) && field.length > 0) completedFields++
    })

    jsonFieldsToCheck.forEach(field => {
      if (field && typeof field === 'object' && Object.keys(field).length > 0) completedFields++
    })

    const completion_percentage = Math.round((completedFields / totalFields) * 100)

    // Update brand profile
    const { data: updatedBrand, error: updateError } = await supabase
      .from('brand_profiles')
      .update({
        brand_name: brand_name.trim(),
        slug,
        tagline: tagline?.trim() || null,
        logo_url: logo_url?.trim() || null,
        website_url: website_url?.trim() || null,
        industry_category: industry_category.trim(),
        company_size: company_size?.trim() || null,
        founded_year: founded_year || null,
        positioning_statement: positioning_statement?.trim() || null,
        brand_values: brand_values || null,
        mission_statement: mission_statement?.trim() || null,
        vision_statement: vision_statement?.trim() || null,
        primary_colors: primary_colors || null,
        secondary_colors: secondary_colors || null,
        font_primary: font_primary?.trim() || null,
        font_secondary: font_secondary?.trim() || null,
        voice_attributes: voice_attributes || null,
        tone_keywords: tone_keywords || null,
        brand_personality: brand_personality || null,
        communication_style: communication_style?.trim() || null,
        primary_demographics: primary_demographics || null,
        secondary_demographics: secondary_demographics || null,
        psychographics: psychographics || null,
        customer_personas: customer_personas || null,
        direct_competitors: direct_competitors || null,
        indirect_competitors: indirect_competitors || null,
        competitive_advantages: competitive_advantages || null,
        completion_percentage,
        updated_at: new Date().toISOString()
      })
      .eq('id', brandId)
      .select()
      .single()

    if (updateError) {
      console.error('❌ Error updating brand profile:', updateError)
      
      if (updateError.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { error: 'A brand with this name already exists' },
          { status: 409 }
        )
      }
      
      return NextResponse.json(
        { error: 'Failed to update brand profile' },
        { status: 500 }
      )
    }

    console.log('✅ Updated brand profile:', brandId)

    return NextResponse.json({
      success: true,
      brand: {
        id: updatedBrand.id,
        brand_name: updatedBrand.brand_name,
        slug: updatedBrand.slug,
        completion_percentage: updatedBrand.completion_percentage
      },
      message: 'Brand profile updated successfully'
    })

  } catch (error) {
    console.error('❌ Error in brand profile update:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/brands/[brandId] - Delete brand profile
export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ brandId: string }> }
) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { brandId } = await context.params
    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Verify brand ownership
    const { data: existingBrand, error: verifyError } = await supabase
      .from('brand_profiles')
      .select('id, brand_name')
      .eq('id', brandId)
      .eq('user_id', user.id)
      .single()

    if (verifyError || !existingBrand) {
      return NextResponse.json(
        { error: 'Brand profile not found' },
        { status: 404 }
      )
    }

    // Delete brand profile (CASCADE will handle related records)
    const { error: deleteError } = await supabase
      .from('brand_profiles')
      .delete()
      .eq('id', brandId)

    if (deleteError) {
      console.error('❌ Error deleting brand profile:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete brand profile' },
        { status: 500 }
      )
    }

    console.log('✅ Deleted brand profile:', brandId)

    return NextResponse.json({
      success: true,
      message: 'Brand profile deleted successfully'
    })

  } catch (error) {
    console.error('❌ Error in brand profile deletion:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}